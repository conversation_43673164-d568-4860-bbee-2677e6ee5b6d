# ===================================================================
# Spring Boot configuration.
#
# This configuration is used for unit/integration tests.
#
# More information on profiles: https://www.jhipster.tech/profiles/
# More information on configuration properties: https://www.jhipster.tech/common-application-properties/
# ===================================================================

# ===================================================================
# Standard Spring Boot properties.
# Full reference is available at:
# http://docs.spring.io/spring-boot/docs/current/reference/html/common-application-properties.html
# ===================================================================

eureka:
    client:
        enabled: false
    instance:
        appname: bam
        instanceId: bam:${spring.application.instance-id:${random.value}}

spring:
    application:
        name: bam
    cache:
        type: simple
    datasource:
        type: com.zaxxer.hikari.HikariDataSource
        url: jdbc:h2:mem:bam;DB_CLOSE_DELAY=-1;DB_CLOSE_ON_EXIT=FALSE
        name:
        username:
        password:
    jpa:
        database-platform: io.github.jhipster.domain.util.FixedH2Dialect
        database: H2
        open-in-view: false
        show-sql: false
        hibernate:
            ddl-auto: none
            naming:
                physical-strategy: org.springframework.boot.orm.jpa.hibernate.SpringPhysicalNamingStrategy
                implicit-strategy: org.springframework.boot.orm.jpa.hibernate.SpringImplicitNamingStrategy
        properties:
            hibernate.id.new_generator_mappings: true
            hibernate.cache.use_second_level_cache: false
            hibernate.cache.use_query_cache: false
            hibernate.generate_statistics: true
            hibernate.hbm2ddl.auto: validate
    liquibase:
        contexts: test
    mail:
        host: localhost
    messages:
        basename: i18n/messages
    mvc:
        favicon:
            enabled: false
    thymeleaf:
        mode: XHTML

    redis:
            cluster:
                nodes: ***********:7000,***********:7001,***********:7000,***********:7001,***********:7000,***********:7001
                max-redirects: 3
    #              nodes: **********:7000,**********:7001,**********:7002,**********:7003,**********:7004,**********:7005
server:
    port: 10344
    address: localhost

info:
    project:
        version: #project.version#

# ===================================================================
# JHipster specific properties
#
# Full reference is available at: https://www.jhipster.tech/common-application-properties/
# ===================================================================

jhipster:
    async:
        core-pool-size: 1
        max-pool-size: 50
        queue-capacity: 10000
    # To test logstash appender
    logging:
        logstash:
            enabled: true
            host: localhost
            port: 5000
            queue-size: 512
    security:
        authentication:
            jwt:
                secret: 7b929447ca31fe29cb06f73d0ffe4469a79dd972
                # Token is valid 24 hours
                token-validity-in-seconds: 86400
        client-authorization:
            access-token-uri: http://uaa/oauth/token
            token-service-id: uaa
            client-id: internal
            client-secret: internal
    metrics: # DropWizard Metrics configuration, used by MetricsConfiguration
        jmx.enabled: true
        logs: # Reports Dropwizard metrics in the logs
            enabled: true
            report-frequency: 60 # in seconds

# ===================================================================
# Application specific properties
# Add your own application properties here, see the ApplicationProperties class
# to have type-safe configuration, like in the JHipsterProperties above
#
# More documentation is available at:
# https://www.jhipster.tech/common-application-properties/
# ===================================================================

# application:
# 消费者的组名
apache:
    rocketmq:
       first:
           namesrvAddr: ***********:9876;***********:9876
       second:
           namesrvaddr: ***********:9876;***********:9876
#       namesrvAddr: **********:9876;**********:9876
       product:
           retryTimesWhenSend: 3
           sendMsgTimeout: 8000
       cosumer:
           maxReconsumeTimes: 3
           consumeThreadMax: 1
           consumeThreadMin: 1
wx-message:
    url: https://mobile-test.gaojihealth.cn/wechat/Bam/bam?appId={replace_appId}&thirdPlatformAppId=wxcb82573536473cc5
    template-id-short: OPENTM411153961
