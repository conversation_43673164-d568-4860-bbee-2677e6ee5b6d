package com.cowell.bam.web.rest.util;

import org.junit.Test;

/**
 * Tests based on parsing algorithm in app/components/util/pagination-util.service.js
 *
 * @see PaginationUtil
 */
public class PaginationUtilUnitTest {

    @Test
    public void generatePaginationHttpHeadersTest() {

        // 判断一个字符串是否都为数字

       String strNum = "36.9";
        String pattern = "^[-\\+]?[\\d]+[.{1}][\\d]+$";
        System.out.println(strNum.matches(pattern));

        ;

//        String baseUrl = "/api/_search/example";
//        List<String> content = new ArrayList<>();
//        Page<String> page = new PageImpl<>(content, PageRequest.of(6, 50), 400L);
//        HttpHeaders headers = PaginationUtil.generatePaginationHttpHeaders(page, baseUrl);
//        List<String> strHeaders = headers.get(HttpHeaders.LINK);
//        assertNotNull(strHeaders);
//        assertTrue(strHeaders.size() == 1);
//        String headerData = strHeaders.get(0);
//        assertTrue(headerData.split(",").length == 4);
//        String expectedData = "</api/_search/example?page=7&size=50>; rel=\"next\","
//                + "</api/_search/example?page=5&size=50>; rel=\"prev\","
//                + "</api/_search/example?page=7&size=50>; rel=\"last\","
//                + "</api/_search/example?page=0&size=50>; rel=\"first\"";
//        assertEquals(expectedData, headerData);
//        List<String> xTotalCountHeaders = headers.get("X-Total-Count");
//        assertTrue(xTotalCountHeaders.size() == 1);
//        assertTrue(Long.valueOf(xTotalCountHeaders.get(0)).equals(400L));
    }

}
