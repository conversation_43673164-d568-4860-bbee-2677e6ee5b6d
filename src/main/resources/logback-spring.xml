<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE configuration>

<configuration scan="true">
    <!-- Do not use the spring provided xml file
    <include resource="org/springframework/boot/logging/logback/base.xml"/>
    -->
    <include resource="org/springframework/boot/logging/logback/defaults.xml"/>
    <include resource="org/springframework/boot/logging/logback/console-appender.xml" />
<!-- The FILE and ASYNC appenders are here as examples for a production configuration -->
<!--
    <appender name="FILE" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <fileNamePattern>logFile.%d{yyyy-MM-dd}.log</fileNamePattern>
            <maxHistory>90</maxHistory>
        </rollingPolicy>
        <encoder>
            <charset>utf-8</charset>
            <Pattern>%d %-5level [%thread] %logger{0}: %msg%n</Pattern>
        </encoder>
    </appender>

    <appender name="ASYNC" class="ch.qos.logback.classic.AsyncAppender">
        <queueSize>512</queueSize>
        <appender-ref ref="FILE"/>
    </appender>

    <root level="${logging.level.root}">
        <appender-ref ref="ASYNC"/>
    </root>
-->

    <property name="log_dir" value="/var/logs/spring-log/"/>
    <springProperty name="app_name" source="spring.application.name"/>
    <springProperty name="app_port" source="server.port"/>
    <springProperty name="version" source="info.project.version"/>

    <appender name="metricsFILE" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
              <maxHistory>1</maxHistory>
            <fileNamePattern>${log_dir}metrics-%d.log</fileNamePattern>
        </rollingPolicy>
        <encoder charset="UTF-8">
            <pattern>{"app_name":"${app_name}","app_port":"${app_port}","version":"${version}","@timestamp": "%d{yyyy-MM-dd'T'HH:mm:ssXXX}","level": "%level","message": "%m","logger_name": "metrics"}\n</pattern>
        </encoder>
    </appender>

    <logger name="metrics" level="INFO" additivity="false">
        <appender-ref ref="metricsFILE" />
    </logger>

    <appender name="FILE" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${log_dir}log.log</file>
        <encoder class="net.logstash.logback.encoder.LogstashEncoder">
            <customFields>
                {
                "app_name":"${app_name}",
                "app_port":"${app_port}",
                "version":"${version}"
                }
            </customFields>
            <enableContextMap>false</enableContextMap>
        </encoder>
        <rollingPolicy class="ch.qos.logback.core.rolling.FixedWindowRollingPolicy">
            <fileNamePattern>${log_dir}/mv_temp/log-%i.log</fileNamePattern>
        </rollingPolicy>
        <triggeringPolicy
            class="ch.qos.logback.core.rolling.SizeBasedTriggeringPolicy">
            <MaxFileSize>100MB</MaxFileSize>
        </triggeringPolicy>
    </appender>

    <appender name="async_file" class="ch.qos.logback.classic.AsyncAppender">
        <discardingThreshold>64</discardingThreshold>
        <queueSize>512</queueSize>
        <appender-ref ref="FILE"/>
    </appender>

    <appender name="async_console" class="ch.qos.logback.classic.AsyncAppender">
        <discardingThreshold>64</discardingThreshold>
        <queueSize>512</queueSize>
        <appender-ref ref="CONSOLE"/>
    </appender>

    <root level="${logging.level.root}">
        <appender-ref ref="async_console"/>
        <appender-ref ref="async_file"/>
    </root>

    <logger name="javax.activation" level="WARN"/>
    <logger name="javax.mail" level="WARN"/>
    <logger name="javax.management.remote" level="WARN"/>
    <logger name="javax.xml.bind" level="WARN"/>
    <logger name="ch.qos.logback" level="WARN"/>
    <logger name="com.codahale.metrics" level="WARN"/>
    <logger name="com.hazelcast" level="INFO"/>
    <logger name="com.netflix" level="WARN"/>
    <logger name="com.netflix.discovery" level="INFO"/>
    <logger name="com.ryantenney" level="WARN"/>
    <logger name="com.sun" level="WARN"/>
    <logger name="com.zaxxer" level="WARN"/>
    <logger name="io.undertow" level="WARN"/>
    <logger name="io.undertow.websockets.jsr" level="ERROR"/>
    <logger name="org.apache" level="WARN"/>
    <logger name="org.apache.catalina.startup.DigesterFactory" level="OFF"/>
    <logger name="org.bson" level="WARN"/>
    <logger name="org.hibernate.validator" level="WARN"/>
    <logger name="org.hibernate" level="WARN"/>
    <logger name="org.hibernate.ejb.HibernatePersistence" level="OFF"/>
    <logger name="org.springframework" level="WARN"/>
    <logger name="org.springframework.web" level="WARN"/>
    <logger name="org.springframework.security" level="WARN"/>
    <logger name="org.springframework.cache" level="WARN"/>
    <logger name="org.thymeleaf" level="WARN"/>
    <logger name="org.xnio" level="WARN"/>
    <logger name="springfox" level="WARN"/>
    <logger name="sun.rmi" level="WARN"/>
<!--    <logger name="liquibase" level="WARN"/>
    <logger name="LiquibaseSchemaResolver" level="INFO"/>-->
    <logger name="sun.net.www" level="INFO"/>
    <logger name="sun.rmi.transport" level="WARN"/>

    <!-- https://logback.qos.ch/manual/configuration.html#shutdownHook and https://jira.qos.ch/browse/LOGBACK-1090 -->
    <shutdownHook class="ch.qos.logback.core.hook.DelayingShutdownHook"/>

    <contextListener class="ch.qos.logback.classic.jul.LevelChangePropagator">
        <resetJUL>true</resetJUL>
    </contextListener>

</configuration>
