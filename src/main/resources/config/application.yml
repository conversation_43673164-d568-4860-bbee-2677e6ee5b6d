# ===================================================================
# Spring Boot configuration.
#
# This configuration will be overridden by the Spring profile you use,
# for example application-dev.yml if you use the "dev" profile.
#
# More information on profiles: https://www.jhipster.tech/profiles/
# More information on configuration properties: https://www.jhipster.tech/common-application-properties/
# ===================================================================

# ===================================================================
# Standard Spring Boot properties.
# Full reference is available at:
# http://docs.spring.io/spring-boot/docs/current/reference/html/common-application-properties.html
# ===================================================================

eureka:
    client:
        enabled: true
        healthcheck:
            enabled: true
        fetch-registry: true
        register-with-eureka: true
        instance-info-replication-interval-seconds: 10
        registry-fetch-interval-seconds: 10
    instance:
        appname: bam
        instanceId: bam:${spring.application.instance-id:${random.value}}
        lease-renewal-interval-in-seconds: 5
        lease-expiration-duration-in-seconds: 10
        status-page-url-path: ${management.context-path}/info
        health-check-url-path: ${management.context-path}/health
        metadata-map:
            zone: primary # This is needed for the load balancer
            profile: ${spring.profiles.active}
            version: ${info.project.version:}
ribbon:
    eureka:
        enabled: true
    ConnectTimeout: 5000
    ReadTimeout: 5000
    MaxTotalConnections: 600
    MaxConnectionsPerHost: 200
feign:
    hystrix:
        enabled: true

hystrix:
    share-security-context: true

management:
    security:
        enabled: true
        roles: ADMIN,ACTUATOR
    context-path: /management
    info:
        git:
            mode: full
    health:
        mail:
            enabled: false # When using the MailService, configure an SMTP server and set this to true
spring:
    application:
        name: bam
    jackson:
        serialization.write_dates_as_timestamps: false
        date-format: yyyy-MM-dd HH:mm:ss
    jpa:
        open-in-view: false
        hibernate:
            ddl-auto: none
            naming:
                physical-strategy: org.springframework.boot.orm.jpa.hibernate.SpringPhysicalNamingStrategy
                implicit-strategy: org.springframework.boot.orm.jpa.hibernate.SpringImplicitNamingStrategy
    messages:
        basename: i18n/messages
    mvc:
        favicon:
            enabled: false
    thymeleaf:
        mode: XHTML
security:
    basic:
        enabled: false
    oauth2:
        resource:
            filter-order: 3

server:
    session:
        cookie:
            http-only: true

info:
    project:
            version: #project.version#

# ===================================================================
# JHipster specific properties
#
# Full reference is available at: https://www.jhipster.tech/common-application-properties/
# ===================================================================

jhipster:
    async:
        core-pool-size: 2
        max-pool-size: 50
        queue-capacity: 10000
    # By default CORS is disabled. Uncomment to enable.
    #cors:
        #allowed-origins: "*"
        #allowed-methods: "*"
        #allowed-headers: "*"
        #exposed-headers: "Authorization,Link,X-Total-Count"
        #allow-credentials: true
        #max-age: 1800
    mail:
        from: bam@localhost
    swagger:
        default-include-pattern: /api/.*
        title: bam API
        description: bam API documentation
        version: 0.0.1
        terms-of-service-url:
        contact-name:
        contact-url:
        contact-email:
        license:
        license-url:
    ribbon:
            display-on-active-profiles: dev

# ===================================================================
# Application specific properties
# Add your own application properties here, see the ApplicationProperties class
# to have type-safe configuration, like in the JHipsterProperties above
#
# More documentation is available at:
# http://www.jhipster.tech/common-application-properties/
# ===================================================================

application:

testframe:
  refresh:
    txt: "test string 3"
