# ===================================================================
# Spring Boot configuration for the "dev" profile.
#
# This configuration overrides the application.yml file.
#
# More information on profiles: http://www.jhipster.tech/profiles/
# More information on configuration properties: http://www.jhipster.tech/common-application-properties/
# ===================================================================

# ===================================================================
# Standard Spring Boot properties.
# Full reference is available at:
# http://docs.spring.io/spring-boot/docs/current/reference/html/common-application-properties.html
# ===================================================================
hystrix:
    shareSecurityContext: true
    command:
        default:
            circuitBreaker:
                sleepWindowInMilliseconds: 8000
            execution:
                isolation:
                    thread:
                        timeoutInMilliseconds: 3000
api:
    version: test
# 消费者的组名
apache:
    rocketmq:
        namesrvAddr: ***********:9876;***********:9876
        saasNamesrvAddr: ***********:9876;***********:9876
        product:
            retryTimesWhenSend: 3
            sendMsgTimeout: 8000
        cosumer:
            maxReconsumeTimes: 3
            consumeThreadMax: 1
            consumeThreadMin: 1
        compare_cosumer:
            topic: ERP_CALLBACK_BAM_SERVICE_TOPIC_DEV
            stockTag: ERP_CALLBACK_STOCK_SERVICE_TAG
            groupName: ERP_CALLBACK_STOCK_COMPARE_SERVICE_CONSUMER_GROUP
        compare_producer:
            topic: compare_topic_dev
            stockTag: stock_tag_dev
            priceTag: price_tag_dev
            groupName: compare_producer_group_devE
        delete_stock_hdcompare-producer:
            topic: DELETE_COMPAREHD_STOCK_SERVICE_TOPIC_dev
            tag: DELETE_COMPAREHD_STOCK_SERVICE_TAG_dev
            group: DELETE_COMPAREHD_STOCK_SERVICE_PRODUCER_GROUP_dev
        recorrect_stock_consumer:
            topic: REGAIN-STOCK-TOPIC-DEV
            tag: REGAIN-STOCK_CONSUMER_TAG_dev
            group: REGAIN-STOCK_CONSUMER_GROUP_dev
        notice_send_stock:
            topic: NOTICE_SEND_STOCK_TOPIC_dev
            tag: NOTICE_SEND_STOCK_TAG_dev
            group: NOTICE_SEND_STOCK_PRODUCER_GROUP_dev
        compare_price_self:
            topic: COMPARE_PRODUCER_ONESELF_TOPIC_dev
            tag: COMPARE_PRODUCER_ONESELF_TAG_dev
            group: COMPARE_PRODUCER_ONESELF_GROUP_dev
        compare_stock_balance:
            topic: COMPARE_STOCK_TOPIC_dev
            tag: COMPARE_STOCK_TAG_dev
            producer-group: COMPARE_STOCK_PRODUCER_GROUP_dev
            consumer-group: COMPARE_STOCK_CONSUMER_GROUP_dev
        compare_transit_stock:
            topic: COMPARE_TRANSIT_STOCK_TOPIC_dev
            tag: COMPARE_TRANSIT_STOCK_TAG_dev
            producer-group: COMPARE_TRANSIT_STOCK_PRODUCER_GROUP_dev
            consumer-group: COMPARE_TRANSIT_STOCK_CONSUMER_GROUP_dev
        email_transit_stock:
            topic: EMAIL_TRANSIT_STOCK_TOPIC_dev
            tag: EMAIL_TRANSIT_STOCK_TAG_dev
            producer-group: EMAIL_TRANSIT_STOCK_PRODUCER_GROUP_dev
            consumer-group: EMAIL_TRANSIT_STOCK_CONSUMER_GROUP_dev
        sync_stock_producer:
            topic: ERP_CALLBACK_STOCK_SERVICE_TOPIC_DEV
            tag: ERP_CALLBACK_STOCK_SERVICE_TAG_DEV
            group: BAM_SYCN_STOCK_PRODUCER_GROUP_DEV
logging:
    level:
        ROOT: INFO
        com.cowell.bam: DEBUG
        io.github.jhipster: DEBUG

eureka:
    instance:
        prefer-ip-address: true
    client:
        service-url:
#            defaultZone: http://admin:${jhipster.registry.password}@registry.cowellhealth.net:8761/eureka/
            defaultZone: http://admin:${jhipster.registry.password}@************:8761/eureka/


spring:
    profiles:
        active: dev
        include: no-liquibase,swagger
    devtools:
        restart:
            enabled: true
        livereload:
            enabled: false # we use gulp + BrowserSync for livereload
    jackson:
        serialization.indent_output: true
    datasource:
        #        type: com.zaxxer.hikari.HikariDataSource
        type: com.alibaba.druid.pool.DruidDataSource
        #        type: com.zaxxer.hikari.HikariDataSource
        driverClassName: com.mysql.jdbc.Driver
        url: ***********************************************************************************************************************************************
        username: scrmrw
        password: 123qaz!@#
        druid:
            # 下面为连接池的补充设置，应用到上面所有数据源中
            # 初始化大小，最小，最大
            initialSize: 5
            minIdle: 5
            maxActive: 20
            # 配置获取连接等待超时的时间
            maxWait: 60000
            # 配置间隔多久才进行一次检测，检测需要关闭的空闲连接，单位是毫秒
            timeBetweenEvictionRunsMillis: 60000
            # 配置一个连接在池中最小生存的时间，单位是毫秒
            minEvictableIdleTimeMillis: 300000
            validationQuery: SELECT 1 FROM DUAL
            validationQueryTimeout: 10000
            testWhileIdle: true
            testOnBorrow: false
            testOnReturn: false
            # 打开PSCache，并且指定每个连接上PSCache的大小
            poolPreparedStatements: true
            maxPoolPreparedStatementPerConnectionSize: 20
            # 配置监控统计拦截的filters，去掉后监控界面sql无法统计，'wall'用于防火墙
            filters: stat,log4j
            filter:
                wall:
                    config:
                        enabled: true
                        multiStatementAllow: true
                        noneBaseStatementAllow: true
            # 通过connectProperties属性来打开mergeSql功能；慢SQL记录 毫秒
            connectionProperties: druid.stat.mergeSql=true;druid.stat.slowSqlMillis=5
            # 合并多个DruidDataSource的监控数据
            #useGlobalDataSourceStat: true
            # WebStatFilter配置，说明请参考Druid Wiki，配置_配置WebStatFilter
            web-stat-filter:
                enabled: true
                url-pattern: /*
                exclusions: "*.js,*.gif,*.jpg,*.bmp,*.png,*.css,*.ico,/druid/*"

            # StatViewServlet配置，说明请参考Druid Wiki，配置_StatViewServlet配置
            stat-view-servlet:
                enabled: true
                url-pattern: /druid/*
                reset-enable: "false"
                allow:
                #IP黑名单 (存在共同时，deny优先于allow)
                deny:
                login-username: admin
                login-password: admin
            # Spring监控AOP切入点，如x.y.z.service.*,配置多个英文逗号分隔
            aop-patterns: com.cowell.bam.service.impl.*
    HanaDatasource:
        driverClassName: com.sap.db.jdbc.Driver
        url: ******************************************
        username: PO_BDP
        password: Bdp_2018

    #        hikari:
    #            data-source-properties:
    #                cachePrepStmts: true
    #                prepStmtCacheSize: 250
    #                prepStmtCacheSqlLimit: 2048
    #                useServerPrepStmts: true
    messages:
        cache-seconds: 1
    thymeleaf:
        cache: false
    zipkin: # Use the "zipkin" Maven profile to have the Spring Cloud Zipkin dependencies
        base-url: http://localhost:9411
        enabled: false
        locator:
            discovery:
                enabled: true

    redis:
        cluster:
            nodes: common_microservice_redis-01_test.cowelltech.com:6379
            max-redirects: 3
            read-mode: MASTER
    #              nodes: **********:7000,**********:7001,**********:7002,**********:7003,**********:7004,**********:7005


    kafka:
        producer:
            bootstrap-servers: "**********:9092,**********:9092,**********:9092"
            acks: 0
            retries: 0
            buffer-memory: 33554432
            key-deserializer: "org.apache.kafka.common.serialization.StringSerializer"
            value-deserializer: "org.apache.kafka.common.serialization.StringSerializer"
        consumer:
            bootstrap-servers: "**********:9092,**********:9092,**********:9092"
            enable-auto-commit: true
            key-deserializer: "org.apache.kafka.common.serialization.StringDeserializer"
            value-deserializer: "org.apache.kafka.common.serialization.StringDeserializer"
        auditlog-consumer:
            bootstrap-servers: "**********:9092,**********:9092,**********:9092"
            group: "AUDIT_PROD"
            topic: "ELK_INGRESS_NGINX_PROD"
    data:
        elasticsearch:
            cluster-name: elk-test
            cluster-node1: 10.8.132.162
            cluster-node2: 10.8.132.162
            cluster-node3: 10.8.132.162
            port: 9200
            x-pack:
                username: elastic
                password: "Gaoji_001#"
    cloud:
        stream:
            bindings:
                sleuth:
                    binder: sleuth-kafka
                    destination: sleuth
                    content-type: application/json
                    producer:
                        headerMode: raw
            binders:
                sleuth-kafka:
                    type: kafka
                    environment:
                        spring:
                            cloud:
                                stream:
                                    kafka:
                                        binder:
                                            brokers: 10.8.183.126:9092,10.8.183.127:9092,10.8.183.128:9092
                                            zkNodes: 10.8.183.126:2181,10.8.183.127:2181,10.8.183.128:2181/kafka-elk
#数据源配置
#数据源配置
oracle:
    druid:
        hd:
            type: com.alibaba.druid.pool.DruidDataSource
            driverClassName: oracle.jdbc.OracleDriver
            url: ****************************************
            username: bam_uaa_readonly
            password: hdtest
            password_encrypted: false
            maxActive: 50
            minIdle: 5
            maxWait: 60000
            poolPreparedStatements : true
            testOnBorrow : false
            testOnReturn : false
            removeAbandoned : false
            maxPoolPreparedStatementPerConnectionSize : 100
            keepAlive : true
            minEvictableIdleTimeMillis : 300000
            validationQuery : "SELECT 'x' FROM DUAL"
            testWhileIdle : false
            filters : "stat"
        xn:
            type: com.alibaba.druid.pool.DruidDataSource
            driverClassName: oracle.jdbc.OracleDriver
            url: ****************************************
            username: bam_uaa_readonly
            password: hdtest
            password_encrypted: false
            maxActive: 50
            minIdle: 5
            maxWait: 60000
            poolPreparedStatements : true
            testOnBorrow : false
            testOnReturn : false
            removeAbandoned : false
            maxPoolPreparedStatementPerConnectionSize : 100
            keepAlive : true
            minEvictableIdleTimeMillis : 300000
            validationQuery : "SELECT 'x' FROM DUAL"
            testWhileIdle : false
            filters : "stat"
        hb:
            type: com.alibaba.druid.pool.DruidDataSource
            driverClassName: oracle.jdbc.OracleDriver
            url: ***************************************
            username: bam_uaa_readonly
            password: hdtest
            password_encrypted: false
            maxActive: 50
            minIdle: 5
            maxWait: 60000
            poolPreparedStatements : true
            testOnBorrow : false
            testOnReturn : false
            removeAbandoned : false
            maxPoolPreparedStatementPerConnectionSize : 100
            keepAlive : true
            minEvictableIdleTimeMillis : 300000
            validationQuery : "SELECT 'x' FROM DUAL"
            testWhileIdle : false
            filters : "stat"
        hz:
            type: com.alibaba.druid.pool.DruidDataSource
            driverClassName: oracle.jdbc.OracleDriver
            url: ********************************************
            username: bam_uaa_readonly
            password: hdtest
            password_encrypted: false
            maxActive: 50
            minIdle: 5
            maxWait: 60000
            poolPreparedStatements : true
            testOnBorrow : false
            testOnReturn : false
            removeAbandoned : false
            maxPoolPreparedStatementPerConnectionSize : 100
            keepAlive : true
            minEvictableIdleTimeMillis : 300000
            validationQuery : "SELECT 'x' FROM DUAL"
            testWhileIdle : false
            filters : "stat"
        xb:
            type: com.alibaba.druid.pool.DruidDataSource
            driverClassName: oracle.jdbc.OracleDriver
            url: ***************************************
            username: bam_uaa_readonly
            password: hdtest
            password_encrypted: false
            maxActive: 20
            minIdle: 5
            maxWait: 60000
            poolPreparedStatements : true
            testOnBorrow : false
            testOnReturn : false
            removeAbandoned : false
            maxPoolPreparedStatementPerConnectionSize : 100
            keepAlive : true
            minEvictableIdleTimeMillis : 300000
            validationQuery : "SELECT 'x' FROM DUAL"
            testWhileIdle : false
            filters : "stat"
        bj:
            type: com.alibaba.druid.pool.DruidDataSource
            driverClassName: oracle.jdbc.OracleDriver
            url: ***************************************
            username: bam_uaa_readonly
            password: hdtest
            password_encrypted: false
            maxActive: 20
            minIdle: 5
            maxWait: 60000
            poolPreparedStatements : true
            testOnBorrow : false
            testOnReturn : false
            removeAbandoned : false
            maxPoolPreparedStatementPerConnectionSize : 100
            keepAlive : true
            minEvictableIdleTimeMillis : 300000
            validationQuery : "SELECT 'x' FROM DUAL"
            testWhileIdle : false
            filters : "stat"
        xszk:
            type: com.alibaba.druid.pool.DruidDataSource
            driverClassName: oracle.jdbc.OracleDriver
            url: ***************************************
            username: bam_uaa_readonly
            password: hdtest
            password_encrypted: false
            maxActive: 20
            minIdle: 5
            maxWait: 60000
            poolPreparedStatements : true
            testOnBorrow : false
            testOnReturn : false
            removeAbandoned : false
            maxPoolPreparedStatementPerConnectionSize : 100
            keepAlive : true
            minEvictableIdleTimeMillis : 300000
            validationQuery : "SELECT 'x' FROM DUAL"
            testWhileIdle : false
            filters : "stat"
        hn:
            type: com.alibaba.druid.pool.DruidDataSource
            driverClassName: oracle.jdbc.OracleDriver
            url: ***************************************
            username: bam_uaa_readonly
            password: hdtest
            password_encrypted: false
            maxActive: 20
            minIdle: 5
            maxWait: 60000
            poolPreparedStatements: true
            testOnBorrow: false
            testOnReturn: false
            removeAbandoned: false
            maxPoolPreparedStatementPerConnectionSize: 100
            keepAlive: true
            minEvictableIdleTimeMillis: 300000
            validationQuery: "SELECT 'x' FROM DUAL"
            testWhileIdle: false
            filters: "stat"

mybatis:
    #pojo路径
    type-aliases-package: com.cowell.bam.domain
    #    type-aliases-package: com.cowell.bam.service.dto
    #mapper路径
    mapper-locations: classpath:mybatis/mapper/*Mapper.xml
    config-location: classpath:mybatis/mybatis-config.xml


liquibase:
    contexts: dev
# ===================================================================
# To enable SSL, generate a certificate using:
# keytool -genkey -alias bam -storetype PKCS12 -keyalg RSA -keysize 2048 -keystore keystore.p12 -validity 3650
#
# You can also use Let's Encrypt:
# https://maximilian-boehm.com/hp2121/Create-a-Java-Keystore-JKS-from-Let-s-Encrypt-Certificates.htm
#
# Then, modify the server.ssl properties so your "server" configuration looks like:
#
# server:
#    port: 8443
#    ssl:
#        key-store: keystore.p12
#        key-store-password: <your-password>
#        key-store-type: PKCS12
#        key-alias: bam
# ===================================================================
server:
    port: 8689

# ===================================================================
# JHipster specific properties
#
# Full reference is available at: http://www.jhipster.tech/common-application-properties/
# ===================================================================

jhipster:
    http:
        version: V_1_1 # To use HTTP/2 you will need SSL support (see above the "server.ssl" configuration)
        # CORS is disabled by default on microservices, as you should access them through a gateway.
        # If you want to enable it, please uncomment the configuration below.
        # cors:
        # allowed-origins: "*"
        # allowed-methods: "*"
        # allowed-headers: "*"
        # exposed-headers: "Authorization,Link,X-Total-Count"
        # allow-credentials: true
        # max-age: 1800
    security:
        client-authorization:
            access-token-uri: http://uaa/oauth/token
            token-service-id: uaa
            client-id: internal
            client-secret: internal
    mail: # specific JHipster mail property, for standard properties see MailProperties
        from: bam@localhost
        base-url: http://127.0.0.1:8689
    metrics: # DropWizard Metrics configuration, used by MetricsConfiguration
        jmx.enabled: true
        graphite: # Use the "graphite" Maven profile to have the Graphite dependencies
            enabled: false
            host: localhost
            port: 2003
            prefix: bam
        prometheus: # Use the "prometheus" Maven profile to have the Prometheus dependencies
            enabled: false
            endpoint: /prometheusMetrics
        logs: # Reports Dropwizard metrics in the logs
            enabled: false
            report-frequency: 60 # in seconds
    logging:
        logstash: # Forward logs to logstash over a socket, used by LoggingConfiguration
            enabled: false
            host: localhost
            port: 5000
            queue-size: 512
        spectator-metrics: # Reports Spectator Circuit Breaker metrics in the logs
            enabled: false
            # edit spring.metrics.export.delay-millis to set report frequency

oauth2:
    signature-verification:
        public-key-endpoint-uri: http://uaa/oauth/token_key
        #ttl for public keys to verify JWT tokens (in ms)
        ttl: 3600000
        #max. rate at which public keys will be fetched (in ms)
        public-key-refresh-rate-limit: 10000
    web-client-configuration:
        #keep in sync with UAA configuration
        client-id: web_app
        secret: changeit
# ===================================================================
# Application specific properties
# Add your own application properties here, see the ApplicationProperties class
# to have type-safe configuration, like in the JHipsterProperties above
#
# More documentation is available at:
# http://www.jhipster.tech/common-application-properties/
# ===================================================================

application:
xxl:
    job:
        admin:
            #addresses: http://xxl-job/xxl-job/
            addresses: http://***********:10169/xxl-job-admin
            removejobpath: /jobinfo/remove
            username: admin
            password: gaoji_001
        executor:
            appname: bamBdp
            ip:
            port: 9999
            logpath: /data/applogs/xxl-job/jobhandler
            logretentiondays: -1
        accessToken:


