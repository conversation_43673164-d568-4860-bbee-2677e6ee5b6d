<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.cowell.bam.repository.mybatis.dao.DiffStockDataCompareInfoMapper" >
  <resultMap id="BaseResultMap" type="com.cowell.bam.domain.DiffStockDataCompareInfo" >
    <id column="id" property="id" jdbcType="BIGINT" />
    <result column="business_id" property="businessId" jdbcType="BIGINT" />
    <result column="store_id" property="storeId" jdbcType="BIGINT" />
    <result column="goods_no" property="goodsNo" jdbcType="VARCHAR" />
    <result column="batch_code" property="batchCode" jdbcType="VARCHAR" />
    <result column="batch_no" property="batchNo" jdbcType="VARCHAR" />
    <result column="data_type" property="dataType" jdbcType="INTEGER" />
    <result column="reason" property="reason" jdbcType="VARCHAR" />
    <result column="status" property="status" jdbcType="INTEGER" />
    <result column="gmt_create" property="gmtCreate" jdbcType="TIMESTAMP" />
    <result column="extend" property="extend" jdbcType="VARCHAR" />
  </resultMap>
  <resultMap id="ResultMapWithBLOBs" type="com.cowell.bam.domain.DiffStockDataCompareInfoWithBLOBs" extends="BaseResultMap" >
    <result column="batch_code_data" property="batchCodeData" jdbcType="LONGVARCHAR" />
    <result column="batch_cost_data" property="batchCostData" jdbcType="LONGVARCHAR" />
  </resultMap>
  <sql id="Example_Where_Clause" >
    <where >
      <foreach collection="oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause" >
    <where >
      <foreach collection="example.oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List" >
    id, business_id, store_id, goods_no, batch_code, batch_no, data_type, reason, status,
    gmt_create, extend
  </sql>
  <sql id="Blob_Column_List" >
    batch_code_data, batch_cost_data
  </sql>
  <select id="selectByExampleWithBLOBs" resultMap="ResultMapWithBLOBs" parameterType="com.cowell.bam.domain.DiffStockDataCompareInfoExample" >
    select
    <if test="distinct" >
      distinct
    </if>
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from diff_stock_data_compare_info
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null" >
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByExample" resultMap="BaseResultMap" parameterType="com.cowell.bam.domain.DiffStockDataCompareInfoExample" >
    select
    <if test="distinct" >
      distinct
    </if>
    <include refid="Base_Column_List" />
    from diff_stock_data_compare_info
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null" >
      order by ${orderByClause}
    </if>
    <if test="limitStart >= 0" >
      limit ${limitStart} , ${pageSize}
    </if>
  </select>
  <select id="selectByPrimaryKey" resultMap="ResultMapWithBLOBs" parameterType="java.lang.Long" >
    select
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from diff_stock_data_compare_info
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long" >
    delete from diff_stock_data_compare_info
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.cowell.bam.domain.DiffStockDataCompareInfoExample" >
    delete from diff_stock_data_compare_info
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.cowell.bam.domain.DiffStockDataCompareInfoWithBLOBs" >
    insert into diff_stock_data_compare_info (id, business_id, store_id,
      goods_no, batch_code, batch_no,
      data_type, reason, status,
      gmt_create, extend, batch_code_data,
      batch_cost_data)
    values (#{id,jdbcType=BIGINT}, #{businessId,jdbcType=BIGINT}, #{storeId,jdbcType=BIGINT},
      #{goodsNo,jdbcType=VARCHAR}, #{batchCode,jdbcType=VARCHAR}, #{batchNo,jdbcType=VARCHAR},
      #{dataType,jdbcType=INTEGER}, #{reason,jdbcType=VARCHAR}, #{status,jdbcType=INTEGER},
      #{gmtCreate,jdbcType=TIMESTAMP}, #{extend,jdbcType=VARCHAR}, #{batchCodeData,jdbcType=LONGVARCHAR},
      #{batchCostData,jdbcType=LONGVARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.cowell.bam.domain.DiffStockDataCompareInfoWithBLOBs" >
    insert into diff_stock_data_compare_info
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        id,
      </if>
      <if test="businessId != null" >
        business_id,
      </if>
      <if test="storeId != null" >
        store_id,
      </if>
      <if test="goodsNo != null" >
        goods_no,
      </if>
      <if test="batchCode != null" >
        batch_code,
      </if>
      <if test="batchNo != null" >
        batch_no,
      </if>
      <if test="dataType != null" >
        data_type,
      </if>
      <if test="reason != null" >
        reason,
      </if>
      <if test="status != null" >
        status,
      </if>
      <if test="gmtCreate != null" >
        gmt_create,
      </if>
      <if test="extend != null" >
        extend,
      </if>
      <if test="batchCodeData != null" >
        batch_code_data,
      </if>
      <if test="batchCostData != null" >
        batch_cost_data,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        #{id,jdbcType=BIGINT},
      </if>
      <if test="businessId != null" >
        #{businessId,jdbcType=BIGINT},
      </if>
      <if test="storeId != null" >
        #{storeId,jdbcType=BIGINT},
      </if>
      <if test="goodsNo != null" >
        #{goodsNo,jdbcType=VARCHAR},
      </if>
      <if test="batchCode != null" >
        #{batchCode,jdbcType=VARCHAR},
      </if>
      <if test="batchNo != null" >
        #{batchNo,jdbcType=VARCHAR},
      </if>
      <if test="dataType != null" >
        #{dataType,jdbcType=INTEGER},
      </if>
      <if test="reason != null" >
        #{reason,jdbcType=VARCHAR},
      </if>
      <if test="status != null" >
        #{status,jdbcType=INTEGER},
      </if>
      <if test="gmtCreate != null" >
        #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="extend != null" >
        #{extend,jdbcType=VARCHAR},
      </if>
      <if test="batchCodeData != null" >
        #{batchCodeData,jdbcType=LONGVARCHAR},
      </if>
      <if test="batchCostData != null" >
        #{batchCostData,jdbcType=LONGVARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.cowell.bam.domain.DiffStockDataCompareInfoExample" resultType="java.lang.Integer" >
    select count(*) from diff_stock_data_compare_info
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map" >
    update diff_stock_data_compare_info
    <set >
      <if test="record.id != null" >
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.businessId != null" >
        business_id = #{record.businessId,jdbcType=BIGINT},
      </if>
      <if test="record.storeId != null" >
        store_id = #{record.storeId,jdbcType=BIGINT},
      </if>
      <if test="record.goodsNo != null" >
        goods_no = #{record.goodsNo,jdbcType=VARCHAR},
      </if>
      <if test="record.batchCode != null" >
        batch_code = #{record.batchCode,jdbcType=VARCHAR},
      </if>
      <if test="record.batchNo != null" >
        batch_no = #{record.batchNo,jdbcType=VARCHAR},
      </if>
      <if test="record.dataType != null" >
        data_type = #{record.dataType,jdbcType=INTEGER},
      </if>
      <if test="record.reason != null" >
        reason = #{record.reason,jdbcType=VARCHAR},
      </if>
      <if test="record.status != null" >
        status = #{record.status,jdbcType=INTEGER},
      </if>
      <if test="record.gmtCreate != null" >
        gmt_create = #{record.gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.extend != null" >
        extend = #{record.extend,jdbcType=VARCHAR},
      </if>
      <if test="record.batchCodeData != null" >
        batch_code_data = #{record.batchCodeData,jdbcType=LONGVARCHAR},
      </if>
      <if test="record.batchCostData != null" >
        batch_cost_data = #{record.batchCostData,jdbcType=LONGVARCHAR},
      </if>
    </set>
    <if test="_parameter != null" >
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExampleWithBLOBs" parameterType="map" >
    update diff_stock_data_compare_info
    set id = #{record.id,jdbcType=BIGINT},
      business_id = #{record.businessId,jdbcType=BIGINT},
      store_id = #{record.storeId,jdbcType=BIGINT},
      goods_no = #{record.goodsNo,jdbcType=VARCHAR},
      batch_code = #{record.batchCode,jdbcType=VARCHAR},
      batch_no = #{record.batchNo,jdbcType=VARCHAR},
      data_type = #{record.dataType,jdbcType=INTEGER},
      reason = #{record.reason,jdbcType=VARCHAR},
      status = #{record.status,jdbcType=INTEGER},
      gmt_create = #{record.gmtCreate,jdbcType=TIMESTAMP},
      extend = #{record.extend,jdbcType=VARCHAR},
      batch_code_data = #{record.batchCodeData,jdbcType=LONGVARCHAR},
      batch_cost_data = #{record.batchCostData,jdbcType=LONGVARCHAR}
    <if test="_parameter != null" >
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map" >
    update diff_stock_data_compare_info
    set id = #{record.id,jdbcType=BIGINT},
      business_id = #{record.businessId,jdbcType=BIGINT},
      store_id = #{record.storeId,jdbcType=BIGINT},
      goods_no = #{record.goodsNo,jdbcType=VARCHAR},
      batch_code = #{record.batchCode,jdbcType=VARCHAR},
      batch_no = #{record.batchNo,jdbcType=VARCHAR},
      data_type = #{record.dataType,jdbcType=INTEGER},
      reason = #{record.reason,jdbcType=VARCHAR},
      status = #{record.status,jdbcType=INTEGER},
      gmt_create = #{record.gmtCreate,jdbcType=TIMESTAMP},
      extend = #{record.extend,jdbcType=VARCHAR}
    <if test="_parameter != null" >
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.cowell.bam.domain.DiffStockDataCompareInfoWithBLOBs" >
    update diff_stock_data_compare_info
    <set >
      <if test="businessId != null" >
        business_id = #{businessId,jdbcType=BIGINT},
      </if>
      <if test="storeId != null" >
        store_id = #{storeId,jdbcType=BIGINT},
      </if>
      <if test="goodsNo != null" >
        goods_no = #{goodsNo,jdbcType=VARCHAR},
      </if>
      <if test="batchCode != null" >
        batch_code = #{batchCode,jdbcType=VARCHAR},
      </if>
      <if test="batchNo != null" >
        batch_no = #{batchNo,jdbcType=VARCHAR},
      </if>
      <if test="dataType != null" >
        data_type = #{dataType,jdbcType=INTEGER},
      </if>
      <if test="reason != null" >
        reason = #{reason,jdbcType=VARCHAR},
      </if>
      <if test="status != null" >
        status = #{status,jdbcType=INTEGER},
      </if>
      <if test="gmtCreate != null" >
        gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="extend != null" >
        extend = #{extend,jdbcType=VARCHAR},
      </if>
      <if test="batchCodeData != null" >
        batch_code_data = #{batchCodeData,jdbcType=LONGVARCHAR},
      </if>
      <if test="batchCostData != null" >
        batch_cost_data = #{batchCostData,jdbcType=LONGVARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKeyWithBLOBs" parameterType="com.cowell.bam.domain.DiffStockDataCompareInfoWithBLOBs" >
    update diff_stock_data_compare_info
    set business_id = #{businessId,jdbcType=BIGINT},
      store_id = #{storeId,jdbcType=BIGINT},
      goods_no = #{goodsNo,jdbcType=VARCHAR},
      batch_code = #{batchCode,jdbcType=VARCHAR},
      batch_no = #{batchNo,jdbcType=VARCHAR},
      data_type = #{dataType,jdbcType=INTEGER},
      reason = #{reason,jdbcType=VARCHAR},
      status = #{status,jdbcType=INTEGER},
      gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      extend = #{extend,jdbcType=VARCHAR},
      batch_code_data = #{batchCodeData,jdbcType=LONGVARCHAR},
      batch_cost_data = #{batchCostData,jdbcType=LONGVARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.cowell.bam.domain.DiffStockDataCompareInfo" >
    update diff_stock_data_compare_info
    set business_id = #{businessId,jdbcType=BIGINT},
      store_id = #{storeId,jdbcType=BIGINT},
      goods_no = #{goodsNo,jdbcType=VARCHAR},
      batch_code = #{batchCode,jdbcType=VARCHAR},
      batch_no = #{batchNo,jdbcType=VARCHAR},
      data_type = #{dataType,jdbcType=INTEGER},
      reason = #{reason,jdbcType=VARCHAR},
      status = #{status,jdbcType=INTEGER},
      gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      extend = #{extend,jdbcType=VARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>
