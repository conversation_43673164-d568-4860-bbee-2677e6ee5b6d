<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cowell.bam.repository.mybatis.dao.DiffDataCompareInfoMapper">
    <resultMap id="BaseResultMap" type="com.cowell.bam.domain.DiffDataCompareInfo">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="business_id" property="businessId" jdbcType="BIGINT"/>
        <result column="store_id" property="storeId" jdbcType="BIGINT"/>
        <result column="goods_no" property="goodsNo" jdbcType="VARCHAR"/>
        <result column="user_id" property="userId" jdbcType="BIGINT"/>
        <result column="data_type" property="dataType" jdbcType="INTEGER"/>
        <result column="reason" property="reason" jdbcType="VARCHAR"/>
        <result column="status" property="status" jdbcType="INTEGER"/>
        <result column="version" property="version" jdbcType="INTEGER"/>
        <result column="created_by" property="createdBy" jdbcType="VARCHAR"/>
        <result column="gmt_create" property="gmtCreate" jdbcType="TIMESTAMP"/>
        <result column="updated_by" property="updatedBy" jdbcType="VARCHAR"/>
        <result column="gmt_update" property="gmtUpdate" jdbcType="TIMESTAMP"/>
        <result column="extend" property="extend" jdbcType="VARCHAR"/>
    </resultMap>
    <resultMap id="ResultMapWithBLOBs" type="com.cowell.bam.domain.DiffDataCompareInfoWithBLOBs"
               extends="BaseResultMap">
        <result column="third_data" property="thirdData" jdbcType="LONGVARCHAR"/>
        <result column="our_data" property="ourData" jdbcType="LONGVARCHAR"/>
    </resultMap>

    <sql id="Example_Where_Clause">
        <where>
            <foreach collection="oredCriteria" item="criteria" separator="or">
                <if test="criteria.valid">
                    <trim prefix="(" suffix=")" prefixOverrides="and">
                        <foreach collection="criteria.criteria" item="criterion">
                            <choose>
                                <when test="criterion.noValue">
                                    and ${criterion.condition}
                                </when>
                                <when test="criterion.singleValue">
                                    and ${criterion.condition} #{criterion.value}
                                </when>
                                <when test="criterion.betweenValue">
                                    and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                                </when>
                                <when test="criterion.listValue">
                                    and ${criterion.condition}
                                    <foreach collection="criterion.value" item="listItem" open="(" close=")"
                                             separator=",">
                                        #{listItem}
                                    </foreach>
                                </when>
                            </choose>
                        </foreach>
                    </trim>
                </if>
            </foreach>
        </where>
    </sql>
    <sql id="Update_By_Example_Where_Clause">
        <where>
            <foreach collection="example.oredCriteria" item="criteria" separator="or">
                <if test="criteria.valid">
                    <trim prefix="(" suffix=")" prefixOverrides="and">
                        <foreach collection="criteria.criteria" item="criterion">
                            <choose>
                                <when test="criterion.noValue">
                                    and ${criterion.condition}
                                </when>
                                <when test="criterion.singleValue">
                                    and ${criterion.condition} #{criterion.value}
                                </when>
                                <when test="criterion.betweenValue">
                                    and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                                </when>
                                <when test="criterion.listValue">
                                    and ${criterion.condition}
                                    <foreach collection="criterion.value" item="listItem" open="(" close=")"
                                             separator=",">
                                        #{listItem}
                                    </foreach>
                                </when>
                            </choose>
                        </foreach>
                    </trim>
                </if>
            </foreach>
        </where>
    </sql>
    <sql id="Base_Column_List">
        id, business_id, store_id, goods_no, user_id, data_type, reason, status, version,
        created_by, gmt_create, updated_by, gmt_update, extend
    </sql>
    <sql id="Blob_Column_List">
        third_data, our_data
    </sql>
    <select id="selectByExampleWithBLOBs" resultMap="ResultMapWithBLOBs"
            parameterType="com.cowell.bam.domain.DiffDataCompareInfoExample">
        select
        <if test="distinct">
            distinct
        </if>
        <include refid="Base_Column_List"/>
        ,
        <include refid="Blob_Column_List"/>
        from diff_data_compare_info
        <if test="_parameter != null">
            <include refid="Example_Where_Clause"/>
        </if>
        <if test="orderByClause != null">
            order by ${orderByClause}
        </if>
        <if test="limitStart >= 0">
            limit ${limitStart}
        </if>
    </select>
    <select id="selectByExample" resultMap="BaseResultMap"
            parameterType="com.cowell.bam.domain.DiffDataCompareInfoExample">
        select
        <if test="distinct">
            distinct
        </if>
        <include refid="Base_Column_List"/>
        from diff_data_compare_info
        <if test="_parameter != null">
            <include refid="Example_Where_Clause"/>
        </if>
        <if test="orderByClause != null">
            order by ${orderByClause}
        </if>
        <if test="limitStart >= 0">
            limit ${limitStart} , ${pageSize}
        </if>
    </select>
    <select id="selectByPrimaryKey" resultMap="ResultMapWithBLOBs" parameterType="java.lang.Long">
        select
        <include refid="Base_Column_List"/>
        ,
        <include refid="Blob_Column_List"/>
        from diff_data_compare_info
        where id = #{id,jdbcType=BIGINT}
    </select>

    <select id="selectDiffCollect" resultType="com.cowell.bam.service.dto.DiffCollectDataResponseDTO" >
        select business_id as businessId ,COUNT(*) as count from diff_data_compare_info
        where data_type  = #{dataType}
         <![CDATA[ and gmt_create > #{gmtCreateStart} and gmt_create < #{gmtCreateEnd} ]]>
        GROUP BY business_id
    </select>

    <select id="selectDiffData" resultType="com.cowell.bam.service.dto.DiffCollectDataResponseDTO" >
        select business_id as businessId, store_id AS storeId, COUNT(*) as count from diff_data_compare_info
        where business_id = #{businessId}
        and data_type  = #{dataType}
        and version = #{version}
        <if test="gmtCreateStart != null">
            <![CDATA[ and gmt_create > #{gmtCreateStart}  ]]>
        </if>
        <if test="gmtCreateEnd != null">
            <![CDATA[ and gmt_create < #{gmtCreateEnd} ]]>
        </if>
        GROUP BY store_id
    </select>

    <select id="selectTransitDiffCollect" resultType="com.cowell.bam.service.dto.DiffCollectDataResponseDTO" >
        SELECT business_id AS businessId, store_id AS storeId, goods_no AS goodsCode, third_data AS thirdData, our_data AS ourData, reason FROM diff_data_compare_info
        WHERE data_type  = #{dataType}
        AND extend = #{date}
        <if test="businessIdList != null">
            AND business_id IN
            <foreach collection="businessIdList" item="code" index="index" open="(" close=")" separator=",">
                #{code}
            </foreach>
        </if>
        <if test="version != null">
            AND  VERSION = #{version}
        </if>
        <![CDATA[ AND gmt_create > #{gmtCreateStart} AND gmt_create < #{gmtCreateEnd} ]]>
        ORDER BY business_id, store_id DESC
    </select>

    <select id="selectTransitDiffBusiness" resultType="java.lang.Long" >
        SELECT DISTINCT business_id FROM diff_data_compare_info
        WHERE data_type  = #{dataType}  AND extend = #{date}
        <if test="version != null">
            AND  VERSION = #{version}
        </if>
        <![CDATA[ and gmt_create > #{gmtCreateStart} and gmt_create < #{gmtCreateEnd} ]]>
    </select>

    <select id="selectMaxVersionDay" resultType="java.lang.Integer" >
        SELECT max(version) FROM diff_data_compare_info
        WHERE data_type  = #{dataType} AND extend = #{date}  <![CDATA[ and gmt_create > #{gmtCreateStart} and gmt_create < #{gmtCreateEnd} ]]>  LIMIT 1
    </select>

    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        delete from diff_data_compare_info
        where id = #{id,jdbcType=BIGINT}
    </delete>
    <delete id="deleteByExample" parameterType="com.cowell.bam.domain.DiffDataCompareInfoExample">
        delete from diff_data_compare_info
        <if test="_parameter != null">
            <include refid="Example_Where_Clause"/>
        </if>
        order  by id limit 20000
    </delete>
    <delete id="deleteByDateExample" parameterType="com.cowell.bam.domain.DiffDataCompareInfoExample">
        delete from diff_data_compare_info
        <if test="_parameter != null">
            <include refid="Example_Where_Clause"/>
        </if>
    </delete>
    <insert id="insert" parameterType="com.cowell.bam.domain.DiffDataCompareInfoWithBLOBs">
        insert into diff_data_compare_info (id, business_id, store_id,
        goods_no, user_id, data_type,
        reason, status, version,
        created_by, gmt_create, updated_by,
        gmt_update, extend, third_data,
        our_data)
        values (#{id,jdbcType=BIGINT}, #{businessId,jdbcType=BIGINT}, #{storeId,jdbcType=BIGINT},
        #{goodsNo,jdbcType=VARCHAR}, #{userId,jdbcType=BIGINT}, #{dataType,jdbcType=INTEGER},
        #{reason,jdbcType=VARCHAR}, #{status,jdbcType=INTEGER}, #{version,jdbcType=INTEGER},
        #{createdBy,jdbcType=VARCHAR}, #{gmtCreate,jdbcType=TIMESTAMP}, #{updatedBy,jdbcType=VARCHAR},
        #{gmtUpdate,jdbcType=TIMESTAMP}, #{extend,jdbcType=VARCHAR}, #{thirdData,jdbcType=LONGVARCHAR},
        #{ourData,jdbcType=LONGVARCHAR})
    </insert>
    <insert id="insertSelective" parameterType="com.cowell.bam.domain.DiffDataCompareInfoWithBLOBs">
        insert into diff_data_compare_info
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                id,
            </if>
            <if test="businessId != null">
                business_id,
            </if>
            <if test="storeId != null">
                store_id,
            </if>
            <if test="goodsNo != null">
                goods_no,
            </if>
            <if test="userId != null">
                user_id,
            </if>
            <if test="dataType != null">
                data_type,
            </if>
            <if test="reason != null">
                reason,
            </if>
            <if test="status != null">
                status,
            </if>
            <if test="version != null">
                version,
            </if>
            <if test="createdBy != null">
                created_by,
            </if>
            <if test="gmtCreate != null">
                gmt_create,
            </if>
            <if test="updatedBy != null">
                updated_by,
            </if>
            <if test="gmtUpdate != null">
                gmt_update,
            </if>
            <if test="extend != null">
                extend,
            </if>
            <if test="thirdData != null">
                third_data,
            </if>
            <if test="ourData != null">
                our_data,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id,jdbcType=BIGINT},
            </if>
            <if test="businessId != null">
                #{businessId,jdbcType=BIGINT},
            </if>
            <if test="storeId != null">
                #{storeId,jdbcType=BIGINT},
            </if>
            <if test="goodsNo != null">
                #{goodsNo,jdbcType=VARCHAR},
            </if>
            <if test="userId != null">
                #{userId,jdbcType=BIGINT},
            </if>
            <if test="dataType != null">
                #{dataType,jdbcType=INTEGER},
            </if>
            <if test="reason != null">
                #{reason,jdbcType=VARCHAR},
            </if>
            <if test="status != null">
                #{status,jdbcType=INTEGER},
            </if>
            <if test="version != null">
                #{version,jdbcType=INTEGER},
            </if>
            <if test="createdBy != null">
                #{createdBy,jdbcType=VARCHAR},
            </if>
            <if test="gmtCreate != null">
                #{gmtCreate,jdbcType=TIMESTAMP},
            </if>
            <if test="updatedBy != null">
                #{updatedBy,jdbcType=VARCHAR},
            </if>
            <if test="gmtUpdate != null">
                #{gmtUpdate,jdbcType=TIMESTAMP},
            </if>
            <if test="extend != null">
                #{extend,jdbcType=VARCHAR},
            </if>
            <if test="thirdData != null">
                #{thirdData,jdbcType=LONGVARCHAR},
            </if>
            <if test="ourData != null">
                #{ourData,jdbcType=LONGVARCHAR},
            </if>
        </trim>
    </insert>
    <select id="countByExample" parameterType="com.cowell.bam.domain.DiffDataCompareInfoExample"
            resultType="java.lang.Integer">
        select count(*) from diff_data_compare_info
        <if test="_parameter != null">
            <include refid="Example_Where_Clause"/>
        </if>
    </select>
    <update id="updateByExampleSelective" parameterType="map">
        update diff_data_compare_info
        <set>
            <if test="record.id != null">
                id = #{record.id,jdbcType=BIGINT},
            </if>
            <if test="record.businessId != null">
                business_id = #{record.businessId,jdbcType=BIGINT},
            </if>
            <if test="record.storeId != null">
                store_id = #{record.storeId,jdbcType=BIGINT},
            </if>
            <if test="record.goodsNo != null">
                goods_no = #{record.goodsNo,jdbcType=VARCHAR},
            </if>
            <if test="record.userId != null">
                user_id = #{record.userId,jdbcType=BIGINT},
            </if>
            <if test="record.dataType != null">
                data_type = #{record.dataType,jdbcType=INTEGER},
            </if>
            <if test="record.reason != null">
                reason = #{record.reason,jdbcType=VARCHAR},
            </if>
            <if test="record.status != null">
                status = #{record.status,jdbcType=INTEGER},
            </if>
            <if test="record.version != null">
                version = #{record.version,jdbcType=INTEGER},
            </if>
            <if test="record.createdBy != null">
                created_by = #{record.createdBy,jdbcType=VARCHAR},
            </if>
            <if test="record.gmtCreate != null">
                gmt_create = #{record.gmtCreate,jdbcType=TIMESTAMP},
            </if>
            <if test="record.updatedBy != null">
                updated_by = #{record.updatedBy,jdbcType=VARCHAR},
            </if>
            <if test="record.gmtUpdate != null">
                gmt_update = #{record.gmtUpdate,jdbcType=TIMESTAMP},
            </if>
            <if test="record.extend != null">
                extend = #{record.extend,jdbcType=VARCHAR},
            </if>
            <if test="record.thirdData != null">
                third_data = #{record.thirdData,jdbcType=LONGVARCHAR},
            </if>
            <if test="record.ourData != null">
                our_data = #{record.ourData,jdbcType=LONGVARCHAR},
            </if>
        </set>
        <if test="_parameter != null">
            <include refid="Update_By_Example_Where_Clause"/>
        </if>
    </update>
    <update id="updateByExampleWithBLOBs" parameterType="map">
        update diff_data_compare_info
        set id = #{record.id,jdbcType=BIGINT},
        business_id = #{record.businessId,jdbcType=BIGINT},
        store_id = #{record.storeId,jdbcType=BIGINT},
        goods_no = #{record.goodsNo,jdbcType=VARCHAR},
        user_id = #{record.userId,jdbcType=BIGINT},
        data_type = #{record.dataType,jdbcType=INTEGER},
        reason = #{record.reason,jdbcType=VARCHAR},
        status = #{record.status,jdbcType=INTEGER},
        version = #{record.version,jdbcType=INTEGER},
        created_by = #{record.createdBy,jdbcType=VARCHAR},
        gmt_create = #{record.gmtCreate,jdbcType=TIMESTAMP},
        updated_by = #{record.updatedBy,jdbcType=VARCHAR},
        gmt_update = #{record.gmtUpdate,jdbcType=TIMESTAMP},
        extend = #{record.extend,jdbcType=VARCHAR},
        third_data = #{record.thirdData,jdbcType=LONGVARCHAR},
        our_data = #{record.ourData,jdbcType=LONGVARCHAR}
        <if test="_parameter != null">
            <include refid="Update_By_Example_Where_Clause"/>
        </if>
    </update>
    <update id="updateByExample" parameterType="map">
        update diff_data_compare_info
        set id = #{record.id,jdbcType=BIGINT},
        business_id = #{record.businessId,jdbcType=BIGINT},
        store_id = #{record.storeId,jdbcType=BIGINT},
        goods_no = #{record.goodsNo,jdbcType=VARCHAR},
        user_id = #{record.userId,jdbcType=BIGINT},
        data_type = #{record.dataType,jdbcType=INTEGER},
        reason = #{record.reason,jdbcType=VARCHAR},
        status = #{record.status,jdbcType=INTEGER},
        version = #{record.version,jdbcType=INTEGER},
        created_by = #{record.createdBy,jdbcType=VARCHAR},
        gmt_create = #{record.gmtCreate,jdbcType=TIMESTAMP},
        updated_by = #{record.updatedBy,jdbcType=VARCHAR},
        gmt_update = #{record.gmtUpdate,jdbcType=TIMESTAMP},
        extend = #{record.extend,jdbcType=VARCHAR}
        <if test="_parameter != null">
            <include refid="Update_By_Example_Where_Clause"/>
        </if>
    </update>
    <update id="updateByPrimaryKeySelective"
            parameterType="com.cowell.bam.domain.DiffDataCompareInfoWithBLOBs">
        update diff_data_compare_info
        <set>
            <if test="businessId != null">
                business_id = #{businessId,jdbcType=BIGINT},
            </if>
            <if test="storeId != null">
                store_id = #{storeId,jdbcType=BIGINT},
            </if>
            <if test="goodsNo != null">
                goods_no = #{goodsNo,jdbcType=VARCHAR},
            </if>
            <if test="userId != null">
                user_id = #{userId,jdbcType=BIGINT},
            </if>
            <if test="dataType != null">
                data_type = #{dataType,jdbcType=INTEGER},
            </if>
            <if test="reason != null">
                reason = #{reason,jdbcType=VARCHAR},
            </if>
            <if test="status != null">
                status = #{status,jdbcType=INTEGER},
            </if>
            <if test="version != null">
                version = #{version,jdbcType=INTEGER},
            </if>
            <if test="createdBy != null">
                created_by = #{createdBy,jdbcType=VARCHAR},
            </if>
            <if test="gmtCreate != null">
                gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
            </if>
            <if test="updatedBy != null">
                updated_by = #{updatedBy,jdbcType=VARCHAR},
            </if>
            <if test="gmtUpdate != null">
                gmt_update = #{gmtUpdate,jdbcType=TIMESTAMP},
            </if>
            <if test="extend != null">
                extend = #{extend,jdbcType=VARCHAR},
            </if>
            <if test="thirdData != null">
                third_data = #{thirdData,jdbcType=LONGVARCHAR},
            </if>
            <if test="ourData != null">
                our_data = #{ourData,jdbcType=LONGVARCHAR},
            </if>
        </set>
        where id = #{id,jdbcType=BIGINT}
    </update>
    <update id="updateByPrimaryKeyWithBLOBs"
            parameterType="com.cowell.bam.domain.DiffDataCompareInfoWithBLOBs">
        update diff_data_compare_info
        set business_id = #{businessId,jdbcType=BIGINT},
        store_id = #{storeId,jdbcType=BIGINT},
        goods_no = #{goodsNo,jdbcType=VARCHAR},
        user_id = #{userId,jdbcType=BIGINT},
        data_type = #{dataType,jdbcType=INTEGER},
        reason = #{reason,jdbcType=VARCHAR},
        status = #{status,jdbcType=INTEGER},
        version = #{version,jdbcType=INTEGER},
        created_by = #{createdBy,jdbcType=VARCHAR},
        gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
        updated_by = #{updatedBy,jdbcType=VARCHAR},
        gmt_update = #{gmtUpdate,jdbcType=TIMESTAMP},
        extend = #{extend,jdbcType=VARCHAR},
        third_data = #{thirdData,jdbcType=LONGVARCHAR},
        our_data = #{ourData,jdbcType=LONGVARCHAR}
        where id = #{id,jdbcType=BIGINT}
    </update>
    <update id="updateByPrimaryKey" parameterType="com.cowell.bam.domain.DiffDataCompareInfo">
        update diff_data_compare_info
        set business_id = #{businessId,jdbcType=BIGINT},
        store_id = #{storeId,jdbcType=BIGINT},
        goods_no = #{goodsNo,jdbcType=VARCHAR},
        user_id = #{userId,jdbcType=BIGINT},
        data_type = #{dataType,jdbcType=INTEGER},
        reason = #{reason,jdbcType=VARCHAR},
        status = #{status,jdbcType=INTEGER},
        version = #{version,jdbcType=INTEGER},
        created_by = #{createdBy,jdbcType=VARCHAR},
        gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
        updated_by = #{updatedBy,jdbcType=VARCHAR},
        gmt_update = #{gmtUpdate,jdbcType=TIMESTAMP},
        extend = #{extend,jdbcType=VARCHAR}
        where id = #{id,jdbcType=BIGINT}
    </update>
    <insert id="batchInsert" parameterType="java.util.List">
        insert into diff_data_compare_info (
        id,business_id,store_id,goods_no,user_id,data_type,reason,status,version,
        created_by,gmt_create,updated_by,gmt_update,extend,third_data,our_data)
        VALUES
        <foreach collection="list" item="record" separator=",">
            (#{record.id,jdbcType=BIGINT},#{record.businessId,jdbcType=BIGINT},#{record.storeId,jdbcType=BIGINT},
            #{record.goodsNo,jdbcType=VARCHAR},#{record.userId,jdbcType=BIGINT},#{record.dataType,jdbcType=INTEGER},
            #{record.reason,jdbcType=VARCHAR},#{record.status,jdbcType=INTEGER},#{record.version,jdbcType=INTEGER},
            #{record.createdBy,jdbcType=VARCHAR},#{record.gmtCreate,jdbcType=TIMESTAMP},#{record.updatedBy,jdbcType=VARCHAR},
            #{record.gmtUpdate,jdbcType=TIMESTAMP},#{record.extend,jdbcType=VARCHAR},#{record.thirdData,jdbcType=LONGVARCHAR},
            #{record.ourData,jdbcType=LONGVARCHAR})
        </foreach>
    </insert>


    <select id="queryHistoryCompareRecords" parameterType="com.cowell.bam.query.CompareDataQuery" resultType="long">
        select
        id
        from diff_data_compare_info
        where data_type = #{dataType}
        <![CDATA[  and gmt_create < #{endDate} ]]>
        <if test="startDate != null">
        <![CDATA[   and  gmt_create > #{startDate} ]]>
        </if>
        limit #{pageSize}
    </select>

    <select id="selectDiffDetail" resultMap="ResultMapWithBLOBs" >
        select
        <include refid="Base_Column_List"/>
        ,
        <include refid="Blob_Column_List"/>
        from diff_data_compare_info
        where data_type  = #{dataType}
        <![CDATA[ and gmt_create > #{gmtCreateStart} and gmt_create < #{gmtCreateEnd} ]]>
    </select>
</mapper>
