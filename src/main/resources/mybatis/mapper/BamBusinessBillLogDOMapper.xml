<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.cowell.bam.repository.mybatis.dao.BamBusinessBillLogDOMapper" >
  <resultMap id="BaseResultMap" type="com.cowell.bam.domain.BamBusinessBillLogDO" >
      <id column="id" jdbcType="BIGINT" property="id"/>
    <id column="gu_id" property="guId" jdbcType="VARCHAR" />
    <result column="bill_id" property="billId" jdbcType="VARCHAR" />
    <result column="flow_id" property="flowId" jdbcType="INTEGER" />
    <result column="step_id" property="stepId" jdbcType="INTEGER" />
    <result column="send_sys_id" property="sendSysId" jdbcType="INTEGER" />
    <result column="receive_sys_id" property="receiveSysId" jdbcType="INTEGER" />
    <result column="company_id" property="companyId" jdbcType="VARCHAR" />
    <result column="store_id" property="storeId" jdbcType="VARCHAR" />
    <result column="timestamp" property="timestamp" jdbcType="VARCHAR" />
    <result column="business_date" property="businessDate" jdbcType="VARCHAR" />
    <result column="item_count" property="itemCount" jdbcType="INTEGER" />
    <result column="status" property="status" jdbcType="INTEGER" />
    <result column="message" property="message" jdbcType="VARCHAR" />
    <result column="extend1" property="extend1" jdbcType="VARCHAR" />
    <result column="extend2" property="extend2" jdbcType="VARCHAR" />
    <result column="extend3" property="extend3" jdbcType="VARCHAR" />
    <result column="extend4" property="extend4" jdbcType="VARCHAR" />
      <result column="gmt_create" property="gmtCreate" jdbcType="TIMESTAMP"/>
      <result column="env" property="env" jdbcType="VARCHAR" />
  </resultMap>
  <sql id="Base_Column_List" >
    id, gu_id,bill_id, flow_id, step_id, send_sys_id, receive_sys_id, company_id, store_id,
    timestamp, business_date, item_count, status, message, extend1, extend2, extend3,
    extend4
  </sql>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Long" >
    select
    <include refid="Base_Column_List" />
    from bam_business_bill_log
    where id = #{id,jdbcType=BIGINT}
  </select>
    <select id="selectTop5ByCompanyAndStoreId" resultType="java.lang.String" parameterType="com.cowell.bam.domain.BamQueryDO" >
        SELECT DISTINCT(bill_id)
        from bam_business_bill_log
        where  company_id = #{companyId,jdbcType=VARCHAR}
           and store_id = #{storeId,jdbcType=VARCHAR}
           and step_id = #{stepId,jdbcType=VARCHAR}
           and bill_id != ''
        order by business_date desc
        limit 5
    </select>

  <select id="selectByBillId" resultMap="BaseResultMap" parameterType="java.lang.String" >
        select
        <include refid="Base_Column_List" />
        from bam_business_bill_log
        where bill_id = #{billId,jdbcType=VARCHAR}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long" >
    delete from bam_business_bill_log
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" parameterType="com.cowell.bam.domain.BamBusinessBillLogDO" >
    insert into bam_business_bill_log (gu_id, bill_id, flow_id,
      step_id, send_sys_id, receive_sys_id,
      company_id, store_id, timestamp,
      business_date, item_count, status,
      message, extend1, extend2,
      extend3, extend4,gmt_create,env)
    values (#{guId,jdbcType=VARCHAR}, #{billId,jdbcType=VARCHAR}, #{flowId,jdbcType=INTEGER},
      #{stepId,jdbcType=INTEGER}, #{sendSysId,jdbcType=INTEGER}, #{receiveSysId,jdbcType=INTEGER},
      #{companyId,jdbcType=VARCHAR}, #{storeId,jdbcType=VARCHAR}, #{timestamp,jdbcType=VARCHAR},
      #{businessDate,jdbcType=VARCHAR}, #{itemCount,jdbcType=INTEGER}, #{status,jdbcType=INTEGER},
      #{message,jdbcType=VARCHAR}, #{extend1,jdbcType=VARCHAR}, #{extend2,jdbcType=VARCHAR},
      #{extend3,jdbcType=VARCHAR}, #{extend4,jdbcType=VARCHAR},#{gmtCreate,jdbcType=TIMESTAMP},#{env,jdbcType=VARCHAR})
  </insert>

    <insert id="batchInsert" parameterType="com.cowell.bam.domain.BamBusinessBillLogDO" >
        insert into bam_business_bill_log (gu_id, bill_id, flow_id,
        step_id, send_sys_id, receive_sys_id,
        company_id, store_id, timestamp,
        business_date, item_count, status,
        message, extend1, extend2,
        extend3, extend4,gmt_create,env)
        values
        <foreach collection="list" item="item" separator=",">
            (#{item.guId,jdbcType=VARCHAR}, #{item.billId,jdbcType=VARCHAR}, #{item.flowId,jdbcType=INTEGER},
            #{item.stepId,jdbcType=INTEGER}, #{item.sendSysId,jdbcType=INTEGER}, #{item.receiveSysId,jdbcType=INTEGER},
            #{item.companyId,jdbcType=VARCHAR}, #{item.storeId,jdbcType=VARCHAR}, #{item.timestamp,jdbcType=VARCHAR},
            #{item.businessDate,jdbcType=VARCHAR}, #{item.itemCount,jdbcType=INTEGER}, #{item.status,jdbcType=INTEGER},
            #{item.message,jdbcType=VARCHAR}, #{item.extend1,jdbcType=VARCHAR}, #{item.extend2,jdbcType=VARCHAR},
            #{item.extend3,jdbcType=VARCHAR}, #{item.extend4,jdbcType=VARCHAR},#{item.gmtCreate,jdbcType=TIMESTAMP},#{item.env,jdbcType=VARCHAR})
        </foreach>
        on duplicate key update
        gu_id=VALUES(gu_id), bill_id=VALUES(bill_id), flow_id=VALUES(flow_id),
        step_id=VALUES(step_id), send_sys_id=VALUES(send_sys_id), receive_sys_id=VALUES(receive_sys_id),
        company_id=VALUES(company_id), store_id=VALUES(store_id), timestamp=VALUES(timestamp),
        business_date=VALUES(business_date), item_count=VALUES(item_count), status=VALUES(status),
        message=VALUES(message), extend1=VALUES(extend1), extend2=VALUES(extend2),
        extend3=VALUES(extend3), extend4=VALUES(extend4),gmt_create=VALUES(gmt_create),env=VALUES(env)
    </insert>
</mapper>
