<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cowell.bam.repository.mybatis.dao.AuditUrlConfigMapper">
  <resultMap id="BaseResultMap" type="com.cowell.bam.domain.AuditUrlConfig">
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="app_name" jdbcType="VARCHAR" property="appName" />
    <result column="uri" jdbcType="VARCHAR" property="uri" />
    <result column="description" jdbcType="VARCHAR" property="description" />
    <result column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate" />
    <result column="gmt_update" jdbcType="TIMESTAMP" property="gmtUpdate" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, app_name, uri, description, gmt_create, gmt_update
  </sql>
  <select id="selectByExample" parameterType="com.cowell.bam.domain.AuditUrlConfigExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from audit_url_config
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from audit_url_config
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    delete from audit_url_config
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <delete id="deleteByExample" parameterType="com.cowell.bam.domain.AuditUrlConfigExample">
    delete from audit_url_config
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.cowell.bam.domain.AuditUrlConfig">
    insert into audit_url_config (app_name, uri, description,
      gmt_create, gmt_update)
    values (#{appName,jdbcType=VARCHAR}, #{uri,jdbcType=VARCHAR}, #{description,jdbcType=VARCHAR},
      #{gmtCreate,jdbcType=TIMESTAMP}, #{gmtUpdate,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" parameterType="com.cowell.bam.domain.AuditUrlConfig">
    insert into audit_url_config
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="appName != null">
        app_name,
      </if>
      <if test="uri != null">
        uri,
      </if>
      <if test="description != null">
        description,
      </if>
      <if test="gmtCreate != null">
        gmt_create,
      </if>
      <if test="gmtUpdate != null">
        gmt_update,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="appName != null">
        #{appName,jdbcType=VARCHAR},
      </if>
      <if test="uri != null">
        #{uri,jdbcType=VARCHAR},
      </if>
      <if test="description != null">
        #{description,jdbcType=VARCHAR},
      </if>
      <if test="gmtCreate != null">
        #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtUpdate != null">
        #{gmtUpdate,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.cowell.bam.domain.AuditUrlConfigExample" resultType="java.lang.Long">
    select count(*) from audit_url_config
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update audit_url_config
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=INTEGER},
      </if>
      <if test="record.appName != null">
        app_name = #{record.appName,jdbcType=VARCHAR},
      </if>
      <if test="record.uri != null">
        uri = #{record.uri,jdbcType=VARCHAR},
      </if>
      <if test="record.description != null">
        description = #{record.description,jdbcType=VARCHAR},
      </if>
      <if test="record.gmtCreate != null">
        gmt_create = #{record.gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.gmtUpdate != null">
        gmt_update = #{record.gmtUpdate,jdbcType=TIMESTAMP},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update audit_url_config
    set id = #{record.id,jdbcType=INTEGER},
      app_name = #{record.appName,jdbcType=VARCHAR},
      uri = #{record.uri,jdbcType=VARCHAR},
      description = #{record.description,jdbcType=VARCHAR},
      gmt_create = #{record.gmtCreate,jdbcType=TIMESTAMP},
      gmt_update = #{record.gmtUpdate,jdbcType=TIMESTAMP}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.cowell.bam.domain.AuditUrlConfig">
    update audit_url_config
    <set>
      <if test="appName != null">
        app_name = #{appName,jdbcType=VARCHAR},
      </if>
      <if test="uri != null">
        uri = #{uri,jdbcType=VARCHAR},
      </if>
      <if test="description != null">
        description = #{description,jdbcType=VARCHAR},
      </if>
      <if test="gmtCreate != null">
        gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtUpdate != null">
        gmt_update = #{gmtUpdate,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.cowell.bam.domain.AuditUrlConfig">
    update audit_url_config
    set app_name = #{appName,jdbcType=VARCHAR},
      uri = #{uri,jdbcType=VARCHAR},
      description = #{description,jdbcType=VARCHAR},
      gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      gmt_update = #{gmtUpdate,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=INTEGER}
  </update>
</mapper>
