<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cowell.bam.repository.mybatis.dao.AuditLogMapper">
  <resultMap id="BaseResultMap" type="com.cowell.bam.domain.AuditLog">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="source_ip" jdbcType="VARCHAR" property="sourceIp" />
    <result column="user_id" jdbcType="VARCHAR" property="userId" />
    <result column="user_name" jdbcType="VARCHAR" property="userName" />
    <result column="operate_date" jdbcType="TIMESTAMP" property="operateDate" />
    <result column="operate_module" jdbcType="VARCHAR" property="operateModule" />
    <result column="result" jdbcType="TINYINT" property="result" />
    <result column="url" jdbcType="VARCHAR" property="url" />
    <result column="system_name" jdbcType="VARCHAR" property="systemName" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, source_ip, user_id, user_name, operate_date, operate_module, result, url, system_name
  </sql>
  <select id="selectByExample" parameterType="com.cowell.bam.domain.AuditLogExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from audit_log
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from audit_log
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from audit_log
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.cowell.bam.domain.AuditLogExample">
    delete from audit_log
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.cowell.bam.domain.AuditLog">
    insert into audit_log (id, source_ip, user_id,
      user_name, operate_date, operate_module,
      result, url, system_name
      )
    values (#{id,jdbcType=BIGINT}, #{sourceIp,jdbcType=VARCHAR}, #{userId,jdbcType=VARCHAR},
      #{userName,jdbcType=VARCHAR}, #{operateDate,jdbcType=TIMESTAMP}, #{operateModule,jdbcType=VARCHAR},
      #{result,jdbcType=TINYINT}, #{url,jdbcType=VARCHAR}, #{systemName,jdbcType=VARCHAR}
      )
  </insert>
    <insert id="insertBatch" parameterType="com.cowell.bam.domain.AuditLog">
        insert into audit_log (source_ip,user_id,user_name,operate_date,operate_module,result,url,system_name) values
        <foreach collection="list" item="item" separator=",">
            (#{item.sourceIp,jdbcType=VARCHAR}, #{item.userId,jdbcType=VARCHAR}, #{item.userName,jdbcType=VARCHAR}, #{item.operateDate,jdbcType=TIMESTAMP}, #{item.operateModule,jdbcType=VARCHAR}, #{item.result,jdbcType=TINYINT}, #{item.url,jdbcType=VARCHAR}, #{item.systemName,jdbcType=VARCHAR})
        </foreach>
    </insert>
  <insert id="insertSelective" parameterType="com.cowell.bam.domain.AuditLog">
    insert into audit_log
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="sourceIp != null">
        source_ip,
      </if>
      <if test="userId != null">
        user_id,
      </if>
      <if test="userName != null">
        user_name,
      </if>
      <if test="operateDate != null">
        operate_date,
      </if>
      <if test="operateModule != null">
        operate_module,
      </if>
      <if test="result != null">
        result,
      </if>
      <if test="url != null">
        url,
      </if>
      <if test="systemName != null">
        system_name,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="sourceIp != null">
        #{sourceIp,jdbcType=VARCHAR},
      </if>
      <if test="userId != null">
        #{userId,jdbcType=VARCHAR},
      </if>
      <if test="userName != null">
        #{userName,jdbcType=VARCHAR},
      </if>
      <if test="operateDate != null">
        #{operateDate,jdbcType=TIMESTAMP},
      </if>
      <if test="operateModule != null">
        #{operateModule,jdbcType=VARCHAR},
      </if>
      <if test="result != null">
        #{result,jdbcType=TINYINT},
      </if>
      <if test="url != null">
        #{url,jdbcType=VARCHAR},
      </if>
      <if test="systemName != null">
        #{systemName,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
    <select id="countByExample" parameterType="com.cowell.bam.domain.AuditLogExample" resultType="java.lang.Long">
    select count(*) from audit_log
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update audit_log
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.sourceIp != null">
        source_ip = #{record.sourceIp,jdbcType=VARCHAR},
      </if>
      <if test="record.userId != null">
        user_id = #{record.userId,jdbcType=VARCHAR},
      </if>
      <if test="record.userName != null">
        user_name = #{record.userName,jdbcType=VARCHAR},
      </if>
      <if test="record.operateDate != null">
        operate_date = #{record.operateDate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.operateModule != null">
        operate_module = #{record.operateModule,jdbcType=VARCHAR},
      </if>
      <if test="record.result != null">
        result = #{record.result,jdbcType=TINYINT},
      </if>
      <if test="record.url != null">
        url = #{record.url,jdbcType=VARCHAR},
      </if>
      <if test="record.systemName != null">
        system_name = #{record.systemName,jdbcType=VARCHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update audit_log
    set id = #{record.id,jdbcType=BIGINT},
      source_ip = #{record.sourceIp,jdbcType=VARCHAR},
      user_id = #{record.userId,jdbcType=VARCHAR},
      user_name = #{record.userName,jdbcType=VARCHAR},
      operate_date = #{record.operateDate,jdbcType=TIMESTAMP},
      operate_module = #{record.operateModule,jdbcType=VARCHAR},
      result = #{record.result,jdbcType=TINYINT},
      url = #{record.url,jdbcType=VARCHAR},
      system_name = #{record.systemName,jdbcType=VARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.cowell.bam.domain.AuditLog">
    update audit_log
    <set>
      <if test="sourceIp != null">
        source_ip = #{sourceIp,jdbcType=VARCHAR},
      </if>
      <if test="userId != null">
        user_id = #{userId,jdbcType=VARCHAR},
      </if>
      <if test="userName != null">
        user_name = #{userName,jdbcType=VARCHAR},
      </if>
      <if test="operateDate != null">
        operate_date = #{operateDate,jdbcType=TIMESTAMP},
      </if>
      <if test="operateModule != null">
        operate_module = #{operateModule,jdbcType=VARCHAR},
      </if>
      <if test="result != null">
        result = #{result,jdbcType=TINYINT},
      </if>
      <if test="url != null">
        url = #{url,jdbcType=VARCHAR},
      </if>
      <if test="systemName != null">
        system_name = #{systemName,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.cowell.bam.domain.AuditLog">
    update audit_log
    set source_ip = #{sourceIp,jdbcType=VARCHAR},
      user_id = #{userId,jdbcType=VARCHAR},
      user_name = #{userName,jdbcType=VARCHAR},
      operate_date = #{operateDate,jdbcType=TIMESTAMP},
      operate_module = #{operateModule,jdbcType=VARCHAR},
      result = #{result,jdbcType=TINYINT},
      url = #{url,jdbcType=VARCHAR},
      system_name = #{systemName,jdbcType=VARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>
