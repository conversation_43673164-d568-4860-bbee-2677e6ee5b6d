package com.cowell.bam.util;

import org.apache.rocketmq.client.consumer.DefaultMQPushConsumer;
import org.apache.rocketmq.client.producer.DefaultMQProducer;
import org.apache.rocketmq.common.consumer.ConsumeFromWhere;
import org.apache.rocketmq.common.protocol.heartbeat.MessageModel;

import java.util.UUID;


/**
 * bam
 * 2020/9/14 10:01
 *
 * <AUTHOR>
 * @since
 **/
public class CommonUtil {

    /**
     * 填充mq消费者信息
     */
    public static void fillConsumerInfo(DefaultMQPushConsumer consumer) {
        consumer.setConsumeFromWhere(ConsumeFromWhere.CONSUME_FROM_LAST_OFFSET);
        consumer.setMessageModel(MessageModel.CLUSTERING);
        consumer.setMaxReconsumeTimes(3);
        consumer.setPullBatchSize(1);
        consumer.setConsumeThreadMax(2);
        consumer.setConsumeThreadMin(1);
        consumer.setPullThresholdForQueue(20);
        consumer.setPullThresholdForTopic(20);
        consumer.setConsumeTimeout(5);

    }

    /**
     * 填充mq生产者信息
     * @param producer
     */
    public static void fillProducerInfo(DefaultMQProducer producer){
        producer.setInstanceName(UUID.randomUUID().toString());
        producer.setRetryAnotherBrokerWhenNotStoreOK(true);
        producer.setRetryTimesWhenSendAsyncFailed(18);
        producer.setSendMsgTimeout(30000);
        producer.setRetryTimesWhenSendFailed(18);
        producer.setMaxMessageSize(10485760);
    }
}
