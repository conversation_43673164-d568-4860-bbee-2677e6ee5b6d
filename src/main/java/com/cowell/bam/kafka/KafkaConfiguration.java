package com.cowell.bam.kafka;

import org.apache.kafka.clients.consumer.ConsumerConfig;
import org.apache.kafka.common.serialization.StringDeserializer;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import org.springframework.kafka.annotation.EnableKafka;
import org.springframework.kafka.config.ConcurrentKafkaListenerContainerFactory;
import org.springframework.kafka.config.KafkaListenerContainerFactory;
import org.springframework.kafka.core.*;
import org.springframework.kafka.listener.AbstractMessageListenerContainer;

import java.util.HashMap;
import java.util.Map;

/**
 * @Author: GaiWen
 * @Date: 2019/1/10 下午7:35
 */
@Configuration
@EnableKafka
public class KafkaConfiguration {

    @Value("${bam.kafka.group}")
    private String consumerGroupId;
    @Value("${spring.kafka.consumer.bootstrap-servers}")
    private String consumerBootstrapServers;
    @Value("${spring.kafka.consumer.key-deserializer}")
    private String consumerKeyDeserializer;
    @Value("${spring.kafka.consumer.value-deserializer}")
    private String consumerValueDeserializer;
    @Value("${bam.kafka.topic}")
    private String bdpKafaGoodsTopic;

    @Value("${spring.kafka.auditlog-consumer.bootstrap-servers}")
    private String auditLogBootStrapServers;
    @Value("${spring.kafka.auditlog-consumer.group}")
    private String auditLogGroup;

    /**
     * ConsumerFactory
     * @return
     */
    @Primary
    @Bean(name="consumerFactory")
    public ConsumerFactory<Object, Object> consumerFactory(){
        Map<String, Object> configs = new HashMap<String, Object>();
        configs.put(ConsumerConfig.BOOTSTRAP_SERVERS_CONFIG, consumerBootstrapServers);
        configs.put(ConsumerConfig.GROUP_ID_CONFIG, consumerGroupId);
        configs.put(ConsumerConfig.ENABLE_AUTO_COMMIT_CONFIG, false);
        //批量消费数量
        configs.put(ConsumerConfig.MAX_POLL_RECORDS_CONFIG, 5000);
        configs.put(ConsumerConfig.KEY_DESERIALIZER_CLASS_CONFIG, StringDeserializer.class);
        configs.put(ConsumerConfig.VALUE_DESERIALIZER_CLASS_CONFIG,StringDeserializer.class);
        configs.put(ConsumerConfig.AUTO_OFFSET_RESET_CONFIG, "earliest");

        return new DefaultKafkaConsumerFactory<>(configs);
    }

    @Bean(name="auditConsumerFactory")
    public ConsumerFactory<Object, Object> auditConsumerFactory(){
        Map<String, Object> configs = new HashMap<String, Object>();
        configs.put(ConsumerConfig.BOOTSTRAP_SERVERS_CONFIG, auditLogBootStrapServers);
        configs.put(ConsumerConfig.GROUP_ID_CONFIG, auditLogGroup);
        configs.put(ConsumerConfig.ENABLE_AUTO_COMMIT_CONFIG, false);
        //批量消费数量
        configs.put(ConsumerConfig.MAX_POLL_RECORDS_CONFIG, 1000);
        configs.put(ConsumerConfig.KEY_DESERIALIZER_CLASS_CONFIG, StringDeserializer.class);
        configs.put(ConsumerConfig.VALUE_DESERIALIZER_CLASS_CONFIG,StringDeserializer.class);
        configs.put(ConsumerConfig.AUTO_OFFSET_RESET_CONFIG, "earliest");

        return new DefaultKafkaConsumerFactory<>(configs);
    }

    /**
     * 添加KafkaListenerContainerFactory，用于批量消费消息
     * @return
     */
    @Bean(name = "batchContainerFactory")
    public KafkaListenerContainerFactory<?> batchContainerFactory(@Qualifier("consumerFactory")ConsumerFactory<Object, Object> consumerFactory){
        ConcurrentKafkaListenerContainerFactory<Object, Object> containerFactory = new ConcurrentKafkaListenerContainerFactory<Object, Object>();
        containerFactory.setConsumerFactory(consumerFactory());
        containerFactory.setConcurrency(10);
        containerFactory.setBatchListener(true); //批量消费
        containerFactory.getContainerProperties().setAckMode(AbstractMessageListenerContainer.AckMode.MANUAL_IMMEDIATE);
        return containerFactory;
    }

    @Bean(name = "auditBatchContainerFactory")
    public KafkaListenerContainerFactory<?> auditBatchContainerFactory(@Qualifier("auditConsumerFactory")ConsumerFactory<Object, Object> consumerFactory){
        ConcurrentKafkaListenerContainerFactory<Object, Object> containerFactory = new ConcurrentKafkaListenerContainerFactory<Object, Object>();
        containerFactory.setConsumerFactory(auditConsumerFactory());
        containerFactory.setConcurrency(10);
        containerFactory.setBatchListener(true); //批量消费
        containerFactory.getContainerProperties().setAckMode(AbstractMessageListenerContainer.AckMode.MANUAL_IMMEDIATE);
        return containerFactory;
    }
}
