package com.cowell.bam.kafka;

import com.alibaba.fastjson.JSONObject;
import com.cowell.bam.domain.BamLogDO;
import com.cowell.bam.mq.MqProduct;
import com.cowell.bam.mq.RocketMqConfig;
import com.cowell.bam.service.BamBusinessBillLogService;
import com.cowell.bam.service.EsBdpDataService;
import com.cowell.bam.service.dto.MessageDto;
import com.cowell.bam.web.rest.util.StringUtil;
import com.google.common.collect.Lists;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.kafka.clients.consumer.KafkaConsumer;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.sleuth.annotation.NewSpan;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.kafka.support.Acknowledgment;
import org.springframework.messaging.Message;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.ZonedDateTime;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;


/**
 * <AUTHOR>
 * @date: 2018/12/27 16:52
 * @description:
 */
@Component
public class HanaBdpKafkaConsumer {

    private Logger log = LoggerFactory.getLogger(HanaBdpKafkaConsumer.class);
    private KafkaConsumer<String, String> consumer;
    @Value("${bam.kafka.group}")
    private String consumerGroupId;
    @Value("${spring.kafka.consumer.bootstrap-servers}")
    private String consumerBootstrapServers;
    @Value("${spring.kafka.consumer.key-deserializer}")
    private String consumerKeyDeserializer;
    @Value("${spring.kafka.consumer.value-deserializer}")
    private String consumerValueDeserializer;
    @Value("${bam.kafka.topic}")
    private String bdpKafaGoodsTopic;

    @Value("${apache.rocketmq.bam.topic}")
    private String bamLogTopic;
    @Autowired
    private EsBdpDataService esBdpDataService;
    @Autowired
    private BamBusinessBillLogService bamBusinessBillLogService;
    @Value("${data_source_env}")
    private String dataSourceEnv;
    @Autowired
    private MqProduct mqProduct;

    @KafkaListener(topics = {"${bam.kafka.topic}"}, containerFactory = "batchContainerFactory")
    @NewSpan("HanaBdpKafkaConsumer.kafkalistener")
    public void listener(Message<List<String>> message, Acknowledgment ack) {
        log.info("begin  Hanakafka listener");
        try {
            List<String> contents = message.getPayload();
            if (CollectionUtils.isEmpty(contents)) {
                ack.acknowledge();
                log.warn("Hanakafka message is null");
                return;
            }
            log.info("HanaBdpKafkaConsumer.listener|start|{}|{}", contents.size(), Thread.currentThread().getId());
            List<BamLogDO> bamLogDOList = new ArrayList<>();
            for (String content : contents) {
                if (org.apache.commons.lang3.StringUtils.isEmpty(content)) {
                    continue;
                }
                //log.info("HanaBdpKafkaConsumer#listener消息{}", content); 日志打的太大，暂时关一下

                BamLogDO bamLogDO = JSONObject.parseObject(content, BamLogDO.class);
                if (bamLogDO!= null && StringUtils.isNotEmpty(bamLogDO.getZGUID())) {
                    bamLogDO.setEnv(dataSourceEnv);
                    String timestamp = getTimestamp(bamLogDO.getZTIMESTAMP());
                    if(StringUtils.isNotEmpty(timestamp)){
                        bamLogDO.setLogtime(timestamp);
                    }
                    bamLogDOList.add(bamLogDO);
                }
            }
            if (CollectionUtils.isNotEmpty(bamLogDOList)) {
                List<List<BamLogDO>> sublist = Lists.partition(bamLogDOList, 200);
                for (List<BamLogDO> sub : sublist) {
                    MessageDto messageDto = new MessageDto();
                    messageDto.setId(ZonedDateTime.now().toInstant() + "");
                    messageDto.setTag(RocketMqConfig.BAM_LOG_TAG);
                    messageDto.setTopic(bamLogTopic);
                    messageDto.setObject(JSONObject.toJSONString(sub));
                    mqProduct.sendMessageAsync(messageDto);
                }

            }
            ack.acknowledge();
        } catch (Exception e) {
            log.error("HanaBdpKafkaConsumer#KafkaClientHelper.start", e);
        }
    }

    private String getTimestamp(String ztimestamp) throws ParseException {
        String timestamp = "";
        if(StringUtils.isEmpty(ztimestamp)){
            return timestamp;
        }

        if(ztimestamp.length()>=14){
            String substring = ztimestamp.substring(0, 14);
            SimpleDateFormat oldFormat = new SimpleDateFormat("yyyyMMddHHmmss");
            SimpleDateFormat newFormat = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ssXXX");
            Date date = null;
            try {
                date = oldFormat.parse(substring);
            } catch (ParseException e) {
                log.error("HanaBdpKafkaConsumer#KafkaClientHelper.getTimestamp", e);
            }
            timestamp = newFormat.format(date);
        }
        return timestamp;
    }

    @Transactional(rollbackFor = Exception.class)
    public void setHanaBdpData(List<BamLogDO> bamLogDOList) {
        //保存数据库
        bamBusinessBillLogService.batchInsert(bamLogDOList);
        //保存ES数据
        esBdpDataService.batchSave(bamLogDOList);
    }
}
