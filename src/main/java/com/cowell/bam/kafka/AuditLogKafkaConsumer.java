package com.cowell.bam.kafka;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.cowell.bam.domain.AuditLog;
import com.cowell.bam.domain.AuditUrlConfig;
import com.cowell.bam.domain.AuditUrlConfigExample;
import com.cowell.bam.repository.mybatis.dao.AuditLogMapper;
import com.cowell.bam.repository.mybatis.dao.AuditUrlConfigMapper;
import com.cowell.bam.security.oauth2.TokenAuthenticationManager;
import com.cowell.bam.service.dto.TokenUserDTO;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.ibatis.session.ExecutorType;
import org.apache.ibatis.session.SqlSession;
import org.mybatis.spring.SqlSessionTemplate;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.sleuth.annotation.NewSpan;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.kafka.support.Acknowledgment;
import org.springframework.messaging.Message;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.text.DateFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;


@Component
public class AuditLogKafkaConsumer {

    private Logger log = LoggerFactory.getLogger(AuditLogKafkaConsumer.class);
    @Value("${spring.kafka.auditlog-consumer.topic}")
    private String topic;
    @Value("${spring.kafka.auditlog-consumer.bootstrap-servers}")
    private String auditLogBootStrapServers;
    @Value("${spring.kafka.auditlog-consumer.group}")
    private String auditLogGroup;
    private static Map<String, String> operateModuleMap = new HashMap<>();
    private static Map<String, String> operateModule2Map = new HashMap<>();
    private static Set<String> systemNameSet = new HashSet<>();
    private static Map<String, String> systemNameMap = new HashMap<>();

    private static String GET = "GET";

    private static String STATUS = "200";

    private static Byte SUCCESS = 1;

    private static Byte FAIL = 0;

    @Autowired
    private AuditLogMapper auditLogMapper;
    @Autowired
    private AuditUrlConfigMapper auditUrlConfigMapper;
    @PostConstruct
    public void initUrlConfig(){
        log.info("审计日志初始化url配置");
        List<AuditUrlConfig> configs = auditUrlConfigMapper.selectByExample(new AuditUrlConfigExample());
        if(CollectionUtils.isNotEmpty(configs)){
            for(AuditUrlConfig config : configs){
                operateModuleMap.put(config.getUri(),config.getDescription());
                operateModule2Map.put(config.getUri(),config.getAppName().toUpperCase());
                systemNameSet.add(config.getAppName());
                systemNameMap.put(config.getAppName(),config.getAppName().toUpperCase());
            }
        }
        log.info("审计日志url配置{}",operateModuleMap);
        log.info("审计日志topic:{},{},{}",topic,auditLogBootStrapServers,auditLogGroup);

    }

    private TokenAuthenticationManager tokenAuthenticationManager;

    @KafkaListener(topics = {"${spring.kafka.auditlog-consumer.topic}"}, containerFactory = "auditBatchContainerFactory")
    @NewSpan("AuditKafkaConsumer.kafkalistener")
    public void listener(Message<List<String>> message, Acknowledgment ack) {
//        log.info("begin new AuditLogKafkaConsumer listener");
        List<String> contents = message.getPayload();
        if (CollectionUtils.isEmpty(contents)) {
            ack.acknowledge();
            log.warn("AuditLogKafkaConsumer message is null");
            return;
        }
        //log.info("new AuditLogKafkaConsumer.listener|start|{}|{}", contents.size(), Thread.currentThread().getId());
        List<AuditLog> auditLogs = new LinkedList<>();
        for (String content : contents) {
            if (StringUtils.isEmpty(content)) {
                continue;
            }
            JSONObject contentJsonObject = JSONObject.parseObject(content);

            String requestStr = contentJsonObject.getString("request");
            //log.info("contentJsonObject:"+ contentJsonObject);
            if (StringUtils.isEmpty(requestStr)) {
                continue;
            }
            String[] requestStrs = requestStr.split(" ");
            if (requestStrs.length < 2) {
                continue;
            }
            String method = requestStrs[0];

            String requestUrl = requestStrs[1];
            if (StringUtils.isEmpty(requestUrl)) {
                continue;
            }
            String[] splitUrl = requestUrl.split("\\/");
            if (splitUrl.length < 2) {
                continue;
            }
            String applicationName = splitUrl[1];
            String systemName = null;
//            if ((systemName = systemNameMap.get(applicationName)) == null) {
//                continue;
//            }
            //原逻辑未匹配到 匹配新逻辑
            String http_host = contentJsonObject.getString("http_host");
            String url = requestUrl.split("\\?")[0];
            if(StringUtils.isBlank(http_host)){
                continue;
            }
            if(http_host.contains("test") || http_host.contains("stage")){
                continue;
            }
            for(String name:systemNameSet){
                if(http_host.contains(name)){
                    systemName = systemNameMap.get(name);
                    break;
                }
            }
            systemName = systemName==null?operateModule2Map.get(url):systemName;
            if(systemName == null){
                continue;
            }

            String operateModule = null;
            //优先匹配 http_referrer
//            String http_referrer = contentJsonObject.getString("http_referrer");
//            if(StringUtils.isNotBlank(http_referrer)){
//                Integer index = StringUtils.ordinalIndexOf(http_referrer,"/",3);
//                if(index != -1){
//                    operateModule = operateModuleMap.get(http_referrer.substring(index));
//                }
//            }
            //未匹配到则匹配request

            if(StringUtils.isBlank(operateModule)){
                operateModule = operateModuleMap.get(url);
                if(StringUtils.isBlank(operateModule)){
                    //log.warn("AuditLogKafkaConsumer message is not match module;url:"+url);
                    continue;
                }
            }
            String status = contentJsonObject.getString("status");
            Byte result = null;
            if (status.equals(STATUS)) {
                result = SUCCESS;
            } else {
                result = FAIL;
            }

            String sourceIp = contentJsonObject.getString("remote_addr");
            String time =  contentJsonObject.getString("@timestamp");
            Date operateDate = null;
            try {
                operateDate = getDateFromRFC3339(time);
            } catch (ParseException e) {
                log.warn("审计日志转换时间失败:", e);
            }
            AuditLog auditLog = new AuditLog();
            String token = contentJsonObject.getString("authorization");
            if (StringUtils.isNotEmpty(token) && !token.equals("-")) {
                TokenUserDTO tokenUserDTO = null;
                try {
                    tokenUserDTO = tokenAuthenticationManager.getUserInfobyToken(token);
                } catch (Exception e) {
                    log.error("审计日志解析token失败:", e);
                }
                if (tokenUserDTO != null) {
                    Long userId = tokenUserDTO.getUserId();
                    String name = tokenUserDTO.getName();
                    String userName = tokenUserDTO.getUserName();
                    userName = name==null?userName:name;
                    auditLog.setUserId(String.valueOf(userId));
                    auditLog.setUserName(userName);
                    //log.info("auditlog-tokenUserDTO:{}", JSON.toJSONString(tokenUserDTO));
                    if(StringUtils.isBlank(userName)){
                        log.info("auditlog无用户名，tokenUserDTO:{}", JSON.toJSONString(tokenUserDTO));
                    }
                } else {
                    log.info("auditlog无用户，requestStr:{}", requestStr);
                    auditLog.setUserId(null);
                    auditLog.setUserName(null);
                    //continue;
                }
            }
            auditLog.setSourceIp(sourceIp);
            auditLog.setOperateDate(operateDate);
            auditLog.setOperateModule(operateModule);
            auditLog.setResult(result);
            auditLog.setUrl(url);
            auditLog.setSystemName(systemName);
            String userId = auditLog.getUserId();
            if(userId == null || StringUtils.isBlank(userId.trim())){
                log.info("auditLog:"+JSON.toJSONString(auditLog));
                continue;
            }
            //log.info("auditLog:"+JSON.toJSONString(auditLog));
            auditLogs.add(auditLog);
        }
        if (CollectionUtils.isNotEmpty(auditLogs)) {
            try {
                auditLogMapper.insertBatch(auditLogs);
            } catch (Exception e) {
                log.error("审计日志插入数据库失败:", e);
            }
        }
//        try {
//            sqlSession.commit();
//        } catch (Exception e) {
//            log.error("审计日志提交数据失败：", e);
//        }
        ack.acknowledge();
    }

//    private Date getDateFromTimestamp(Long timestamp) throws ParseException {
//        DateFormat format =  new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
//
//        String d = format.format(timestamp);
//        Date date=format.parse(d);
//        return date;
//    }

    private Date getDateFromRFC3339(String time) throws ParseException {
        DateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss.SSS'Z'");
        Date date = dateFormat.parse(time);

        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        calendar.set(Calendar.HOUR, calendar.get(Calendar.HOUR)+8);
        date = calendar.getTime();

        return date;
    }

}
