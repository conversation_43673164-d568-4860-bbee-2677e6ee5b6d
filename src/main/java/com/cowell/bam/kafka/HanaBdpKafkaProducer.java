package com.cowell.bam.kafka;

import com.alibaba.fastjson.JSON;
import com.cowell.bam.domain.HanaBdpDO;
import org.apache.kafka.clients.producer.KafkaProducer;
import org.apache.kafka.clients.producer.Producer;
import org.apache.kafka.clients.producer.ProducerRecord;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Properties;

/**
 * <AUTHOR>
 * @date: 2018/12/27 16:52
 * @description:
 */
@Component
public class HanaBdpKafkaProducer {

    private Logger log = LoggerFactory.getLogger(HanaBdpKafkaProducer.class);
    private Producer<String, String> producer;
    @Value("${spring.kafka.producer.bootstrap-servers}")
    private String producerBootstrapServers;
    @Value("${spring.kafka.producer.acks}")
    private String producerAcks;
    @Value("${spring.kafka.producer.retries}")
    private String producerRetries;
    @Value("${spring.kafka.producer.buffer-memory}")
    private String producerBufferMemory;
    @Value("${spring.kafka.producer.key-deserializer}")
    private String producerKeySerializer;
    @Value("${spring.kafka.producer.value-deserializer}")
    private String producerValueSerializer;
    @Value("${bam.kafka.topic}")
    private String producerTopic;
    public  void initKafkaConfig() {

        Properties props = new Properties();
        //broker地址
        props.put("bootstrap.servers", producerBootstrapServers);
        //请求时候需要验证
        props.put("acks", producerAcks);
        //请求失败时候需要重试
        props.put("retries", producerRetries);
        //内存缓存区大小
        props.put("buffer.memory", producerBufferMemory);
        //指定消息key序列化方式
        props.put("key.serializer", producerKeySerializer);
        //指定消息本身的序列化方式
        props.put("value.serializer", producerValueSerializer);

        producer = new KafkaProducer<>(props);
    }
    public void send(List<HanaBdpDO> hanaBdpDOList){
        try{
            initKafkaConfig();
            log.info("HanaBdpKafkaProducer#send批量发送kafka消息");
            for (HanaBdpDO bdpDO:hanaBdpDOList){
                String jsonString = JSON.toJSONString(bdpDO);
                //log.info("HanaBdpKafkaProducer#send发送kafka消息:key{}",bdpDO.getzGuId());
                producer.send(new ProducerRecord<>(producerTopic, bdpDO.getzGuId(),jsonString));

            }
        }catch (Exception e){
            log.warn("HanaBdpKafkaProducer#send发送消息失败",e);

        }
        producer.close();

    }
}
