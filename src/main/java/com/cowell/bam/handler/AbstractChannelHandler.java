package com.cowell.bam.handler;


/**
 * <AUTHOR>
 * @Description: 基础处理handler
 * @date 2018-07-09 09:45
 */
public abstract class AbstractChannelHandler<K, V> implements BaseChannelHandler<K, V> {


    /**
     * 逻辑处理之前的一些验证操作
     *
     * @param k
     */
    @Override
    public void before(K k) {}


    /**
     * 执行请求
     *
     * @param k
     */
    public V compare(K k) {
        before(k);
        V v = handler(k);
        after(k);
        return v;
    }

    /**
     * 判断与指定的handler知否匹配
     * @param operate handler类型
     * @return Boolean
     */
    public abstract Boolean isMatchHandler(Integer operate);



}
