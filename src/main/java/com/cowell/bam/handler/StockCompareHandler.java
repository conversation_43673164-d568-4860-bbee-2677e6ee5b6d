package com.cowell.bam.handler;

import com.cowell.bam.enums.SyncTypeEnum;
import com.cowell.bam.service.StockCompareService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @Description: 库存对比hanler
 * @date 2018-07-31 14:35
 */
@Component
public class StockCompareHandler extends AbstractChannelHandler <String, Boolean> {

    @Autowired
    private StockCompareService stockCompareService;

    /**
     * 京东获取店铺列表
     */
    private static final Integer STOCK_TYPE = SyncTypeEnum.STOCK.getCode();

    @Override
    public Boolean isMatchHandler(Integer type) {
        if (STOCK_TYPE.equals(type)) {
            return true;
        }
        return false;
    }

    @Override
    public Boolean handler(String comId) {
        return stockCompareService.compare(comId);
    }

    @Override
    public void after(String businessId) {

    }
}
