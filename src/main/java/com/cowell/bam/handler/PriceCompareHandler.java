package com.cowell.bam.handler;

import com.cowell.bam.enums.SyncTypeEnum;
import com.cowell.bam.service.PriceCompareService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @Description: 对比hanler
 * @date 2018-07-31 14:35
 */
@Component
public class PriceCompareHandler extends AbstractChannelHandler <String, Boolean> {

    @Autowired
    private PriceCompareService priceCompareService;

    /**
     * 京东获取店铺列表
     */
    private static final Integer PRICE_TYPE = SyncTypeEnum.PRICE.getCode();

    @Override
    public Boolean isMatchHandler(Integer type) {
        if (PRICE_TYPE.equals(type)) {
            return true;
        }
        return false;
    }

    @Override
    public Boolean handler(String comId) {
        return priceCompareService.compare(comId);
    }

    @Override
    public void after(String comId) {

    }
}
