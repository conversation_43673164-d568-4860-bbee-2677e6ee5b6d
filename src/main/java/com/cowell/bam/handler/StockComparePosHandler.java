package com.cowell.bam.handler;

import com.cowell.bam.enums.SyncTypeEnum;
import com.cowell.bam.service.StockCompareService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * 以中台为主，比对线下库存
 */
@Component
public class StockComparePosHandler extends AbstractChannelHandler <String, Boolean> {

    @Autowired
    private StockCompareService stockCompareService;

    /**
     * 京东获取店铺列表
     */
    private static final Integer STOCK_TYPE = SyncTypeEnum.STOCK_POS.getCode();

    @Override
    public Boolean isMatchHandler(Integer type) {
        if (STOCK_TYPE.equals(type)) {
            return true;
        }
        return false;
    }

    @Override
    public Boolean handler(String comId) {
        return stockCompareService.comparePos(comId);
    }

    @Override
    public void after(String businessId) {

    }
}
