package com.cowell.bam.handler;

import com.cowell.bam.enums.SyncTypeEnum;
import com.cowell.bam.service.StockTransitCompareService;
import com.cowell.bam.service.dto.CompareTransitMessageDTO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * 在途库存比较job
 * <AUTHOR>
 * @date 2022/10/19 14:29
 */
@Component
public class StockCompareTransitHandler extends AbstractChannelHandler <CompareTransitMessageDTO, Boolean> {

    @Autowired
    private StockTransitCompareService stockTransitCompareService;

    /**
     * 京东获取店铺列表
     */
    private static final Integer STOCK_TYPE = SyncTypeEnum.STOCK_TRANSIT.getCode();

    @Override
    public Boolean isMatchHandler(Integer type) {
        if (STOCK_TYPE.equals(type)) {
            return true;
        }
        return false;
    }

    @Override
    public Boolean handler(CompareTransitMessageDTO messageDTO) {
        return stockTransitCompareService.compareTransit(messageDTO.getComId(), messageDTO.getVersion(), messageDTO.getDate());
    }

    @Override
    public void after(CompareTransitMessageDTO messageDTO) {

    }

}
