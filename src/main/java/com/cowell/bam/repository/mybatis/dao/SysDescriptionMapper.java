package com.cowell.bam.repository.mybatis.dao;

import com.cowell.bam.entity.SysDescription;
import com.cowell.bam.entity.SysDescriptionExample;
import java.util.List;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
@Mapper
public interface SysDescriptionMapper {
    long countByExample(SysDescriptionExample example);

    int deleteByExample(SysDescriptionExample example);

    int insert(SysDescription record);

    int insertSelective(SysDescription record);

    List<SysDescription> selectByExample(SysDescriptionExample example);

    int updateByExampleSelective(@Param("record") SysDescription record, @Param("example") SysDescriptionExample example);

    int updateByExample(@Param("record") SysDescription record, @Param("example") SysDescriptionExample example);
}
