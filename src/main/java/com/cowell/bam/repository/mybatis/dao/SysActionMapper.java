package com.cowell.bam.repository.mybatis.dao;

import com.cowell.bam.entity.SysAction;
import com.cowell.bam.entity.SysActionExample;
import java.util.List;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
@Mapper
public interface SysActionMapper {
    long countByExample(SysActionExample example);

    int deleteByExample(SysActionExample example);

    int insert(SysAction record);

    int insertSelective(SysAction record);

    List<SysAction> selectByExample(SysActionExample example);

    int updateByExampleSelective(@Param("record") SysAction record, @Param("example") SysActionExample example);

    int updateByExample(@Param("record") SysAction record, @Param("example") SysActionExample example);
}
