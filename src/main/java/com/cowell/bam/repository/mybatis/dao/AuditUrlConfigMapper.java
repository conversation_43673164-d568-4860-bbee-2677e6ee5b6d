package com.cowell.bam.repository.mybatis.dao;

import com.cowell.bam.domain.AuditUrlConfig;
import com.cowell.bam.domain.AuditUrlConfigExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface AuditUrlConfigMapper {
    long countByExample(AuditUrlConfigExample example);

    int deleteByExample(AuditUrlConfigExample example);

    int deleteByPrimaryKey(Integer id);

    int insert(AuditUrlConfig record);

    int insertSelective(AuditUrlConfig record);

    List<AuditUrlConfig> selectByExample(AuditUrlConfigExample example);

    AuditUrlConfig selectByPrimaryKey(Integer id);

    int updateByExampleSelective(@Param("record") AuditUrlConfig record, @Param("example") AuditUrlConfigExample example);

    int updateByExample(@Param("record") AuditUrlConfig record, @Param("example") AuditUrlConfigExample example);

    int updateByPrimaryKeySelective(AuditUrlConfig record);

    int updateByPrimaryKey(AuditUrlConfig record);
}