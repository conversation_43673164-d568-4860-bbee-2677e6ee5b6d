package com.cowell.bam.repository.mybatis.dao;

import com.cowell.bam.domain.AuditLog;
import com.cowell.bam.domain.AuditLogExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface AuditLogMapper {
    long countByExample(AuditLogExample example);

    int deleteByExample(AuditLogExample example);

    int deleteByPrimaryKey(Long id);

    int insert(AuditLog record);

    int insertSelective(AuditLog record);

    int insertBatch(List<AuditLog> records);

    List<AuditLog> selectByExample(AuditLogExample example);

    AuditLog selectByPrimaryKey(Long id);

    int updateByExampleSelective(@Param("record") AuditLog record, @Param("example") AuditLogExample example);

    int updateByExample(@Param("record") AuditLog record, @Param("example") AuditLogExample example);

    int updateByPrimaryKeySelective(AuditLog record);

    int updateByPrimaryKey(AuditLog record);
}
