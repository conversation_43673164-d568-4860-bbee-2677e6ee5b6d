package com.cowell.bam.repository.mybatis.dao;

import com.cowell.bam.domain.BamBusinessBillLogDO;
import com.cowell.bam.domain.BamQueryDO;
import com.cowell.bam.domain.BamReturnDO;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

@Mapper
public interface BamBusinessBillLogDOMapper {
    int deleteByPrimaryKey(Long id);

    int insert(BamBusinessBillLogDO record);

    int batchInsert(List<BamBusinessBillLogDO> record);

    BamBusinessBillLogDO selectByPrimaryKey(Long id);

    List<String> selectTop5ByCompanyAndStoreId(BamQueryDO bamQueryDO);

    List<BamBusinessBillLogDO> selectByBillId(String billId);

}
