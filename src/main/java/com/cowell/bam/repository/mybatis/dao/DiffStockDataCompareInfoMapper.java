package com.cowell.bam.repository.mybatis.dao;

import com.cowell.bam.domain.DiffStockDataCompareInfo;
import com.cowell.bam.domain.DiffStockDataCompareInfoExample;
import com.cowell.bam.domain.DiffStockDataCompareInfoWithBLOBs;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface DiffStockDataCompareInfoMapper {
    int countByExample(DiffStockDataCompareInfoExample example);

    int deleteByExample(DiffStockDataCompareInfoExample example);

    int deleteByPrimaryKey(Long id);

    int insert(DiffStockDataCompareInfoWithBLOBs record);

    int insertSelective(DiffStockDataCompareInfoWithBLOBs record);

    List<DiffStockDataCompareInfoWithBLOBs> selectByExampleWithBLOBs(DiffStockDataCompareInfoExample example);

    List<DiffStockDataCompareInfo> selectByExample(DiffStockDataCompareInfoExample example);

    DiffStockDataCompareInfoWithBLOBs selectByPrimaryKey(Long id);

    int updateByExampleSelective(@Param("record") DiffStockDataCompareInfoWithBLOBs record, @Param("example") DiffStockDataCompareInfoExample example);

    int updateByExampleWithBLOBs(@Param("record") DiffStockDataCompareInfoWithBLOBs record, @Param("example") DiffStockDataCompareInfoExample example);

    int updateByExample(@Param("record") DiffStockDataCompareInfo record, @Param("example") DiffStockDataCompareInfoExample example);

    int updateByPrimaryKeySelective(DiffStockDataCompareInfoWithBLOBs record);

    int updateByPrimaryKeyWithBLOBs(DiffStockDataCompareInfoWithBLOBs record);

    int updateByPrimaryKey(DiffStockDataCompareInfo record);
}
