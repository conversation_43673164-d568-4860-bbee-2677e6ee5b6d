package com.cowell.bam.repository.mybatis.dao;

import com.cowell.bam.domain.DiffDataCompareInfo;
import com.cowell.bam.domain.DiffDataCompareInfoExample;
import com.cowell.bam.domain.DiffDataCompareInfoWithBLOBs;
import com.cowell.bam.query.CompareDataQuery;
import com.cowell.bam.service.dto.DiffCollectDataResponseDTO;
import com.cowell.bam.service.dto.DiffDataRequestDTO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface DiffDataCompareInfoMapper {

    int countByExample(DiffDataCompareInfoExample example);

    int deleteByExample(DiffDataCompareInfoExample example);

    int deleteByDateExample(DiffDataCompareInfoExample example);

    int deleteByPrimaryKey(Long id);

    int insert(DiffDataCompareInfoWithBLOBs record);

    int insertSelective(DiffDataCompareInfoWithBLOBs record);

    List<DiffDataCompareInfoWithBLOBs> selectByExampleWithBLOBs(DiffDataCompareInfoExample example);

    List<DiffDataCompareInfo> selectByExample(DiffDataCompareInfoExample example);

    DiffDataCompareInfoWithBLOBs selectByPrimaryKey(Long id);

    List<DiffCollectDataResponseDTO> selectDiffCollect(DiffDataRequestDTO diffDataRequestDTO);

    List<DiffCollectDataResponseDTO> selectDiffData(DiffDataRequestDTO diffDataRequestDTO);

    List<DiffCollectDataResponseDTO> selectTransitDiffCollect(DiffDataRequestDTO diffDataRequestDTO);

    List<Long> selectTransitDiffBusiness(DiffDataRequestDTO diffDataRequestDTO);

    Integer selectMaxVersionDay(DiffDataRequestDTO diffDataRequestDTO);

    int updateByExampleSelective(@Param("record") DiffDataCompareInfoWithBLOBs record, @Param("example") DiffDataCompareInfoExample example);

    int updateByExampleWithBLOBs(@Param("record") DiffDataCompareInfoWithBLOBs record, @Param("example") DiffDataCompareInfoExample example);

    int updateByExample(@Param("record") DiffDataCompareInfo record, @Param("example") DiffDataCompareInfoExample example);

    int updateByPrimaryKeySelective(DiffDataCompareInfoWithBLOBs record);

    int updateByPrimaryKeyWithBLOBs(DiffDataCompareInfoWithBLOBs record);

    int updateByPrimaryKey(DiffDataCompareInfo record);

    int batchInsert(@Param("list") List<DiffDataCompareInfoWithBLOBs> list);

    /**
     * 查找历史对比结果
     * @param dataQuery
     * @return
     */
    List<Long> queryHistoryCompareRecords(CompareDataQuery dataQuery);

    List<DiffDataCompareInfo> selectDiffDetail(DiffDataRequestDTO diffDataRequestDTO);


}
