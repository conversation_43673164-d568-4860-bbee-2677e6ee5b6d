package com.cowell.bam.repository;

import com.cowell.bam.domain.HanaBdpDO;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.lang.reflect.Field;
import java.sql.*;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date: 2018/12/27 14:47
 * @description:
 */
@Component
public class JdbcHanaRepository {
    private final Logger log = LoggerFactory.getLogger(JdbcHanaRepository.class);
    @Value("${spring.HanaDatasource.driverClassName}")
    private String driver;
    @Value("${spring.HanaDatasource.url}")
    private String url;
    @Value("${spring.HanaDatasource.username}")
    private String username;
    @Value("${spring.HanaDatasource.password}")
    private String password;

    public JdbcHanaRepository() {

    }

    public List<HanaBdpDO> select(String startTime, String endTime,Integer pageNum,Integer pageSize) throws Exception {
        Connection con = this.getConnection(driver, url, username, password);
        PreparedStatement pstmt = null;
        List<HanaBdpDO> list = new ArrayList();
        try {
            StringBuilder sqlSbd = new StringBuilder();
            sqlSbd.append("SELECT MANDT, ZGUID, ZTRACEID, ZFLOWID, ZSTEPID, ZSENDSYSID, ZRECSYSID, ZCOMID, ZSTOREID, ZTIMESTAMP, ZBUSINESSDATE, ZITEMCOUNT, ZSTATUS, ZMESSAGE, ZEXTEND1, ZEXTEND2, ZEXTEND3, ZEXTEND4\n" +
                "FROM SAPABAP1.ZBCT0008 WHERE ZTIMESTAMP<=? ");
            if (StringUtils.isNotEmpty(startTime)) {
                sqlSbd.append(" AND  ZTIMESTAMP>=?");
            }
            sqlSbd.append("limit ? OFFSET ?");
            pstmt = con.prepareStatement(sqlSbd.toString());
            int count =1;
            pstmt.setString(count, endTime);
            if (StringUtils.isNotEmpty(startTime)) {
                count = count+1;
                pstmt.setString(count, startTime);
            }

            pstmt.setInt(count+1,pageSize);
            pstmt.setInt(count+2,pageNum);
            ResultSet rs = pstmt.executeQuery();
            list =  this.processResult(rs);
        } catch (Exception e) {
            e.printStackTrace();
            log.error("JdbcHanaRepository#select{}" + e);
        } finally {
            this.closeConnection(con, pstmt);
        }
        return list;
    }

    private List<HanaBdpDO> processResult(ResultSet rs) throws Exception {
        //返回结果的列表集合
        List<HanaBdpDO> list = new ArrayList();
        try {
            ResultSetMetaData rsmd = rs.getMetaData();
            int colNum = rsmd.getColumnCount();
            //业务对象的属性数组
            Field[] fields = HanaBdpDO.class.getDeclaredFields();
            //对每一条记录进行操作
            while (rs.next()) {
                HanaBdpDO obj = HanaBdpDO.class.newInstance();
                //将每一个字段取出进行赋值
                for (int i = 1; i <= colNum; i++) {
                    Object value = rs.getObject(i);
                    //寻找该列对应的对象属性
                    for (int j = 0; j < fields.length; j++) {
                        Field f = fields[j];
                        //如果匹配进行赋值
                        String columnName = rsmd.getColumnName(i);
                        String nameUpperCase = f.getName().toUpperCase();
                        if (nameUpperCase.equalsIgnoreCase(columnName.toUpperCase())) {
                            boolean flag = f.isAccessible();
                            f.setAccessible(true);
                            if(nameUpperCase.equals("ZSENDSYSID") || nameUpperCase.equals("ZRECSYSID")){
                                value = String.valueOf(value);
                            }
                            f.set(obj, value);
                            f.setAccessible(flag);
                        }
                    }
                }
                list.add(obj);
            }
        } catch (Exception e) {
            log.warn("JdbcHanaRepository#processResult转换失败",e);
        }
        return list;
    }

    private Connection getConnection(String driver, String url, String user,
                                     String password) throws Exception {
        Class.forName(driver);
        return DriverManager.getConnection(url, user, password);

    }

    private void closeConnection(Connection con, Statement stmt)
        throws Exception {
        if (stmt != null) {
            stmt.close();
        }
        if (con != null) {
            con.close();
        }
    }

    public Long selectCount() throws Exception {
        Connection con = this.getConnection(driver, url, username, password);
        PreparedStatement pstmt = null;
        Long count = 0L;
        List<HanaBdpDO> list = new ArrayList();
        try {
            StringBuilder sqlSbd = new StringBuilder();
            sqlSbd.append("SELECT count(MANDT) FROM SAPABAP1.ZBCT0008");
            pstmt = con.prepareStatement(sqlSbd.toString());
            ResultSet rs = pstmt.executeQuery();
            rs.next();
            count = rs.getLong(1);
        } catch (Exception e) {
            log.error("JdbcHanaRepository#select{}" + e.getMessage());
        } finally {
            this.closeConnection(con, pstmt);
        }
        return count;
    }
}
