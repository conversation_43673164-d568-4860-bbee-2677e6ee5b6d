
package com.cowell.bam.xxlJob;

import com.cowell.bam.service.IThirdPlatformService;
import com.cowell.bam.service.StockCompareService;
import com.cowell.bam.service.dto.ThirdPlatformStockDTO;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHandler;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.sleuth.annotation.NewSpan;
import org.springframework.stereotype.Component;

import static com.cowell.bam.enums.NotifySyncStockTypeEnum.STOCK_CHECK;

/**
 * <AUTHOR>
 * @date: 2020/09/14 15:49
 * @description: 定时通知SAP推送库存数据 进行对比 (截止2025-08-05 prod目前参数:3116)
 */

@Slf4j
@JobHandler(value = "notifySAPSendStockByStoreNoJobHandler")
@Component
public class NotifySAPSendStockByStoreNoJobHandler extends IJobHandler {

    @Autowired
    private StockCompareService stockCompareService;

    @Override
    @NewSpan("notifySAPSendStockByStoreNoJobHandler")
    public ReturnT<String> execute(String param) {

        try {
            log.info("NotifySAPSendStockByStoreNoJobHandler|execute|通知SAP推送库存数据开始|param={}", param);
            if (StringUtils.isEmpty(param)) {
                return ReturnT.SUCCESS;
            }
            for (String storeNo : StringUtils.split(param, ",")) {
                stockCompareService.notifySAPSendStockByStoreNo(storeNo);
            }
        } catch (Exception e) {
            ReturnT.FAIL.setMsg(e.getMessage());
            return ReturnT.FAIL;
        }
        return ReturnT.SUCCESS;
    }
}
