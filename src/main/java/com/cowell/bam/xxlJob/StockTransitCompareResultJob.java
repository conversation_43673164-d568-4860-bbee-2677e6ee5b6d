package com.cowell.bam.xxlJob;

import com.cowell.bam.enums.SyncTypeEnum;
import com.cowell.bam.service.DataCompareService;
import com.cowell.bam.service.StockTransitCompareService;
import com.cowell.bam.service.dto.DiffDataRequestDTO;
import com.cowell.bam.web.rest.util.ConstantPool;
import com.cowell.bam.web.rest.util.DateUtil;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHandler;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.sleuth.annotation.NewSpan;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.List;

/**
 * 发送在途库存差异邮件
 * <AUTHOR>
 * @date 2022/10/24 17:08
 */

@JobHandler(value = "stockTransitCompareResultJob")
@Component
public class StockTransitCompareResultJob extends IJobHandler {
    private static final Logger log = LoggerFactory.getLogger(StockTransitCompareResultJob.class);

    @Autowired
    private DataCompareService dataCompareService;
    @Autowired
    private StockTransitCompareService stockTransitCompareService;

    @Override
    @NewSpan("stockTransitCompareResultJob")
    public ReturnT<String> execute(String jobConfig)  {

        DiffDataRequestDTO diffDataRequestDTO = new DiffDataRequestDTO();
        String date = DateUtil.dateToStr(new Date(), DateUtil.DATE_FORMAT);
        String toUsers = jobConfig;
        if(jobConfig.contains(":")){
            String paramDate = jobConfig.split(":")[0];
            if(!ConstantPool.TRANSIT_ALL_DATE.equals(paramDate)){
                date = paramDate;
            }
            diffDataRequestDTO.setVersion(Integer.parseInt(jobConfig.split(":")[1]));
            toUsers = jobConfig.split(":")[2];
        }

        diffDataRequestDTO.setDataType(SyncTypeEnum.STOCK_TRANSIT.getCode());
        diffDataRequestDTO.setTime(date);

        try {
            log.info("[stockTransitCompareResultJob]|execute|param:{}.toUsers:{}.", diffDataRequestDTO, toUsers);
            List<Long> businessIdList = dataCompareService.getTransitDiffBusiness(diffDataRequestDTO);
            log.info("[stockTransitCompareResultJob]|execute|businessIdList:{}!", businessIdList);
            stockTransitCompareService.sendDifferEmailAndFile(diffDataRequestDTO, date, toUsers, businessIdList);
        } catch (Exception e) {
            log.warn("stockTransitCompareResultJob|fail.", e);
            return ReturnT.FAIL;
        }
        return ReturnT.SUCCESS;
    }

}
