
package com.cowell.bam.xxlJob;

import com.alibaba.fastjson.JSONObject;
import com.cowell.bam.common.TracerBean;
import com.cowell.bam.service.PriceCenterCompareService;
import com.cowell.bam.service.PriceCompareAppointStoreService;
import com.google.common.collect.Lists;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHandler;
import org.apache.commons.lang.StringUtils;
import org.redisson.api.RBucket;
import org.redisson.api.RedissonClient;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.sleuth.Span;
import org.springframework.cloud.sleuth.annotation.NewSpan;
import org.springframework.core.task.AsyncTaskExecutor;
import org.springframework.stereotype.Component;

import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date: 2020/05/23 00:24
 * @description: 价格对比根据连锁和时间点  (截止2025-08-05 xxl停止状态)
 *
 * param 样式：comId1:busNo1,busNo2;comId2:busNo1,busNo2;
 */

@JobHandler(value = "ComparePriceAppointTimeJob")
@Component
public class ComparePriceAppointTimeJobService extends IJobHandler {
    private Logger log = LoggerFactory.getLogger(ComparePriceAppointTimeJobService.class);

    @Autowired
    private TracerBean tracerBean;
    @Autowired
    @Qualifier("taskExecutorTrace3")
    private AsyncTaskExecutor asyncTaskExecutor3;
    @Autowired
    private PriceCenterCompareService priceCenterCompareService;
    @Value("${appoint.week.time.compare.price}")
    private String priceCompareAppointTimeComId;

    @Override
    @NewSpan("ComparePriceAppointTimeJobService")
    public ReturnT<String> execute(String s){
        Span span = tracerBean.startSpan();
        try {
            log.info("ComparePriceAppointTimeJobService|execute|比对功能开始运行参数：{}", s);

            //获取连锁和门店的对应关系
            String comIdArr = getComIdAndBusNo(s);

            if (StringUtils.isBlank(comIdArr)){
                return ReturnT.SUCCESS;
            }
            String[] paramArr = StringUtils.split(comIdArr, ",");
            for (String param : paramArr) {
                asyncTaskExecutor3.execute(() -> {
                    log.info("ComparePriceAppointTimeJobService|execute|连锁{}比对开始", param);
                    priceCenterCompareService.priceCenterCompareFromComId(param);
                });
            }
        } catch (Exception e) {
            ReturnT.FAIL.setMsg(e.getMessage());
            return ReturnT.FAIL;
        }finally {
            tracerBean.close(span);
        }
        return ReturnT.SUCCESS;
    }



    private String getComIdAndBusNo(String s){
        log.info("定时刷新门店价格信息 参数:{} 配置:{}",s,priceCompareAppointTimeComId);
        if (StringUtils.isBlank(priceCompareAppointTimeComId)){
            return null;
        }

        if (StringUtils.isNotBlank(s)){
            return s;
        }

        return this.getCurrentWeekBusinessConfig();
    }

    private String getCurrentWeekBusinessConfig(){
        //配置信息
        String[] comIdArr = priceCompareAppointTimeComId.replaceAll("\\s*|\\t|\\r|\\n", "").replace("，", ",").split(";");
        //当前 周
        String curWeek = getCurrentWeek();
        //当前 时
        SimpleDateFormat sdf =new SimpleDateFormat("HH");
        String appointHour = "time"+sdf.format(new Date());
        //需要执行的连锁配置信息
        StringBuilder needCompareCompId = new StringBuilder();
        for (String comIdStr : comIdArr){
            String key = curWeek+":"+appointHour;
            if (comIdStr.contains(key)){
                String[] curConfig = comIdStr.split(":");
                needCompareCompId.append(curConfig[2]);
            }
        }
        log.warn("需要执行的连锁MDM编码:{}",needCompareCompId.toString());
        return needCompareCompId.toString();
    }


    private String getCurrentWeek(){
        String[] weekDays = {"sunday" ,"monday", "tuesday", "wednesday", "thursday", "friday", "saturday"};
        Calendar cal = Calendar.getInstance();
        cal.setTime(new Date());
        int w = cal.get(Calendar.DAY_OF_WEEK) - 1;
        if (w < 0){
            w = 0;
        }
        return weekDays[w];
    }



}
