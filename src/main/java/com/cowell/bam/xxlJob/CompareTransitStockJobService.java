package com.cowell.bam.xxlJob;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.cowell.bam.common.TracerBean;
import com.cowell.bam.enums.SyncTypeEnum;
import com.cowell.bam.mq.CompareTransitStockProducer;
import com.cowell.bam.service.INyuwaFeign;
import com.cowell.bam.service.IRedissonCacheService;
import com.cowell.bam.service.dto.CompareTransitMessageDTO;
import com.cowell.bam.service.dto.MdmStoreBaseDTO;
import com.cowell.bam.service.dto.StoreApplyDateDTO;
import com.cowell.bam.service.impl.ThirdService;
import com.cowell.bam.web.rest.util.ConstantPool;
import com.cowell.bam.web.rest.util.RedisKeysConstant;
import com.google.common.collect.Sets;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHandler;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.joda.time.LocalDate;
import org.redisson.api.RLock;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.sleuth.Span;
import org.springframework.cloud.sleuth.annotation.NewSpan;
import org.springframework.stereotype.Component;

import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * 在途库存比对
 * <AUTHOR>
 * @date 2022/10/24 21:03
 */
@JobHandler(value = "CompareTransitStockJobService")
@Component
public class CompareTransitStockJobService extends IJobHandler {
    private Logger log = LoggerFactory.getLogger(CompareTransitStockJobService.class);

    @Autowired
    private TracerBean tracerBean;
    @Autowired
    private IRedissonCacheService redissonCacheService;
    @Autowired
    private CompareTransitStockProducer compareTransitStockProducer;
    @Autowired
    private ThirdService thirdService;

    @Override
    @NewSpan("CompareTransitStockJobService")
    public ReturnT<String> execute(String s){
        Span span = tracerBean.startSpan();
        try {
            log.info("CompareTransitStockJobService|execute|比对功能开始运行参数：{}", s);
            if (StringUtils.isEmpty(s)) {
                return ReturnT.SUCCESS;
            }
            String[] comIdArr = StringUtils.split(s, ",");
            String date = new LocalDate().toString(ConstantPool.YYYYMMDD);
            Integer version = redissonCacheService.versionIncr(SyncTypeEnum.STOCK_TRANSIT.getCode(), date);
            log.info("CompareTransitStockJobService|version:{}.date:{}.", version, date);
            HashSet<String> storeApplyDateSet = thirdService.queryStoreApplyDateSet(date);
            if (CollectionUtils.isEmpty(storeApplyDateSet)) {
                log.error("CompareTransitStockJobService|获取门店请货日期失败 date:{}", date);
                return ReturnT.FAIL;
            }
            for (String comId : comIdArr) {
                CompareTransitMessageDTO messageDTO = new CompareTransitMessageDTO(comId, version, date);
                compareTransitStockProducer.sendMessage(JSON.toJSONString(messageDTO));
            }
        } catch (Exception e) {
            ReturnT.FAIL.setMsg(e.getMessage());
            return ReturnT.FAIL;
        }finally {
            tracerBean.close(span);
        }
        return ReturnT.SUCCESS;
    }
}
