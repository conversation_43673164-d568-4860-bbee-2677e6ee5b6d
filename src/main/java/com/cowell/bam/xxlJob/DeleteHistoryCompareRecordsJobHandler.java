package com.cowell.bam.xxlJob;

import com.alibaba.fastjson.JSONObject;
import com.cowell.bam.domain.DiffDataCompareInfoExample;
import com.cowell.bam.query.CompareDataQuery;
import com.cowell.bam.repository.mybatis.dao.DiffDataCompareInfoMapper;
import com.cowell.bam.web.rest.util.DateUtil;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHandler;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.sleuth.annotation.NewSpan;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.*;


/**
 * 每天凌晨删除前几天对比的数据
 */

@Slf4j
@JobHandler(value = "deleteHistoryCompareRecordsJobHandler")
@Component
public class DeleteHistoryCompareRecordsJobHandler extends <PERSON><PERSON>ob<PERSON>andler {

    private static final Integer DEFAULT_PAGE_SIZE = 1000;

    private static final String START_DATE_FIELD = "startDate";

    private static final String END_DATE_FIELD = "endDate";

    private static final String PREV_DAY_FIELD = "prevDay";

    private static final String PAGE_SIZE_FIELD = "pageSize";

    private static final String DATA_TYPE_FIELD = "dataType";

    private static final Integer DEFAULT_PREV_DAY = 7;


    @Autowired
    private DiffDataCompareInfoMapper diffDataCompareInfoMapper;

    /**
     * 根据配置生成请求参数
     *
     * @param param
     * @return
     */
    private CompareDataQuery getQueryInfo(String param) {
        CompareDataQuery queryInfo = new CompareDataQuery();
        JSONObject jsonObject;
        try {
            jsonObject = JSONObject.parseObject(param);
        } catch (Exception e) {
            log.error("DeleteHistoryCompareRecordsJobHandler|getQueryInfo|json解析错误|param={}", param, e);
            jsonObject = new JSONObject();
        }
        Integer preDays = jsonObject.getInteger(PREV_DAY_FIELD);

        queryInfo.setEndDate(DateUtil.parse(jsonObject.getString(END_DATE_FIELD), DateUtil.TIME_FORMAT));
        queryInfo.setStartDate(DateUtil.parse(jsonObject.getString(START_DATE_FIELD), DateUtil.TIME_FORMAT));

        if (Objects.isNull(queryInfo.getEndDate())) {
            queryInfo.setEndDate(DateUtil.getPreviousDay(new Date(), Objects.nonNull(preDays) ? preDays : DEFAULT_PREV_DAY));
        }

        Integer dataType = jsonObject.getInteger(DATA_TYPE_FIELD);
        dataType = Objects.isNull(dataType) ? 0 : dataType;

        queryInfo.setDataType(dataType);
        queryInfo.setPageSize(Optional.ofNullable(jsonObject.getInteger(PAGE_SIZE_FIELD)).orElse(DEFAULT_PAGE_SIZE));
        queryInfo.setUuid(UUID.randomUUID().toString());
        return queryInfo;
    }

    @Override
    @NewSpan
    public ReturnT<String> execute(String param) {

        long start = System.currentTimeMillis();
        CompareDataQuery query = getQueryInfo(param);
        log.info("DeleteHistoryCompareRecordsJobHandler|execute|删除历史对比数据开始|param:{},start={}", query, start);

        int times = 0;
        List<Long> recordIds = diffDataCompareInfoMapper.queryHistoryCompareRecords(query);
        if (CollectionUtils.isEmpty(recordIds)) {
            log.info("DeleteHistoryCompareRecordsJobHandler|execute|未找到要删除的数据|param:{},use={}", param, System.currentTimeMillis() - start);
            return ReturnT.SUCCESS;
        }

        while (!CollectionUtils.isEmpty(recordIds)) {

            DiffDataCompareInfoExample example = new DiffDataCompareInfoExample();
            example.createCriteria().andIdIn(recordIds);
            diffDataCompareInfoMapper.deleteByExample(example);
            log.info("DeleteHistoryCompareRecordsJobHandler|execute|删除历史对比数据开始|uuid={},times={},size={}",
                query.getUuid(), times, recordIds.size());

            recordIds = diffDataCompareInfoMapper.queryHistoryCompareRecords(query);
            times++;
        }
        log.info("DeleteHistoryCompareRecordsJobHandler|execute|删除历史对比数据结束|param:{},use={}", param, System.currentTimeMillis() - start);
        return ReturnT.SUCCESS;
    }
}
