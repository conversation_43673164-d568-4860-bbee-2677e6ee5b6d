
package com.cowell.bam.xxlJob;

import com.alibaba.fastjson.JSONObject;
import com.cowell.bam.domain.DiffDataCompareInfo;
import com.cowell.bam.domain.DiffDataCompareInfoExample;
import com.cowell.bam.repository.mybatis.dao.DiffDataCompareInfoMapper;
import com.cowell.bam.service.PriceCenterCompareService;
import com.cowell.bam.service.impl.ThirdService;
import com.google.common.collect.Lists;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHandler;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.redisson.api.RBucket;
import org.redisson.api.RedissonClient;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.cloud.sleuth.annotation.NewSpan;
import org.springframework.core.task.AsyncTaskExecutor;
import org.springframework.stereotype.Component;

import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date: 2020/05/23 00:24
 * @description:  删除对比历史数据 (截止2025-08-05 xxl停止状态)
 *
 * param 样式：comId1:busNo1,busNo2;comId2:busNo1,busNo2;
 */

@JobHandler(value = "ComparePriceDeleteOldDataJob")
@Component
public class ComparePriceDeleteOldDataJobService extends IJobHandler {
    private Logger log = LoggerFactory.getLogger(ComparePriceDeleteOldDataJobService.class);


    @Autowired
    private DiffDataCompareInfoMapper diffDataCompareInfoMapper;

    @Override
    @NewSpan("ComparePriceDeleteOldDataJobService")
    public ReturnT<String> execute(String s){
        try {
            log.info("ComparePriceDeleteOldDataJobService|execute|删除历史数据运行参数：{}", s);

            //获取当前时间的 前三天
            Calendar curr = Calendar.getInstance();
            curr.set(Calendar.DAY_OF_MONTH,curr.get(Calendar.DAY_OF_MONTH)-3);
            Date date=curr.getTime();

            DiffDataCompareInfoExample example = new  DiffDataCompareInfoExample();
            example.createCriteria().andGmtCreateLessThanOrEqualTo(date);

            int result = diffDataCompareInfoMapper.deleteByDateExample(example);

            log.info("ComparePriceDeleteOldDataJobService|execute|删除历史数据运行参数 成功条数：{}", result);
        } catch (Exception e) {
            ReturnT.FAIL.setMsg(e.getMessage());
            return ReturnT.FAIL;
        }

        return ReturnT.SUCCESS;
    }
}
