
package com.cowell.bam.xxlJob;

import com.cowell.bam.common.TracerBean;
import com.cowell.bam.enums.SyncTypeEnum;
import com.cowell.bam.handler.AbstractChannelHandler;
import com.cowell.bam.web.rest.BaseController;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHandler;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.cloud.sleuth.Span;
import org.springframework.cloud.sleuth.annotation.NewSpan;
import org.springframework.core.task.AsyncTaskExecutor;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

/**
 * <AUTHOR>
 * @date: 2018/12/29 19:27
 * @description:  价格比对  (截止2025-08-05 xxlxxl停止状态)
 */

@JobHandler(value = "ComparePriceJob")
@Component
public class ComparePriceJobService extends IJobHandler {
    private Logger log = LoggerFactory.getLogger(ComparePriceJobService.class);

    @Autowired
    private BaseController baseController;

    @Autowired
    private List<AbstractChannelHandler> handlerList;


    ExecutorService executorService = Executors.newFixedThreadPool(2);

    @Autowired
    private TracerBean tracerBean;


    @Autowired
    @Qualifier("taskExecutorTrace")
    private AsyncTaskExecutor asyncTaskExecutor;


    @Override
    @NewSpan("ComparePriceJobService")
    public ReturnT<String> execute(String s){
        Span span = tracerBean.startSpan();
        try {
            log.info("CompareJobService|execute|比对功能开始运行参数：{}", s);
            if (StringUtils.isEmpty(s)) {
                return ReturnT.SUCCESS;
            }
            String[] comIdArr = StringUtils.split(s, ",");
            for (String comId : comIdArr) {
//                executorService.execute(()->{
//                    log.info("ComparePriceJob|execute|连锁{}比对开始", comId);
//                    baseController.selectChannelHandler(SyncTypeEnum.PRICE.getCode(), handlerList).compare(comId);
//                });


                asyncTaskExecutor.execute(() -> {
                    log.info("ComparePriceJob|execute|连锁{}比对开始", comId);
                    baseController.selectChannelHandler(SyncTypeEnum.PRICE.getCode(), handlerList).compare(comId);
                });


            }
        } catch (Exception e) {
            ReturnT.FAIL.setMsg(e.getMessage());
            return ReturnT.FAIL;
        }finally {
            tracerBean.close(span);
        }
        return ReturnT.SUCCESS;
    }
}
