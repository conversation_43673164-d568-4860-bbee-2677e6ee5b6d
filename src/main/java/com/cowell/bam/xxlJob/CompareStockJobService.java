
package com.cowell.bam.xxlJob;

import com.cowell.bam.mq.CompareStockLoadBalanceProducer;
import com.cowell.bam.mq.message.CompareStockDTO;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHandler;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.sleuth.annotation.NewSpan;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date: 2018/12/29 19:27
 * @description:
 */

@JobHandler(value = "CompareStockJob")
@Component
public class CompareStockJobService extends IJobHandler {
    private Logger log = LoggerFactory.getLogger(CompareStockJobService.class);

    @Autowired
    private CompareStockLoadBalanceProducer compareStockLoadBalanceProducer;

    @Override
    @NewSpan("CompareStockJobService")
    public ReturnT<String> execute(String s) {

        try {
            log.info("CompareJobService|execute|比对功能开始运行参数：{}", s);
            if (StringUtils.isEmpty(s)) {
                return ReturnT.SUCCESS;
            }
            String[] comIdArr = StringUtils.split(s, ",");
            for (String comId : comIdArr) {
                CompareStockDTO compareStockDTO = new CompareStockDTO();
                compareStockDTO.setComId(comId);
                compareStockDTO.setTag("comId");
                compareStockDTO.setKey(comId);
                compareStockLoadBalanceProducer.sendMessage(compareStockDTO);
            }
        } catch (Exception e) {
            ReturnT.FAIL.setMsg(e.getMessage());
            return ReturnT.FAIL;
        }
        return ReturnT.SUCCESS;
    }
}
