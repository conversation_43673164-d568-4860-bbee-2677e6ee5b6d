package com.cowell.bam.xxlJob;

import com.cowell.bam.enums.SyncTypeEnum;
import com.cowell.bam.service.DataCompareService;
import com.cowell.bam.service.PriceCenterCompareService;
import com.cowell.bam.service.dto.DiffCollectDataResponseDTO;
import com.cowell.bam.service.dto.DiffDataRequestDTO;
import com.cowell.bam.service.impl.ThirdService;
import com.cowell.bam.web.rest.util.DateUtil;
import com.cowell.bam.web.rest.util.EmailUtils;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHandler;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.sleuth.annotation.NewSpan;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.LinkedHashMap;
import java.util.List;

/**
 * 发送价格差异邮件xxl
 */
@JobHandler(value = "PriceCompareResultJob")
@Component
public class PriceCompareResultJob extends IJobHandler{

    private static final Logger log = LoggerFactory.getLogger(PriceCompareResultJob.class);

    @Autowired
    private ThirdService thirdService;
    @Autowired
    private PriceCenterCompareService priceCenterCompareService;

    @Override
    @NewSpan("PriceCompareResultJob")
    public ReturnT<String> execute(String comIds)  {
        try {
            log.info("PriceCompareResultJob|execute|发送价格差异邮件xxl|param={}", comIds);
            if (org.apache.commons.lang.StringUtils.isEmpty(comIds)) {
                return ReturnT.SUCCESS;
            }
            for (String comId : StringUtils.split(comIds, ",")) {
                Long businessId = thirdService.transferMdmComIdToBusinessId(comId);
                if (businessId == null) {
                    continue;
                }
                priceCenterCompareService.exportCompareDataByVersion(comId, businessId, 61, SyncTypeEnum.PRICE);
            }
        } catch (Exception e) {
            ReturnT.FAIL.setMsg(e.getMessage());
            return ReturnT.FAIL;
        }
        return ReturnT.SUCCESS;
    }

}
