
package com.cowell.bam.xxlJob;

import com.cowell.bam.service.StockCostCompareService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHandler;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.sleuth.annotation.NewSpan;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date: 2018/12/29 19:27
 * @description: 比对库存成本库存  (截止2025-08-05 xxl停止状态)
 */

@JobHandler(value = "CompareStockCostJob")
@Component
public class CompareStockCostJobService extends IJobHandler {
    private Logger log = LoggerFactory.getLogger(CompareStockCostJobService.class);


    @Autowired
    private StockCostCompareService stockCostCompareService;


    @Override
    @NewSpan("CompareStockJobService")
    public ReturnT<String> execute(String s) {

        try {
            log.info("CompareStockCostJobService|execute|比对功能开始运行参数：{}", s);
            if (StringUtils.isEmpty(s)) {
                return ReturnT.SUCCESS;
            }
            String[] storeIdArr = StringUtils.split(s, ",");
            for (String storeIdStr : storeIdArr) {
                stockCostCompareService.compare(Long.valueOf(storeIdStr));
            }
        } catch (Exception e) {
            ReturnT.FAIL.setMsg(e.getMessage());
            log.error("CompareStockCostJobService|execute|比对失败：{}", s,e);
            return ReturnT.FAIL;
        }
        return ReturnT.SUCCESS;
    }
}
