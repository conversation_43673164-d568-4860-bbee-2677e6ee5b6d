
package com.cowell.bam.xxlJob;

import com.alibaba.fastjson.JSONArray;
import com.cowell.bam.service.HanaDataService;
import com.cowell.bam.service.IRedissonCacheService;
import com.cowell.bam.service.dto.CompareJobDTO;
import com.cowell.bam.service.dto.CompareTypeDTO;
import com.cowell.bam.service.impl.ThirdService;
import com.cowell.bam.web.rest.util.ConstantPool;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHandler;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.sleuth.annotation.NewSpan;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date: 2018/12/29 19:27
 * @description:  (截止2025-08-05 xxl停止状态)
 */

@JobHandler(value = "compareJobService")
@Component
public class CompareJobService extends IJobHandler {
    private Logger log = LoggerFactory.getLogger(CompareJobService.class);

    @Autowired
    private HanaDataService hanaDataService;

    @Autowired
    private IRedissonCacheService redissonCacheService;

    @Autowired
    private ThirdService thirdService;

    @Override
    @NewSpan("getHanaBdpJobWithBam")
    public ReturnT<String> execute(String s){
        try {
            log.info("CompareJobService|execute|比对功能开始运行参数：{}", s);
            if (StringUtils.isEmpty(s)) {
                return ReturnT.SUCCESS;
            }
            List<CompareJobDTO> compareJobDTOS = JSONArray.parseArray(s, CompareJobDTO.class);
            List<Long> businessIdList = compareJobDTOS.stream().map(CompareJobDTO::getBusinessId).collect(Collectors.toList());
            // 1.查询redis是否有数据 ，同一个连锁设定-1 则为连锁下所有门店
            // 有则根据上次的位置找下个节点
            // 无  则直接从头开始取设定的门店数量
            String businessIdKey = ConstantPool.COMPARE_INFO;
            String result = redissonCacheService.get(businessIdKey);
            List<CompareJobDTO> compareJobDTOList;
            if (StringUtils.isNotEmpty(result)) {
                String[] arr = StringUtils.split(result, "_");
                Long bid = Long.valueOf(arr[0]);
                int num = businessIdList.indexOf(bid);
                if (num == -1) {
                    return ReturnT.SUCCESS;
                }
                compareJobDTOList = compareJobDTOS.subList(num, compareJobDTOS.size());
            } else {
                compareJobDTOList = compareJobDTOS;
            }
            for (CompareJobDTO compareJobDTO : compareJobDTOList) {
                CompareTypeDTO compareTypeDTO = compareJobDTO.getCompareTypeDTO();
                Integer stockStoreNum = compareTypeDTO.getStockStore();
                List<Long> storeIdList = thirdService.getStoreIdsByBusinessId(compareJobDTO.getBusinessId());
                if (CollectionUtils.isEmpty(storeIdList)) {
                    return ReturnT.SUCCESS;
                }
                if (StringUtils.isEmpty(result)) {
                    if (stockStoreNum == -1 || stockStoreNum >= storeIdList.size()) {
                        //组装查询海典数据
                        //storeIdList
                        redissonCacheService.set(businessIdKey, compareJobDTO.getBusinessId() + "_" + "-1");
                        break;
                    } else {
                        //存储redis已经存在的值，下次使用
                        // businessId; 分页，每页大小，第几页
                        int totalPage = storeIdList.size() / stockStoreNum + 1;
                        redissonCacheService.set(businessIdKey, compareJobDTO.getBusinessId() + "_" + totalPage + "_" + 1);
                        break;
                    }
                } else {
                    String[] arr = StringUtils.split(result, "_");
                    Long bid = Long.valueOf(arr[0]);
                    int pageNum = Integer.valueOf(arr[1]);
                    //获取总页数
                    int totalPage = storeIdList.size() / stockStoreNum + 1;
                    if (bid.equals(compareJobDTO.getBusinessId())) {
                        if (pageNum == totalPage) {
                            continue;
                        } else if (pageNum < totalPage) {
                            int fromIndex = (pageNum - 1) * stockStoreNum;
                            int toIndex = pageNum * stockStoreNum;
                            List<Long> data = storeIdList.subList(fromIndex, toIndex);
                            redissonCacheService.set(businessIdKey, compareJobDTO.getBusinessId() + "_" + pageNum);
                            break;
                        }
                    } else {
                        //查询
                        List<Long> data = storeIdList.subList(0, stockStoreNum);
                        redissonCacheService.set(businessIdKey, compareJobDTO.getBusinessId() + "_" + totalPage + "_" + 1);
                        break;
                    }
                }
            }

        } catch (Exception e) {
            ReturnT.FAIL.setMsg(e.getMessage());
            return ReturnT.FAIL;
        }
        return ReturnT.SUCCESS;
    }
}
