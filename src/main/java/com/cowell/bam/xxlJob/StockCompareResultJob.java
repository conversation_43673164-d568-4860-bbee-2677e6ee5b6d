package com.cowell.bam.xxlJob;

import com.cowell.bam.service.DataCompareService;
import com.cowell.bam.service.dto.DiffCollectDataResponseDTO;
import com.cowell.bam.service.dto.DiffDataCompareInfoDTO;
import com.cowell.bam.service.dto.DiffDataRequestDTO;
import com.cowell.bam.service.utils.ExcelUtil;
import com.cowell.bam.web.rest.util.DateUtil;
import com.cowell.bam.web.rest.util.EmailUtils;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHandler;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.sleuth.annotation.NewSpan;
import org.springframework.stereotype.Component;

import java.io.ByteArrayOutputStream;
import java.util.Date;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Objects;

/**
 * 发送库存差异邮件xxl
 */
@JobHandler(value = "stockCompareResultJob")
@Component
public class StockCompareResultJob extends IJobHandler{

    private static final Logger log = LoggerFactory.getLogger(StockCompareResultJob.class);

    private static final String subject = "库存同步差异统计";

    @Autowired
    private DataCompareService dataCompareService;

    @Override
    @NewSpan("stockCompareResultJob")
    public ReturnT<String> execute(String jobConfig)  {

        DiffDataRequestDTO diffDataRequestDTO = new DiffDataRequestDTO();
        diffDataRequestDTO.setDataType(0);
        String date = DateUtil.dateToStr(new Date(), DateUtil.DATE_FORMAT);
        diffDataRequestDTO.setTime(date);

        log.info("[StockCompareResultJob]|execute|param:{}!", diffDataRequestDTO);
        List<DiffCollectDataResponseDTO> diffCollectList = dataCompareService.getDiffCollectList(diffDataRequestDTO);

//        List<DiffDataCompareInfoDTO> diffDetailList = dataCompareService.getDiffDetailList(diffDataRequestDTO);
//        log.info("StockCompareResultJob|execute|diffDetailList:{}!", diffDetailList);
        try {
            StringBuilder content = new StringBuilder("<html><head></head><body><h3>"+ date +"&nbsp;日库存同步差异统计</h3>");
            content.append("<table border=\"5\" style=\"border:solid 1px #E8F2F9;font-size=14px;;font-size:18px;width:500px;text-align:center\">");
            content.append("<tr ><td>连锁id</td><td>连锁名称</td><td>差异数</td></tr>");

            if (CollectionUtils.isEmpty(diffCollectList)) {
                content.append("<tr>");
                content.append("<td>无差异数据</td>");
                content.append("<td>无差异数据</td>");
                content.append("<td>无差异数据</td>");
                content.append("</tr>");
            }
            diffCollectList.forEach(diffCollectDataResponseDTO -> {
                content.append("<tr>");
                content.append("<td>").append(diffCollectDataResponseDTO.getBusinessId()).append("</td>"); //第一列
                content.append("<td>").append(diffCollectDataResponseDTO.getBusinessName()).append("</td>"); //第二列
                content.append("<td>").append(diffCollectDataResponseDTO.getCount()).append("</td>"); //第三列
                content.append("</tr>");
            });
            content.append("</table>");
            content.append("</body></html>");
            String[] to = StringUtils.split(jobConfig, ",");
//            ByteArrayOutputStream bos = new ByteArrayOutputStream();
//            ExcelUtil.listToExcel(diffDetailList, getExcelColumnTitle(), "sheet1",0,  bos);
            EmailUtils.sendMail(to, subject + "_" + date, content.toString(), null);
            log.info("[StockCompareResultJob] sendMailOk !");
            return ReturnT.SUCCESS;
        } catch (Exception e) {
            log.error("邮件任务执行失败, 任务配置：{}", jobConfig, e);
        }
        return ReturnT.SUCCESS;
    }

    private LinkedHashMap<String, String> getExcelColumnTitle() {
        LinkedHashMap<String, String> fieldMap = new LinkedHashMap<>();
        fieldMap.put("businessName", "连锁名称");
        fieldMap.put("businessId", "连锁id");
        fieldMap.put("storeName", "门店名称");
        fieldMap.put("storeId", "门店Id");
        fieldMap.put("goodsNo", "商品编码");
        fieldMap.put("thirdData", "三方库存信息");
        fieldMap.put("ourData", "中台库存信息");
        fieldMap.put("reason", "原因");
        fieldMap.put("gmtCreateStr", "创建时间");
        return fieldMap;
    }


}
