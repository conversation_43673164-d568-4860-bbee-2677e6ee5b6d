package com.cowell.bam.xxlJob;

import com.cowell.bam.service.PriceCenterCompareService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHandler;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.sleuth.annotation.NewSpan;
import org.springframework.stereotype.Component;

/**
 * 比较POS方价格（海典、英克） 比对价格特价标识  (截止2025-08-05 xxl停止状态)
 * <AUTHOR>
 * @date 2022/5/7 15:12
 */
@JobHandler(value = "comparePriceSpecialFlagJobHandler")
@Component
public class ComparePriceSpecialFlagJobHandler extends IJobHandler {

    private final Logger log = LoggerFactory.getLogger(ComparePriceSpecialFlagJobHandler.class);

    @Autowired
    private PriceCenterCompareService priceCenterCompareService;

    @Override
    @NewSpan("comparePriceSpecialFlagJobHandler")
    public ReturnT<String> execute(String s){
        try {
            log.info("comparePriceSpecialFlagJobHandler|execute|比对功能开始运行参数：{}", s);

            //获取连锁和门店的对应关系
            if (StringUtils.isBlank(s)){
                return ReturnT.SUCCESS;
            }
            String[] paramArr = StringUtils.split(s, ",");
            for (String param : paramArr) {
                log.info("comparePriceSpecialFlagJobHandler|execute|连锁{}比对开始", param);
                priceCenterCompareService.comparePriceSpecialFlag(param);
            }
        } catch (Exception e) {
            ReturnT.FAIL.setMsg(e.getMessage());
            return ReturnT.FAIL;
        }
        return ReturnT.SUCCESS;
    }

}
