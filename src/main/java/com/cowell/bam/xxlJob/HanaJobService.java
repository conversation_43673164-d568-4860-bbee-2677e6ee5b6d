
package com.cowell.bam.xxlJob;

import com.cowell.bam.service.HanaDataService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHandler;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.cloud.sleuth.annotation.NewSpan;
import org.springframework.core.task.AsyncTaskExecutor;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date: 2018/12/29 19:27
 * @description:   (截止2025-08-05 xxl停止状态)
 */

@JobHandler(value = "getHanaBdpJob")
@Component
public class HanaJobService extends IJobHandler {
    private Logger log = LoggerFactory.getLogger(HanaJobService.class);
    @Autowired
    @Qualifier("taskExecutor")
    private AsyncTaskExecutor executor;
    @Autowired
    private HanaDataService hanaDataService;

    @Override
    @NewSpan("getHanaBdpJobWithBam")
    public ReturnT<String> execute(String s) throws Exception {

        try {
            log.info("HanaJobService#getHanaBdpJobWithBam开始执行xxljob任务");
            hanaDataService.getHanaBdpData();
        } catch (Exception e) {
            ReturnT.FAIL.setMsg(e.getMessage());
            return ReturnT.FAIL;
        }
        return ReturnT.SUCCESS;
    }
}
