
package com.cowell.bam.xxlJob;

import com.alibaba.fastjson.JSONObject;
import com.cowell.bam.service.PriceCenterCompareService;
import com.cowell.bam.service.impl.ThirdService;
import com.google.common.collect.Lists;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHandler;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.redisson.api.RBucket;
import org.redisson.api.RedissonClient;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.cloud.sleuth.annotation.NewSpan;
import org.springframework.core.task.AsyncTaskExecutor;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 * @date: 2020/05/23 00:24
 * @description:  (截止2025-08-05 xxl停止状态)
 *
 * param 样式：comId1:busNo1,busNo2;comId2:busNo1,busNo2;
 */

@JobHandler(value = "ComparePriceCenterJob")
@Component
public class ComparePriceCenterJobService extends IJobHandler {
    private Logger log = LoggerFactory.getLogger(ComparePriceCenterJobService.class);


    @Autowired
    private PriceCenterCompareService priceCenterCompareService;
    @Autowired
    private ThirdService thirdService;
    @Autowired
    @Qualifier("taskExecutorTrace3")
    private AsyncTaskExecutor asyncTaskExecutor3;
    @Autowired
    private RedissonClient redissonClient;

    private static final String openBusinessList = "OPEN_OMS_CONFIG_BUSINESS_LIST";

    @Override
    @NewSpan("ComparePriceCenterJobService")
    public ReturnT<String> execute(String s){
        try {
            log.info("ComparePriceCenterJobService|execute|比对功能开始运行参数：{}", s);

            //获取连锁和门店的对应关系
            List<String> comIds = this.getComIdsByBusinessId(s);

            if (CollectionUtils.isEmpty(comIds)){
                log.info("ComparePriceCenterJobService|execute|获取需要对比的连锁编码为空, 不需要价格对比");
                return ReturnT.SUCCESS;
            }
            for (String param : comIds) {
                asyncTaskExecutor3.execute(() -> {
                    log.info("ComparePriceCenterJobService|execute|连锁比对开始 :{}", param);
                    priceCenterCompareService.priceCenterCompareFromComId(param);
                });
            }
        } catch (Exception e) {
            ReturnT.FAIL.setMsg(e.getMessage());
            return ReturnT.FAIL;
        }

        return ReturnT.SUCCESS;
    }

    /**
     * 获取需要对比的 连锁编码
     * @return
     */
    private List<String> getComIdsByBusinessId(String s){

        List<String> comIds = Lists.newArrayList();

        if (StringUtils.isNotBlank(s)){
            comIds.add(s);
            log.info("前端传过来的连锁编码 :{}",comIds.toString());
            return comIds;
        }

        RBucket<String> bucket = redissonClient.getBucket(openBusinessList);
        String redisComIds = bucket.get();
        log.info("xxlJob 定时执行价格对比 Redis中获取的ComId:{} ",redisComIds);
        if (StringUtils.isNotBlank(redisComIds)){
            return JSONObject.parseArray(redisComIds,String.class);
        }

        comIds = priceCenterCompareService.openConfigBusinessIdList();

        log.info("需要进行价格对比的连锁编码:{}",comIds);
        return comIds;
    }

}
