package com.cowell.bam.web.rest.util;

/**
 * <AUTHOR>
 * @Description: 第三方请求常量
 * @date 2018-09-11 17:27
 */
public class ErpRequestConstants {

    public static String REQUEST_B_G_UID_FIELD = "bguid";

    public static String REQUEST_B_TYPE_FIELD = "btype";

    public static String REQUEST_B_SOURCE_FIELD = "bsource";

    public static String REQUEST_B_DESTINATION_FIELD = "bdestination";

    public static String REQUEST_B_DATETIME_FIELD = "bdatetime";

    /**
     * 消息处理状态
     */
    public static String REQUEST_B_STATUS_FIELD = "bstatus";

    /**
     * 回调地址
     */
    public static String REQUEST_B_CALLBACK_FIELD = "bcallback";

    /**
     * 版本号
     */
    public static String REQUEST_B_VERSION_FIELD = "bversion";

    /**
     * 数据重复校验字符串
     */
    public static String REQUEST_B_DATAHASH_FIELD = "bdatahash";

    /**
     * 业务查询使用的关键字段
     */
    public static String REQUEST_B_KEYS_FIELD = "bkeys";

    /**
     * 业务双方确定的业务数据JSON结构
     */
    public static String REQUEST_B_DATA_FIELD = "bdata";


    public static String REQUEST_TABLE_FIELD = "Table";

    /**
     * apollo中配置的erpType
     */
    public static String REQUEST_APOLLO_ERP_TYPE_FIELD = "erpType";

    /**
     * apollo中配置的bDataType
     */
    public static String REQUEST_APOLLO_B_DATA_TYPE_FIELD = "bDataType";

    /**
     * apollo中配置的handler类型
     */
    public static String REQUEST_APOLLO_HANDLER_TYPE_FIELD = "businessHandlerType";


    /**
     * 日期格式化成string的格式
     */
    public static String REQUEST_DATE_FORMAT_PATTERN = "yyyy-MM-dd HH:mm:ss";


    /**
     * 海典，英克 接收请求处理的hadler
     */
    public static final String ERP_SAP_REQUEST_HANDLER = "businessErpHandler";

    public static final String ERP_REQUEST_HANDLER_TYPE_FIELD = "businessHandlerType";

    /**
     * sap请求所用的（RCF报文类型）handler
     */
    public static final String ERP_SAP_REQUEST_RFC_HANDLER = "sapRfcHandler";

    /**
     * sap请求所用的（IDOC报文类型）handler
     */
    public static final String ERP_SAP_REQUEST_IDOC_HANDLER = "sapIdocHandler";

    public static final String ERP_SAP_REQUEST_PROXY_HANDLER = "sapProxyHandler";

    /**
     * post请求
     */
    public static final String ERP_REQUEST_MODE_POST = "POST";

    /**
     * get请求
     */
    public static final String ERP_REQUEST_MODE_GET = "GET";

    /**
     * 经营目录默认切割的数数量
     */
    public static final Integer SAP_OPERATINT_DIR_DEFAULT_SPLIT_NUM = 1000;


    /**
     * 阿波罗配置项中appName下的actions节点
     */
    public static final String ERP_REQUEST_ALL_ACTIONS_FIELD = "actions";


    /**
     * 单个action下的businessIds节点
     */
    public static final String ERP_REQUEST_ALL_BUSINESS_ID_FIELD = "businessIds";

    public static final String ERP_REQUEST_ACTION_FIELD = "action";

    public static final String DATE_TIME_FORMAT_ = "yyyy-MM-dd HH:mm:ss";

}
