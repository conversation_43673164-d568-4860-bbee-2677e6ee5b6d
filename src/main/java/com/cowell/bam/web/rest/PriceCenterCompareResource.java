package com.cowell.bam.web.rest;

import com.cowell.bam.enums.SyncTypeEnum;
import com.cowell.bam.service.PriceCenterCompareService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 类说明 价格中心对比
 *
 * @Author: liw
 * @Date: 2020-05-31 00:45
 */
@RestController
@Api(tags = "价格中心价格对比补偿", description = "价格中心价格对比补偿")
@RequestMapping("/api")
public class PriceCenterCompareResource {
    private final Logger logger = LoggerFactory.getLogger(PriceCenterCompareResource.class);


    @Autowired
    private PriceCenterCompareService priceCenterCompareService;


    @GetMapping(value = {"/compare/price/comId"})
    @ApiOperation(value = "根据连锁进行对比")
    public ResponseEntity queryBJStoreGoodsPrice(@RequestParam(value = "comId")String comId){
        priceCenterCompareService.priceCenterCompareFromComId(comId);
        return new ResponseEntity(true, HttpStatus.OK);

    }
    @GetMapping(value = {"/compare/price/businessIdAndStoreIdAndGoodsNo"})
    @ApiOperation(value = "根据连锁进行对比")
    public ResponseEntity comparePriceByBusinessIdAndStoreIdAndGoodsNo(@RequestParam(value = "businessId")Long businessId,
                                                   @RequestParam(value = "storeId",required = false)Long storeId,
                                                   @RequestParam(value = "goodsNos",required = false)String goodsNos){
        priceCenterCompareService.comparePriceByBusinessIdAndStoreIdAndGoodsNo(businessId,storeId,goodsNos);
        return new ResponseEntity(true, HttpStatus.OK);

    }

    @GetMapping(value = {"/compare/price/comIdTwo"})
    @ApiOperation(value = "不比对价格直接进行价格下发")
    @Deprecated
    public ResponseEntity queryBJStoreGoodsPriceTwo(@RequestParam(value = "comId")String comId,
                                                    @RequestParam(value = "busNo",required = false)String busNo){
        priceCenterCompareService.priceCenterFromComId(comId,busNo);
        return new ResponseEntity(true, HttpStatus.OK);

    }


    @GetMapping(value = {"/compare/price/comIdAndBusNo"})
    @ApiOperation(value = "根据连锁和门店进行对比")
    public ResponseEntity queryBJStoreGoodsPrice(@RequestParam(value = "comId")String comId,
                                                 @RequestParam(value = "busNo")String busNo){
        priceCenterCompareService.priceCenterCompareFromBusNo(comId,busNo);
        return new ResponseEntity(true, HttpStatus.OK);
    }


    @GetMapping(value = {"/compare/price/comIdAndBusNoAndGoods"})
    @ApiOperation(value = "根据连锁、门店和商品进行对比")
    public ResponseEntity queryBJStoreGoodsPrice(@RequestParam(value = "comId")String comId,
                                                 @RequestParam(value = "busNo")String busNo,
                                                 @RequestParam(value = "goodsNos") List<String> goodsNos){
        priceCenterCompareService.priceCenterCompareFromGoods(comId,busNo,goodsNos);
        return new ResponseEntity(true, HttpStatus.OK);
    }

    @GetMapping(value = {"/internal/compare/price/businessIdAndStoreIdAndGoods"})
    @ApiOperation(value = "根据连锁、门店和商品进行对比")
    public ResponseEntity queryBJStoreGoodsPriceInternal(@RequestParam(value = "businessId")Long businessId,
                                                 @RequestParam(value = "storeId")Long storeId,
                                                 @RequestParam(value = "goodsNos") List<String> goodsNos){
        priceCenterCompareService.priceCenterCompareFromGoods(businessId,storeId,goodsNos);
        return new ResponseEntity(true, HttpStatus.OK);
    }

    @GetMapping(value = {"/comparePos/price/comId"})
    @ApiOperation(value = "按照门店纬度进行价格差异数据导出")
    @Deprecated
    public ResponseEntity comparePosByComId(@RequestParam(value = "comId")String comId){
        priceCenterCompareService.priceCenterComparePosFromComId(comId);
        return new ResponseEntity(true, HttpStatus.OK);

    }

    @GetMapping(value = {"/comparePos/price/exportByComId"})
    @ApiOperation(value = "根据连锁纬度进行价格差异数据导出")
    @Deprecated
    public ResponseEntity exportByComId(@RequestParam(value = "comId")String comId){
        priceCenterCompareService.exportByComId(comId);
        return new ResponseEntity(true, HttpStatus.OK);

    }

    @GetMapping(value = {"/comparePos/price/exportByComIdDistribute"})
    @ApiOperation(value = "根据连锁纬度进行价格差异数据导出(分布式)")
    public ResponseEntity exportByComIdDistribute(@RequestParam(value = "comId")String comId){
        priceCenterCompareService.exportByComIdDistribute(comId, false);
        return new ResponseEntity(true, HttpStatus.OK);

    }

    @GetMapping(value = {"/comparePos/price/exportCompareDataByVersion"})
    @ApiOperation(value = "根据连锁纬度进行价格差异数据结果导出(分布式)")
    public ResponseEntity exportCompareDataByVersion(@RequestParam(value = "comId") String comId,
                                                     @RequestParam(value = "businessId") Long businessId,
                                                     @RequestParam(value = "version") Integer version) {
        priceCenterCompareService.exportCompareDataByVersion(comId, businessId, version, SyncTypeEnum.PRICE);
        return new ResponseEntity(true, HttpStatus.OK);
    }

    @GetMapping(value = {"/compare/price/comparePriceSpecialFlag"})
    @ApiOperation(value = "根据连锁对比价格特价标记")
    public ResponseEntity comparePriceSpecialFlag(@RequestParam(value = "comId")String comId){
        priceCenterCompareService.comparePriceSpecialFlag(comId);
        return new ResponseEntity(true, HttpStatus.OK);
    }

    @GetMapping(value = {"/compare/price/comparePriceSpecialFlagByBusNo"})
    @ApiOperation(value = "根据连锁和门店对比价格特价标记")
    public ResponseEntity comparePriceSpecialFlag(@RequestParam(value = "comId")String comId,
                                                 @RequestParam(value = "busNo")String busNo){
        priceCenterCompareService.comparePriceSpecialFlag(comId, busNo);
        return new ResponseEntity(true, HttpStatus.OK);
    }

    @GetMapping(value = {"/compare/price/comparePriceSpecialFlagByGoodsNo"})
    @ApiOperation(value = "根据连锁、门店和商品对比价格特价标记")
    public ResponseEntity comparePriceSpecialFlag(@RequestParam(value = "comId")String comId,
                                                 @RequestParam(value = "busNo")String busNo,
                                                 @RequestParam(value = "goodsNos") List<String> goodsNos){
        priceCenterCompareService.comparePriceSpecialFlag(comId, busNo, goodsNos);
        return new ResponseEntity(true, HttpStatus.OK);
    }


}
