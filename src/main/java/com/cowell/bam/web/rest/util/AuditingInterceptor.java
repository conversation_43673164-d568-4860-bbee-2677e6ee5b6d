package com.cowell.bam.web.rest.util;

import com.cowell.bam.domain.AbstractAuditingDO;
import com.cowell.bam.security.oauth2.TokenAuthenticationManager;
import com.cowell.bam.service.dto.TokenUserDTO;
import org.apache.ibatis.executor.Executor;
import org.apache.ibatis.mapping.MappedStatement;
import org.apache.ibatis.mapping.SqlCommandType;
import org.apache.ibatis.plugin.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Component;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;
import org.springframework.web.context.support.WebApplicationContextUtils;

import javax.servlet.http.HttpServletRequest;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.Properties;


/**
 * <AUTHOR>
 */
@Component
@Intercepts({ @Signature(type = Executor.class, method = "update", args = { MappedStatement.class, Object.class }) })
public class AuditingInterceptor implements Interceptor {

    private final Logger log = LoggerFactory.getLogger(AuditingInterceptor.class);

    private String getUserId(){
        String userId = "";
        try{
            //获取request
            ServletRequestAttributes sra = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
            if(sra==null){
                return userId;
            }
            HttpServletRequest request = sra.getRequest();
            //从request中获取上下文，获取spring bean的实例
            ApplicationContext appCtx =  WebApplicationContextUtils.getWebApplicationContext(request.getServletContext());
            TokenAuthenticationManager tokenAuthenticationManager = (TokenAuthenticationManager)appCtx.getBean(TokenAuthenticationManager.class);
            //取得登陆人信息
            TokenUserDTO tokenUserDTO = tokenAuthenticationManager.getUserInfobyToken(request);
            if(tokenUserDTO!=null&&tokenUserDTO.getUserId()!=null){
                userId = tokenUserDTO.getUserId().toString();
            }
        }catch (Exception e){
            log.info("AuditingInterceptor.getUserId",e);
        }
        return userId;
    }
    @Override
    public Object intercept(Invocation invocation) throws Throwable {

        MappedStatement mappedStatement = (MappedStatement) invocation.getArgs()[0];
        // 获取 SQL 命令
        SqlCommandType sqlCommandType = mappedStatement.getSqlCommandType();
        // 获取参数
        Object parameter = invocation.getArgs()[1];
        if(parameter instanceof AbstractAuditingDO){
            String userId = getUserId();
            setValue((AbstractAuditingDO)parameter,userId,sqlCommandType);
        }else if(parameter instanceof HashMap){
            //批量时，传过的数值update与insert是否一样
            Object list = ((HashMap) parameter).get("list");
//            Object collection = ((HashMap) parameter).get("collection");
            if(list!=null && list instanceof ArrayList){
                String userId = getUserId();
                for(Object obj : (ArrayList)list){
                    if(obj instanceof AbstractAuditingDO) {
                        setValue((AbstractAuditingDO) obj, userId, sqlCommandType);
                    }
                }
            }

        }
        return invocation.proceed();
    }
    private void setValue(AbstractAuditingDO abstractAuditingDO,String userId,SqlCommandType sqlCommandType){
        abstractAuditingDO.setUpdateBy(userId);
        abstractAuditingDO.setGmtUpdate(new Date());
        if(SqlCommandType.INSERT.equals(sqlCommandType)){
            abstractAuditingDO.setCreatedBy(userId);
            abstractAuditingDO.setGmtCreate(new Date());
        }
    }


    @Override
    public Object plugin(Object target) {
        return Plugin.wrap(target, this);
    }

    @Override
    public void setProperties(Properties properties) {
    }
}
