package com.cowell.bam.web.rest;

import com.cowell.bam.domain.AuditLog;
import com.cowell.bam.domain.AuditLogDTO;
import com.cowell.bam.kafka.AuditLogKafkaConsumer;
import com.cowell.bam.service.AuditLogService;
import com.cowell.bam.service.utils.Pagination;
import com.cowell.bam.web.rest.util.HeaderUtil;
import org.checkerframework.checker.units.qual.A;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.kafka.support.Acknowledgment;
import org.springframework.messaging.Message;
import org.springframework.messaging.MessageHeaders;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("/api/bam/auditLog")
public class AuditLogResource {

    @Autowired
    private AuditLogService auditLogService;

    private static String BAM = "BAM";

    @PostMapping("/searchAuditLog")
    public ResponseEntity<List<AuditLog>> searchAuditLog(@RequestBody AuditLogDTO auditLogDTO) {
        Pagination<AuditLog> pagination = auditLogService.searchAuditLog(auditLogDTO);
        HttpHeaders headers = HeaderUtil.createEntityCreationAlert(BAM, "SUCCESS");
        headers.set("X-Total-Count", String.valueOf(pagination.getHitCount()));
        return new ResponseEntity<>(pagination.getList(), headers, HttpStatus.OK);
    }

//    @Autowired
//    private AuditLogKafkaConsumer auditLogKafkaConsumer;
//
//    @GetMapping("/test")
//    public ResponseEntity<Object> testConsumer() {
//        Message<List<String>> message = new Message<List<String>>() {
//            @Override
//            public List<String> getPayload() {
//                return null;
//            }
//
//            @Override
//            public MessageHeaders getHeaders() {
//                return null;
//            }
//        };
//        Acknowledgment ack = new Acknowledgment() {
//            @Override
//            public void acknowledge() {
//
//            }
//        };
//        auditLogKafkaConsumer.listener(message, ack);
//        return ResponseEntity.noContent().build();
//    }
}
