//package com.cowell.bam.web.rest;
//
//import com.alibaba.fastjson.JSONArray;
//import com.alibaba.fastjson.JSONObject;
//import com.cowell.bam.enums.RequestWayEnum;
//import com.cowell.bam.service.dto.SendRequestBodyDTO;
//import com.google.common.base.Splitter;
//import org.apache.commons.lang3.StringUtils;
//import org.slf4j.Logger;
//import org.slf4j.LoggerFactory;
//import org.springframework.beans.factory.annotation.Value;
//import org.springframework.util.Assert;
//
///**
// * <AUTHOR>
// * @Description: 第三方接口统一回调erpsaas抽象类
// * @date 2018-10-16 14:33
// */
//public abstract class  AbstractAcceptRequestResource<P, R> {
//
//    private static final Logger logger = LoggerFactory.getLogger(AbstractAcceptRequestResource.class);
//
//
//
//
//    private static final String REQUEST_DESC_FIELD = "desc";
//
//    private static final String REQUEST_ACTION_FIELD = "action";
//
//    private static final String REQUEST_LIMIT_FIELD = "limit";
//
//    /**
//     * erpsaas接收海典英克推送的订单，库存，价格，数据放入metaq自产自消的topic
//     */
//    @Value("${spring.rocketmq.topic.erp-callback-service-topic}")
//    private String erpCallBackTopic;
//
//    /**
//     * erpsaas接收海典英克推送的订单，库存，价格，数据放入metaq自产自消的tag
//     */
//    @Value("${spring.rocketmq.topic.erp-callback-service-tag}")
//    private String erpCallBackTag;
//
//
//    /**
//     * 校验参数
//     *
//     * @param o       推送的数据
//     * @param appName 应用名称
//     * @param action  具体的操作
//     */
//    private void validParam(P o, String appName, String action) {
//
//        Assert.notNull(o, "请求数据不能为空!");
//        Assert.hasText(appName, "appName不能为空!");
//        Assert.hasText(action, "action不能为空!");
//
//        logger.info("AbstractAcceptRequestResource|validParam|接收第三方发送的请求验证参数|param:jsonData={},appName={},action={}",
//                o.toString(), appName, action);
//    }
//
//    /**
//     * 构建请求对象
//     *
//     * @param appName 应用名
//     * @param action  具体操作
//     * @param body    请求发送的数据
//     * @return SendRequestBodyDTO
//     */
//    private SendRequestBodyDTO builderRequest(String appName, String action, String body) {
//
//
//        JSONObject jsonObject = null;
//            //bizConfigProperties.getCallBackConfig();
//        Assert.notEmpty(jsonObject, "Apollo配置项不能为空!");
//
//        JSONArray jsonArray = jsonObject.getJSONArray(appName);
//        Assert.notEmpty(jsonArray, "未配置回调相关数据!");
//
//        try {
//            for (Object o : jsonArray) {
//                if (null == o) {
//                    continue;
//                }
//                JSONObject json = (JSONObject) o;
//                if (StringUtils.isEmpty(json.getString(REQUEST_ACTION_FIELD))) {
//                    logger.error("AbstractAcceptRequestResource|builderRequest|接收erp推送数据，apollo配置action为空!");
//                    continue;
//                }
//
//                //查找相同action
//                if (!StringUtils.equalsIgnoreCase(action, json.getString(REQUEST_ACTION_FIELD))) {
//                    continue;
//                }
//                String requestUrl = json.getString(action);
//                String desc = json.getString(REQUEST_DESC_FIELD);
//
//                SendRequestBodyDTO sendBody = new SendRequestBodyDTO();
//                sendBody.setLimit(json.getInteger(REQUEST_LIMIT_FIELD));
//                sendBody.setPassWord("");
//                sendBody.setUserName("");
//                sendBody.setRequestUrl(requestUrl);
//                sendBody.setServiceDesc(desc);
//                sendBody.setRequestMode(RequestWayEnum.REQUEST_MODE_MQ.getCode());
//                sendBody.setSource("hd or yk");
//                sendBody.setDestination(appName);
//                sendBody.setRequestBody(body);
//                sendBody.setAppName(appName);
//                sendBody.setAction(action);
//                sendBody.setErpType("");
//                return sendBody;
//
//            }
//        } catch (Exception e) {
//            logger.error("AbstractAcceptRequestResource|processRequest|处理请求异常!|param:appName={}, action={}, requestData={}", appName, action, body, e);
//            return null;
//        }
//        return null;
//    }
//
//    /**
//     * 统一处理erp厂商推送过来的数据
//     *
//     * @param p       统一请求参数
//     * @param appName 应用名称
//     * @param action  请求action
//     * @return booleanSapCallbackController
//     */
//    protected R processRequest(P p, String appName, String action) {
//
//        String requestFilter = null;
//            //bizConfigProperties.getRequestFilterBlackList();
//        Iterable<String> result = Splitter.on(',').split(requestFilter);
//        Boolean resultData;
//        for (String str : result) {
//            if (StringUtils.isBlank(str)) {
//                continue;
//            }
//            if (StringUtils.equals(str, action)) {
//                logger.warn("AbstractAcceptRequestResource|processRequest|触发apollo配置的请求过滤规则已被过滤！|action={}", action);
//                resultData = true;
//                return (R) resultData;
//            }
//        }
//        validParam(p, appName, action);
//        SendRequestBodyDTO body = builderRequest(appName, action, JSONObject.toJSONString(p));
//        return sendMsg(body);
//    }
//
//
//    /**
//     * 发送下消息
//     *
//     * @param body 请求体
//     * @return Boolean
//     */
//    private R sendMsg(SendRequestBodyDTO body) {
//        Assert.notNull(body, "请求体不能为空!");
//        Boolean result = false;
//        try {
//            //erpCallbackServiceProducer.sendMessage(erpCallBackTopic, JSON.toJSONString(body), erpCallBackTag);
//            result = true;
//        } catch (Exception e) {
//            logger.error("AbstractAcceptRequestResource|sendMsg|erp推送数据，erpsaas发送消息异常!|param:body={}", body.toString(), e);
//            return (R) result;
//        }
//        return (R) result;
//    }
//}
