package com.cowell.bam.web.rest.errors;


/**
 * <AUTHOR> on 2018/5/2
 */
public enum ErrorCodeEnum {

    SUCCESS("200", "操作成功"),
    FAIL("0001", "操作失败"),
    CHECKFAIL("0002", "校验失败"),
    PARAMETER_CHECK_ERROR("0003","参数校验异常"),
    RETURN_RESULT_ERROR("0004","返回结果异常"),
    SYSTEM_ERROR("1000", "系统错误"),
    DATE_PARSE_EXCEPTION("20004", "字符转日期或时间格式异常"),
    USER_INFO_ERROR("00002", "用户信息错误"),
    HTTP_RESPONSE_FAIL("HTTP_RESPONSE_FAIL", "ERP请求相应失败!"),
    BUSINESS_DBCONFIG_ERROR("BUSINESS_DBCONFIG_ERROR", "连锁未配置对应的数据源"),

    ;

    private String code;
    private String msg;

    private ErrorCodeEnum(String code, String msg) {
        this.code = code;
        this.msg = msg;
    }

    public String getCode() {
        return code;
    }

    public String getMsg() {
        return msg;
    }

}
