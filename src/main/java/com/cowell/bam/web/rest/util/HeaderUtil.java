package com.cowell.bam.web.rest.util;

import com.cowell.bam.web.rest.errors.ErrorCodeEnum;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.HttpHeaders;

import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;

/**
 * Utility class for HTTP headers creation.
 */
public final class HeaderUtil {

    private static final Logger log = LoggerFactory.getLogger(HeaderUtil.class);

    private static final String APPLICATION_NAME = "digitalstoreApp";

    private HeaderUtil() {
    }

    public static HttpHeaders createAlert(String message, String param) {
        HttpHeaders headers = new HttpHeaders();
        headers.add("X-digitalstoreApp-alert", message);
        headers.add("X-digitalstoreApp-params", param);
        return headers;
    }

    public static HttpHeaders createEntityCreationAlert(String entityName, String param) {
        return createAlert(APPLICATION_NAME + "." + entityName + ".created", param);
    }

    public static HttpHeaders createEntityUpdateAlert(String entityName, String param) {
        return createAlert(APPLICATION_NAME + "." + entityName + ".updated", param);
    }

    public static HttpHeaders createEntityDeletionAlert(String entityName, String param) {
        return createAlert(APPLICATION_NAME + "." + entityName + ".deleted", param);
    }

    public static HttpHeaders createFailureAlert(String entityName, String errorKey, String defaultMessage) {
        log.warn("Entity processing failed, {}", defaultMessage);
        HttpHeaders headers = new HttpHeaders();
        headers.add("X-digitalstoreApp-error", "error." + errorKey);
        headers.add("X-digitalstoreApp-params", entityName);
        return headers;
    }
    public static HttpHeaders createErrorHeaders(String errorCode,String errorMessage){
        HttpHeaders headers = new HttpHeaders();
        try {
            headers.add("error-code",errorCode);
            headers.add("error-message",URLEncoder.encode(errorMessage,"UTF-8"));
        }catch (Exception e){
            log.info("HeaderUtil.createErrorHeaders encoder error|errorCode:{}|errorMessage:{}",errorCode,errorMessage,e);
        }finally {
            return headers;
        }
    }
    public static HttpHeaders createBusinessFailureAlert(String errorCode, String errorMessage) {

        log.warn("Entity processing failed, {}", errorMessage);
        HttpHeaders headers = new HttpHeaders();
        headers.add("error-Code", errorCode);
        try {
            headers.add("error-Message", URLEncoder.encode(errorMessage, "UTF-8"));
        } catch (UnsupportedEncodingException e) {
            log.warn("encode error-Message exception : ", e);
        }
        return headers;
    }
    public static HttpHeaders createErrorHeaders(ErrorCodeEnum errorCodeEnum){
        return createErrorHeaders(errorCodeEnum.getCode(),errorCodeEnum.getMsg());
    }
}
