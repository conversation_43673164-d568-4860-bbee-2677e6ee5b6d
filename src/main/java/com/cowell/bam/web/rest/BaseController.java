package com.cowell.bam.web.rest;

import com.cowell.bam.handler.AbstractChannelHandler;
import org.springframework.stereotype.Controller;

import java.util.List;

/**
 * <AUTHOR>
 * @Description: handler选择器
 * @date 2018-07-24 21:34
 */
@Controller
public class BaseController<K, R>  {

    /**
     * 选择执行器
     *
     * @param channel
     * @return AbstractChannelHandler
     */
    public AbstractChannelHandler selectChannelHandler(Integer channel, List<AbstractChannelHandler> handlerList) {
        return handlerList.stream().filter(handler -> handler.isMatchHandler(channel) == true).findFirst().get();
    }
}
