package com.cowell.bam.web.rest.util;

import org.apache.commons.lang.StringUtils;

import java.math.BigDecimal;
import java.util.Objects;

/**
 * 金额换算工具类
 */
public class BigDecimalUtils {

    /**
     * 金额 元转分
     * @param yuan
     * @return
     */
    public static Long convertFenByYuan(String yuan){
        if(StringUtils.isNotBlank(yuan)){
            BigDecimal multiply = new BigDecimal(yuan).multiply(BigDecimal.valueOf(100));
            return multiply.longValue();
        }
        return 0L;
    }

    /**
     * 金额 分转元
     * @param fen
     * @return
     */
    public static String convertYuanByFen(Long fen){
        if(null != fen && fen >= 0){
            BigDecimal divide = BigDecimal.valueOf(fen).divide(BigDecimal.valueOf(100));
            return divide.toString();
        }
        return null;
    }

    /**
     * 金额 分转元
     * @param fen
     * @return
     */
    public static String convertYuanByFen(BigDecimal fen){
        if(Objects.nonNull(fen)){
            BigDecimal divide = fen.divide(BigDecimal.valueOf(100));
            return divide.toString();
        }
        return null;
    }


    /**
     * 验证BigDecimal的值
     * @param num
     * @return
     */
    public static BigDecimal convert(BigDecimal num){
        if(null == num){
            return BigDecimal.ZERO;
        }
        return num;
    }


}
