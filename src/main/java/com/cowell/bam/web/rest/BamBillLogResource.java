package com.cowell.bam.web.rest;

import com.codahale.metrics.annotation.Timed;
import com.cowell.bam.domain.BamQueryDO;
import com.cowell.bam.domain.BamReturnDO;
import com.cowell.bam.service.HanaDataService;
import com.cowell.bam.service.StoreService;
import com.cowell.bam.service.dto.MdmDataTransformDTO;
import com.cowell.bam.service.impl.BamBusinessBillLogServiceImpl;
import com.cowell.bam.web.rest.errors.BusinessErrorException;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.ArrayList;
import java.util.List;

/**
 * Controller for view and managing Log Level at runtime.
 */
@RestController
@RequestMapping("/api/bam/billLog")
public class BamBillLogResource {

    private final Logger log = LoggerFactory.getLogger(BamBillLogResource.class);
    @Autowired
    private BamBusinessBillLogServiceImpl bamBusinessBillLogService;
    @Autowired
    private HanaDataService hanaDataService;
    @Autowired
    private StoreService storeService;
    /**
     * mdm转换scrm数据 数据类型 mdm
     */
    public static final Integer MDM_SCRM_DATA_TYPE_BUSINESS = 1;
    /**
     * mdm转换scrm数据 数据类型 scrm
     */
    public static final Integer MDM_SCRM_DATA_TYPE_STORE = 2;
    /**
     * mdm转换scrm数据 转换类型 mdm
     */
    public static final Integer MDM_SCRM_TRANSFER_TYPE_TO_MDM = 1;
    /**
     * POS发起请货申请
     */
    public static final Integer STEP_ID_QHSQ = 1001;



    @GetMapping("/selectByCompanyAndStoreId")
    @Timed
    public ResponseEntity<List<BamReturnDO>> selectByCompanyAndStoreId(BamQueryDO bamQueryDO) throws Exception{
        log.info("BamBillLogResource->selectByCompanyAndStoreId,入参:{}", bamQueryDO);
        if(null==bamQueryDO){
            throw new BusinessErrorException("BamBillLogResource#入参为空");
        }
        log.info("Request to OrderSyncStoreInfoUtils transferBusinessIdToMdmComId businessId:{}，storeId:{}"
            ,bamQueryDO.getCompanyId(),bamQueryDO.getStoreId());
        if(null==bamQueryDO.getStoreId()){
            throw new BusinessErrorException("BamBillLogResource#入参门店ID为空");
        }
        MdmDataTransformDTO dataTransformDTO=new MdmDataTransformDTO();
        dataTransformDTO.setBusinessId(bamQueryDO.getCompanyId());
        dataTransformDTO.setDataType(MDM_SCRM_DATA_TYPE_BUSINESS);
        dataTransformDTO.setTransFormType(MDM_SCRM_TRANSFER_TYPE_TO_MDM);
        log.info("调用store连锁id转换comId传入参数=={}",dataTransformDTO);
        MdmDataTransformDTO mdmDataTransformDTO = null;
        try {
            mdmDataTransformDTO=storeService.transformMdmData(dataTransformDTO);
        }catch (Exception e){
            log.error("调用business转换连锁id异常",e);
            throw new BusinessErrorException("连锁信息转换失败");
        }
        if(mdmDataTransformDTO==null){
            return null;
        }

        String compId = mdmDataTransformDTO.getComId();
        List<Long> storeIds=new ArrayList<Long>();
        storeIds.add(Long.parseLong(bamQueryDO.getStoreId()));
        dataTransformDTO.setStoreIds(storeIds);
        dataTransformDTO.setDataType(MDM_SCRM_DATA_TYPE_STORE);
        log.info("调用store连锁id转换storeId传入参数=={}",dataTransformDTO);
        try {
            mdmDataTransformDTO=storeService.transformMdmData(dataTransformDTO);
        }catch (Exception e){
            log.error("调用store转换连锁id异常",e);
            throw new BusinessErrorException("门店信息转换失败");
        }
        if(mdmDataTransformDTO==null){
            return null;
        }
        String storeId = "";
        List<String> storeNos=mdmDataTransformDTO.getStoreNos();
        log.info("调用store门店编码转换返回参数compId:{}",compId);
        if(!CollectionUtils.isEmpty(storeNos)){
            storeId = storeNos.get(0);
        }
        log.info("调用store门店编码转换返回参数storeId:{}",storeId);
        if(StringUtils.isNotBlank(compId) && StringUtils.isNotBlank(storeId) ){
            bamQueryDO.setCompanyId(compId);
            bamQueryDO.setStoreId(storeId);
            bamQueryDO.setStepId(STEP_ID_QHSQ);
            List<BamReturnDO> returnDOList = bamBusinessBillLogService.selectByCompanyAndStoreId(bamQueryDO);
            return ResponseEntity.ok().body(returnDOList);
        }else {
            throw new BusinessErrorException("转换后连锁或者门店为空");
        }

    }

    @GetMapping("/test")
    @Timed
    public ResponseEntity<List<BamReturnDO>> test(BamQueryDO bamQueryDO) throws Exception{
        hanaDataService.getHanaBdpData();
        return ResponseEntity.ok().body(null);
    }
}
