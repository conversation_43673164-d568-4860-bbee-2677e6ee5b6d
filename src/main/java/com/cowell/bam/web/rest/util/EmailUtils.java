package com.cowell.bam.web.rest.util;

import org.apache.commons.collections4.MapUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.mail.javamail.JavaMailSenderImpl;
import org.springframework.mail.javamail.MimeMessageHelper;

import javax.activation.DataSource;
import javax.mail.MessagingException;
import javax.mail.internet.MimeMessage;
import javax.mail.util.ByteArrayDataSource;
import java.io.ByteArrayOutputStream;
import java.util.Map;
import java.util.Objects;
import java.util.Properties;

/**
 * @author: yadi
 * @date: 2019-06-21 15:27
 **/
public class EmailUtils {

    private static Logger logger = LoggerFactory.getLogger(EmailUtils.class);

    public static void sendMail(String[] to, String subject, String text,ByteArrayOutputStream baos, String attachName) {
        try {
            JavaMailSenderImpl javaMailSender = new JavaMailSenderImpl();
            MimeMessage mimeMessage = initMailSender(javaMailSender);
            MimeMessageHelper helper = new MimeMessageHelper(mimeMessage, true);
            helper.setFrom("<EMAIL>");
            helper.setTo(to);
            helper.setSubject(subject);
            helper.setText(text,true);

            if (Objects.nonNull(baos)) {
                /*添加附件*/
                DataSource source = new ByteArrayDataSource(baos.toByteArray(), "application/msexcel");
                helper.addAttachment(attachName,source);
            }
            javaMailSender.send(mimeMessage);
            logger.info("[sendMail]发送邮件完成!|to:{}|subject:{}|text:{}", to, subject, text);
        } catch (Exception e) {
            logger.error(">>>>>> {} 任务 【{}】 连锁 发送邮件 失败。<<<<<<", to, subject, text, e);
        }
    }

    public static void sendBatchMail(String[] to, String subject, String text, Map<String, ByteArrayOutputStream> byteMap) {
        try {
            JavaMailSenderImpl javaMailSender = new JavaMailSenderImpl();
            MimeMessage mimeMessage = initMailSender(javaMailSender);
            MimeMessageHelper helper = new MimeMessageHelper(mimeMessage, true);
            helper.setFrom("<EMAIL>");
            helper.setTo(to);
            helper.setSubject(subject);
            helper.setText(text,true);

            if (MapUtils.isNotEmpty(byteMap)) {
                /*添加附件*/
                byteMap.forEach((attachName, baos)->{
                    DataSource source = new ByteArrayDataSource(baos.toByteArray(), "application/msexcel");
                    try {
                        helper.addAttachment(attachName,source);
                    } catch (MessagingException e) {
                        logger.warn("sendBatchMail|添加附件失败|attachName:{}.", attachName);
                    }
                });
            }
            javaMailSender.send(mimeMessage);
            logger.info("[sendBatchMail]发送邮件完成!|to:{}|subject:{}|text:{}", to, subject, text);
        } catch (Exception e) {
            logger.error("sendBatchMail| >>>>>> {} 任务 【{}】 连锁 发送邮件 失败。<<<<<<", to, subject, text, e);
        }
    }

    public static void sendMail(String[] to, String subject, String text,ByteArrayOutputStream baos) {
        sendMail(to, subject, text, baos, "detail.xls");
    }

    public static void sendMail(String[] to, String subject, String text) {
        sendMail(to, subject, text, null, null);
    }

    private static MimeMessage initMailSender(JavaMailSenderImpl javaMailSender){

        javaMailSender.setUsername("<EMAIL>");
        javaMailSender.setPassword("Gaoji002");
        javaMailSender.setPort(-1);
        javaMailSender.setHost("smtp.exmail.qq.com");
        javaMailSender.setDefaultEncoding("UTF-8");

        System.setProperty("mail.mime.splitlongparameters", "false");
        System.setProperty("mail.mime.charset", "UTF-8");

        Properties pros = new Properties();
        pros.put("mail.smtp.auth", true);
        pros.put("mail.smtp.starttls.enable", true);
        pros.put("mail.smtp.starttls.required", true);
        pros.put("mail.smtp.ssl.enable", true);
        pros.put("mail.smtp.port", 465);
        javaMailSender.setJavaMailProperties(pros);
        return javaMailSender.createMimeMessage();
    }

}
