package com.cowell.bam.web.rest.vo;

import io.swagger.annotations.ApiModelProperty;

public class BangJianViewVO {

    @ApiModelProperty("高济连锁id")
    private Long businessId;

    @ApiModelProperty("开始时间")
    private String startTime;

    @ApiModelProperty("结束时间")
    private String endTime;

    @ApiModelProperty("页数")
    private Integer pageNumber;

    @ApiModelProperty("条数")
    private Integer pageSize;

    public BangJianViewVO() {
    }

    public BangJianViewVO(Long businessId, String startTime, String endTime, Integer pageNumber, Integer pageSize) {
        this.businessId = businessId;
        this.startTime = startTime;
        this.endTime = endTime;
        this.pageNumber = pageNumber;
        this.pageSize = pageSize;
    }


    public Long getBusinessId() {
        return businessId;
    }

    public void setBusinessId(Long businessId) {
        this.businessId = businessId;
    }


    public String getStartTime() {
        return startTime;
    }

    public void setStartTime(String startTime) {
        this.startTime = startTime;
    }

    public String getEndTime() {
        return endTime;
    }

    public void setEndTime(String endTime) {
        this.endTime = endTime;
    }

    public Integer getPageNumber() {
        return pageNumber;
    }

    public void setPageNumber(Integer pageNumber) {
        this.pageNumber = pageNumber;
    }

    public Integer getPageSize() {
        return pageSize;
    }

    public void setPageSize(Integer pageSize) {
        this.pageSize = pageSize;
    }
}
