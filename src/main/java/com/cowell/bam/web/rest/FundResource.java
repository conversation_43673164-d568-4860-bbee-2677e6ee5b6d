package com.cowell.bam.web.rest;

import com.codahale.metrics.annotation.Timed;
import com.cowell.bam.domain.DiffDataCompareInfoWithBLOBs;
import com.cowell.bam.entity.Stock;
import com.cowell.bam.service.*;
import com.cowell.bam.service.dto.*;
import com.cowell.bam.service.dto.base.CommonResponse;
import com.cowell.bam.service.impl.ThirdService;
import com.cowell.bam.web.rest.errors.BusinessErrorException;
import com.cowell.bam.web.rest.util.PageHelperUtil;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.Lists;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.collections4.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * <AUTHOR>
 * @ProjectName bam
 * @Description:
 * @date 2020/07/22 11:05
 */
@Api(tags = "积分比对数据")
@RestController
@RequestMapping("/api")
public class FundResource {

    private final Logger log = LoggerFactory.getLogger(FundResource.class);

    @Autowired
    private HdDataService hdDataService;

    @Timed
    @GetMapping(value = {"/internal/fund/getHuananFundAccount","/fund/getHuananFundAccount"})
    @ApiOperation(value = "根据日期获取需要校对的积分账户")
    public List<PosFundDailyDTO> getHuananFundAccount(@RequestParam(value = "compId")String compId,
                                                      @RequestParam(value = "fundDate")String fundDate,
                                                      @RequestParam(value = "pageNo")Integer pageNo,
                                                      @RequestParam(value = "pageSize")Integer pageSize) {
        log.info("FundResource | getHuananFundAccount | compId:{},fundDate:{},pageNo:{},pageSize:{}",
            compId,fundDate,pageNo,pageSize);
        // 如有异常，直接抛出，调用方进行重试
        return hdDataService.queryFundAccount(compId,fundDate,pageNo,pageSize);
    }


    @Timed
    @GetMapping(value = {"/internal/fund/getHuananFundDetails","/fund/getHuananFundDetails"})
    @ApiOperation(value = "根据日期获取需要校对的积分明细")
    public List<PosFundDetailDTO> getHuananFundDetails(@RequestParam(value = "compId")String compId,
                                                       @RequestParam(value = "fundDate")String fundDate,
                                                       @RequestParam(value = "erpCode")String erpCode) {
        log.info("FundResource | getHuananFundDetails | compId:{},fundDate:{},erpCode:{}",
            compId,fundDate,erpCode);
        // 如有异常，直接抛出，调用方进行重试
        return hdDataService.queryFundDetails(compId,fundDate,erpCode);
    }

    @Timed
    @GetMapping(value = {"/internal/fund/getOracleTime","/fund/getOracleTime"})
    @ApiOperation(value = "测试数据库速度")
    public String getOracleTime(@RequestParam(value = "compId")String compId,
                                                       @RequestParam(value = "fundDate")String fundDate,
                                                       @RequestParam(value = "erpCode")String erpCode) {
        log.info("FundResource | getHuananFundDetails | compId:{},fundDate:{},erpCode:{}",
            compId,fundDate,erpCode);
        return hdDataService.getOracleTime(compId,fundDate,erpCode);
    }
}
