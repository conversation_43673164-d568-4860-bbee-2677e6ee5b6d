package com.cowell.bam.web.rest.util;

/**
 * redis key
 */
public class RedisKeysConstant {

    //工程名 stockcenter
    public static final String PROJECT_NAME = "BAM_";
    /**
     * MDM COMPID
     */
    public static final String STOCK_COMPID_MDM_KEY = "STOCK_COMPID_MDM_BUS_";

    public static final String STOCK_STOREID_MDM_KEY = "STOCK_STOREID_MDM_";

    public static final String STOCK_MDM_STORE_KEY = "STOCK_MDM_STORE_";

    public static final String MDM_BUSINESS_KEY = "MDM_BUSINESS_KEY_V1_";

    public static final String COMPARE_PROCESS = "COMPARE_PROCESS";

    public static final String CRM_STORE_KEY = PROJECT_NAME + "CRM_STOCK_INFO_KEY_";

    public static final String COMPARE_VERSION_KEY = PROJECT_NAME.concat("COMPARE_VERSION_KEY_");

    public static final String SEND_DIFF_DATA_2_HD_KEY = PROJECT_NAME + "sendDiffData2Hd_";


    public static final String PRICE_COMPARE_COMPLETED_STORE_CACHE_KEY = PROJECT_NAME + "PRICE_COMPARE_COMPLETED_STORE_";
}
