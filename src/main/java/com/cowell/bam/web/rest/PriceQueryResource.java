package com.cowell.bam.web.rest;

import com.alibaba.fastjson.JSONObject;
import com.cowell.bam.entity.ItemPrice;
import com.cowell.bam.service.HdDataService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 类说明
 *
 * @Author: liw
 * @Date: 2020-05-28 04:26
 */
@RestController
@RequestMapping("/api")
@Api(tags = "价格查询", description = "价格查询")
public class PriceQueryResource {

    private final Logger log = LoggerFactory.getLogger(CompareDataResource.class);

    @Autowired
    private HdDataService hdDataService;



    @GetMapping(value = {"/price/query/comIdAndBusNo"})
    @ApiOperation(value = "查询第三方门店商品价格")
    public ResponseEntity queryPriceByComIdAndBusNo(@RequestParam(value = "comId")String comId,
                                                    @RequestParam(value = "busNo")String busNo){
        List<ItemPrice> itemPriceList = hdDataService.queryPriceByComIdAndBusNo(comId,busNo);
        log.info("查询结果:{}", JSONObject.toJSONString(itemPriceList));
        return new ResponseEntity<>(itemPriceList,null, HttpStatus.OK);
    }

    @GetMapping(value = {"/price/query/comIdAndBusNoAndGoods"})
    @ApiOperation(value = "查询第三方门店商品价格")
    public ResponseEntity queryPriceByComIdAndBusNoAndGoods(@RequestParam(value = "comId")String comId,
                                                            @RequestParam(value = "busNo")String busNo,
                                                            @RequestParam(value = "goods")List<String> goods){
        List<ItemPrice> itemPriceList = hdDataService.queryPriceByComIdAndBusNoAndGoods(comId,busNo,goods);
        log.info("查询结果:{}", JSONObject.toJSONString(itemPriceList));
        return new ResponseEntity<>(itemPriceList,null, HttpStatus.OK);
    }

    @GetMapping(value = {"/price/query/queryPriceByComIdAndBusNoAndDate"})
    @ApiOperation(value = "查询第三方门店商品价格")
    public ResponseEntity queryPriceByComIdAndBusNoAndDate(@RequestParam(value = "comId")String comId,
                                                            @RequestParam(value = "busNo")String busNo,
                                                            @RequestParam(value = "queryDate")String queryDate){

        List<ItemPrice>  itemPriceList = hdDataService.queryPriceByComIdAndBusNoAndDate(comId,busNo,queryDate);
        log.info("查询结果:{}", JSONObject.toJSONString(itemPriceList));
        return new ResponseEntity<>(itemPriceList,null, HttpStatus.OK);
    }

    @GetMapping(value = {"/price/query/businessIdAndStoreIdAndGoods","/intranet/price/query/businessIdAndStoreIdAndGoods"})
    @ApiOperation(value = "查询第三方门店商品价格")
    public ResponseEntity queryPriceByBusinessIdAndStoreIdAndGoods(@RequestParam(value = "businessId")Long businessId,
                                                                   @RequestParam(value = "storeId")Long storeId,
                                                                   @RequestParam(value = "busNo")List<String> goods){
        List<ItemPrice>  itemPriceList = hdDataService.queryPriceByBusinessIdAndStoreIdAndGoods(businessId,storeId,goods);
        log.info("查询结果:{}", JSONObject.toJSONString(itemPriceList));
        return new ResponseEntity<>(itemPriceList,null, HttpStatus.OK);
    }


    @GetMapping(value = {"/price/query/count"})
    @ApiOperation(value = "查询第三方门店商品价格数量")
    public ResponseEntity queryPriceCount(@RequestParam(value = "businessId")Long businessId,
                                          @RequestParam(value = "storeId")Long storeId,
                                          @RequestParam(value = "comId")String comId,
                                          @RequestParam(value = "busNo")String busNo){

        List<ItemPrice>  itemPriceList = hdDataService.queryPriceCount(businessId,storeId,comId,busNo);
        log.info("查询结果:{}", JSONObject.toJSONString(itemPriceList));
        return new ResponseEntity<>(itemPriceList,null, HttpStatus.OK);
    }

    @GetMapping(value = {"/price/query/countByDate"})
    @ApiOperation(value = "查询第三方门店商品价格数量")
    public ResponseEntity queryPriceCount(@RequestParam(value = "businessId")Long businessId,
                                          @RequestParam(value = "storeId")Long storeId,
                                          @RequestParam(value = "comId")String comId,
                                          @RequestParam(value = "busNo")String busNo,
                                          @RequestParam(value = "queryDate")String queryDate){

        List<ItemPrice>  itemPriceList =hdDataService.queryPriceCountByData(businessId,storeId,comId,busNo,queryDate);
        log.info("查询结果:{}", JSONObject.toJSONString(itemPriceList));
        return new ResponseEntity<>(itemPriceList,null, HttpStatus.OK);
    }

}
