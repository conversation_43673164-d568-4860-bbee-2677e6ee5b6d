package com.cowell.bam.web.rest.util;

/**
 * 常量池
 */
public class ConstantPool {

    public static final String STOCK_COMPID_MDM_KEY = RedisKeysConstant.PROJECT_NAME + RedisKeysConstant.STOCK_COMPID_MDM_KEY;

    public static final String STOCK_STORE_MDM_KEY = RedisKeysConstant.PROJECT_NAME + RedisKeysConstant.STOCK_STOREID_MDM_KEY;

    public static final String STOCK_MDM_STORE_KEY = RedisKeysConstant.PROJECT_NAME + RedisKeysConstant.STOCK_MDM_STORE_KEY;

    public static final String MDM_BUSINESS_KEY = RedisKeysConstant.PROJECT_NAME + RedisKeysConstant.MDM_BUSINESS_KEY;

    public static final String STOCK_APPLY_DATE_CACHE_KEY =  RedisKeysConstant.PROJECT_NAME + "STOCK_APPLY_DATE_";
    public static final String STOCK_AND_PRICE_APPLY_DATE_CACHE_KEY =  RedisKeysConstant.PROJECT_NAME + "STOCK_AND_PRICE_APPLY_DATE_";
    public static final String COMPARE_INFO = RedisKeysConstant.PROJECT_NAME + RedisKeysConstant.COMPARE_PROCESS;


    public static final String ERPSAAS_APPNAME = "pos";

    public static final String BTYPE_STOCK = "6034";
    public static final String STOCK_REQUEST_URL = "/gaoji-web/v_1_0/stockrecord/demand.do";
    public static final String SAP_STOCK_REQUEST_URL = "/gaoji-web/v_1_0/stockrecord/demand.do";
    public static final String YK_STOCK_REQUEST_URL = "/gdzqbjyy_nrt_service/NRTControl";
    public static final String PRICE_REQUEST_URL = "/gaoji-web/v_1_0/storeprice/demand.do";

    public static final String BTYPE_PRICE = "2009";
    public static final String YYYYMMDD = "yyyyMMdd";
    public static final String YYYY_MM_DD = "yyyy-MM-dd";
    public static Integer TRANSIT_ALL_VERSION = 9999;
    public static String TRANSIT_ALL_DATE = "9999";
    public static String HD_SYSTEM = "602";


    /**
     * POS 请求状态 1.已发送
     */
    public static final String POS_RESPONSE_STATUS_SENT = "1";
    /**
     * POS 请求状态 2.已接受
     */
    public static final String POS_RESPONSE_STATUS_ACCEPTED = "2";
    /**
     * POS 请求状态 3.发送失败
     */
    public static final String POS_RESPONSE_STATUS_FAILURE = "3";
    /**
     * POS 请求状态 4.处理中
     */
    public static final String POS_RESPONSE_STATUS_PROCESSING = "4";
    /**
     * POS 请求状态 5.处理成功
     */
    public static final String POS_RESPONSE_STATUS_SUCCESS = "5";
    /**
     * POS 请求状态 6.处理异常
     */
    public static final String POS_RESPONSE_STATUS_EXCEPTION = "6";
}
