package com.cowell.bam.web.rest;

import com.codahale.metrics.annotation.Timed;
import com.cowell.bam.service.StockPurchaseReportService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.nio.charset.StandardCharsets;
import java.util.Map;


@Api(tags = "海典进货数据导出")
@RestController
@RequestMapping("/api")
public class ReportPurchasesResource {

    @Autowired
    private StockPurchaseReportService StockPurchaseReportService;


    @Timed
    @GetMapping({"/purchase/data/file","/internal/purchase/data/file"})
    @ApiOperation(value = "获取海典数据文件")
    public ResponseEntity HDPurchaseReport(@ApiParam(name="startDate",value = "开始时间-2021-05-23 00:00:00") @RequestParam(value = "startDate",required = false)String startDate,
                                 @ApiParam(name="endDate",value = "开始时间-2021-05-24 23:59:59") @RequestParam(value = "endDate",required = false)String endDate,
                                 @ApiParam(name="busiNo",value = "门店四位编码") @RequestParam(value = "busiNo") String busiNo)throws Exception {
        String report= StockPurchaseReportService.HDPurchaseReport(startDate,endDate,busiNo);
        if(StringUtils.equals(report,"海典查询数据不存在")){
            return new ResponseEntity<>(report,null, HttpStatus.OK);
        }
        HttpHeaders headers = new HttpHeaders();
        byte[] bytes = report.getBytes(StandardCharsets.UTF_8);
        headers.setContentType(MediaType.APPLICATION_OCTET_STREAM);
        headers.setContentLength(bytes.length);
        headers.setContentDispositionFormData("attachment", busiNo+".xml");
        return new ResponseEntity<>(bytes,headers, HttpStatus.OK);
    }

    @Timed
    @GetMapping("/purchase/data/xml")
    @ApiOperation(value = "获取海典数据xml")
    public ResponseEntity queryHDListXml(@ApiParam(name="startDate",value = "开始时间-2021-05-23 00:00:00")@RequestParam(value = "startDate",required = false)String startDate,
                                         @ApiParam(name="endDate",value = "开始时间-2021-05-24 23:59:59")@RequestParam(value = "endDate",required = false)String endDate,
                                         @ApiParam(name="busiNo",value = "门店四位编码") @RequestParam(value = "busiNo") String busiNo)throws Exception {
        String report= StockPurchaseReportService.HDPurchaseReport(startDate,endDate,busiNo);
        return new ResponseEntity<>(report,null, HttpStatus.OK);
    }

    @Timed
    @GetMapping("/purchase/query")
    @ApiOperation(value = "查询海典数据")
    public ResponseEntity queryHDListQuery(@ApiParam(name="startDate",value = "开始时间-2021-05-23 00:00:00") @RequestParam(value = "startDate",required = false)String startDate,
                                           @ApiParam(name="endDate",value = "开始时间-2021-05-24 23:59:59") @RequestParam(value = "endDate",required = false)String endDate,
                                           @ApiParam(name="busiNo",value = "门店四位编码") @RequestParam(value = "busiNo",required = false) String busiNo)throws Exception {
        Map<String,Object> result = StockPurchaseReportService.queryHDList(startDate,endDate,busiNo);
        return new ResponseEntity<>(result,null, HttpStatus.OK);
    }




}
