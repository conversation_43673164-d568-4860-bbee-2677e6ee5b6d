//package com.cowell.bam.web.rest;
//
//import com.codahale.metrics.annotation.Timed;
//import com.cowell.bam.service.HanaDataService;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.web.bind.annotation.GetMapping;
//import org.springframework.web.bind.annotation.RequestMapping;
//import org.springframework.web.bind.annotation.RestController;
//
///**
// * <AUTHOR>
// * @date: 2018/12/27 20:48
// * @description:
// */
//@RestController
//@RequestMapping("api/test")
//public class TestResource {
//    @Autowired
//    private HanaDataService hanaDataService;
//    @GetMapping("/categorySale/getCategorySaleInfoList")
//    @Timed
//    public void getCategorySaleInfoList() {
//        try {
//            hanaDataService.getHanaBdpData();
//        } catch (Exception e) {
//            e.printStackTrace();
//        }
//    }
//    }
