package com.cowell.bam.web.rest.errors;

import org.zalando.problem.AbstractThrowableProblem;
import org.zalando.problem.Status;

import java.net.URI;
import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR> on 2018/5/2
 */
public class BusinessErrorException extends AbstractThrowableProblem {

    private final String errorCode;

    private final String errorMessage;

    public BusinessErrorException(ErrorCodeEnum errorCodeEnum) {
        this(ErrorConstants.DEFAULT_TYPE, errorCodeEnum.getCode(), errorCodeEnum.getMsg());
    }

    public BusinessErrorException(String msg) {
        this(ErrorConstants.DEFAULT_TYPE, "99999", msg);
    }

    public BusinessErrorException(String errorCode, String errorMessage) {
        this(ErrorConstants.DEFAULT_TYPE, errorCode, errorMessage);
    }

    public BusinessErrorException(URI type, String errorCode, String errorMessage) {
        super(type, errorMessage, Status.BAD_REQUEST, null, null, null, getErrorParameters(errorCode, errorMessage));
        this.errorCode = errorCode;
        this.errorMessage = errorMessage;
    }

    public String getErrorCode() {
        return errorCode;
    }

    public String getErrorMessage() {
        return errorMessage;
    }

    private static Map<String, Object> getErrorParameters(String errorCode, String errorMessage) {
        Map<String, Object> parameters = new HashMap<>();
        parameters.put("errorCode", errorCode);
        parameters.put("errorMessage", errorMessage);
        return parameters;
    }

}

