package com.cowell.bam.web.rest.util;

import org.apache.commons.collections.CollectionUtils;

import java.util.ArrayList;
import java.util.List;

/**
 * @auther: jbliu
 * @date: 2018/9/12 09:51
 * @description:
 */
public class StringUtil {

    public static List<Long> transStrToInteger(List<String> list){
        if(CollectionUtils.isEmpty(list)){
            return null;
        }
        List<Long> resultList = new ArrayList<>(list.size());
        for(String str:list){
            resultList.add(Long.valueOf(str));
        }

        return resultList;

    }

    public static List<String> transLongToStr(List<Long> list){
        if(CollectionUtils.isEmpty(list)){
            return null;
        }
        List<String> resultList = new ArrayList<>(list.size());
        for(Long item:list){
            resultList.add(String.valueOf(item));
        }

        return resultList;

    }

    // 判断一个字符串是否都为数字
    public static boolean isDigit(String strNum) {
        return strNum.matches("[0-9]{1,}");
    }

    public static boolean isNumber(String str) {
        String pattern = "^[-\\+]?[\\d]+[.{1}][\\d]+$";
        return str.matches(pattern);

    }

    public static boolean isInt(String str) {
        String reg = "0|([-]?[1-9][0-9]*)";
        return str.matches(reg);
    }

    }
