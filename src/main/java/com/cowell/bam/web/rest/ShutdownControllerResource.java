package com.cowell.bam.web.rest;

import com.codahale.metrics.annotation.Timed;
import com.netflix.discovery.DiscoveryManager;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * REST controller for managing BusinessInfo.
 */
@RestController
@RequestMapping("/controller")
public class ShutdownControllerResource {

    private final Logger log = LoggerFactory.getLogger(ShutdownControllerResource.class);

    private static final String ENTITY_NAME = "shutdownclient";

    public ShutdownControllerResource() {
    }

    @PostMapping("/shutdown-client")
    @Timed
    public void shutdownDiscoveryClient () {
        log.info("REST request to SHUTDOWN discovery client");
        DiscoveryManager.getInstance().getEurekaClient().shutdown();

    }

}
