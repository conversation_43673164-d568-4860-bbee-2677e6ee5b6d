package com.cowell.bam.web.rest;

import com.cowell.bam.entity.Stock;
import com.cowell.bam.service.HdDataService;
import com.cowell.bam.service.IThirdPlatformService;
import com.cowell.bam.service.StockCompareService;
import com.cowell.bam.service.StockTransitCompareService;
import com.cowell.bam.service.dto.ThirdPlatformStockDTO;
import com.cowell.bam.web.rest.vo.StockVO;
import com.cowell.bam.xxlJob.DeleteHistoryCompareRecordsJobHandler;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;
import java.util.Map;

import static com.cowell.bam.enums.NotifySyncStockTypeEnum.STOCK_CHECK;

@Slf4j
@RestController
@Api(tags = "库存中心对比管理", description = "库存中心对比管理")
@RequestMapping("/api")
public class StockCenterCompareResource {


    @Autowired
    private StockCompareService stockCompareService;
    @Autowired
    private StockTransitCompareService stockTransitCompareService;

    @Autowired
    private HdDataService hdDataService;

    @Autowired
    private DeleteHistoryCompareRecordsJobHandler deleteHistoryCompareRecordsJobHandler;

    @Autowired
    private IThirdPlatformService thirdPlatformService;


    @GetMapping(value = {"/stock/compare/compareStockByComId"})
    @ApiOperation(value = "根据连锁对比库存")
    public ResponseEntity compareStockByComId(@RequestParam(value = "comId") String comId) {
        stockCompareService.compare(comId);
        return new ResponseEntity(true, HttpStatus.OK);
    }

    @GetMapping(value = {"/stock/compare/distributeCompareStock"})
    @ApiOperation(value = "根据连锁对比库存")
    public ResponseEntity distributeCompareStock(@RequestParam(value = "comId") String comId) {
        stockCompareService.distributeCompare(comId);
        return new ResponseEntity(true, HttpStatus.OK);
    }

    @GetMapping(value = {"/stock/compare/compareTransitStockByComId"})
    @ApiOperation(value = "根据连锁对比在途库存")
    public ResponseEntity compareTransitStockByComId(@RequestParam(value = "comId") String comId) {
        stockTransitCompareService.compareTransitByCom(comId);
        return new ResponseEntity(true, HttpStatus.OK);
    }

    @GetMapping(value = {"/stock/compare/compareStockPosByComId"})
    @ApiOperation(value = "根据连锁对比库存(删除线下不存在的库存)")
    public ResponseEntity compareStockPosByComId(@RequestParam(value = "comId") String comId) {
        stockCompareService.comparePos(comId);
        return new ResponseEntity(true, HttpStatus.OK);
    }


    @GetMapping(value = {"/stock/compare/compareByStoreNo"})
    @ApiOperation(value = "对比单个门店货品库存情况")
    public ResponseEntity<Boolean> compareByStoreNo(@RequestParam(value = "comId") String comId, @RequestParam(value = "storeNo") String storeNo) {
        stockCompareService.compareStock(comId, storeNo);
        return new ResponseEntity(true, HttpStatus.OK);
    }

    @GetMapping(value = {"/stock/compare/compareStockByGoods"})
    @ApiOperation(value = "对比单个门店货品库存情况")
    public ResponseEntity<Boolean> compareStockByGoods(@RequestParam(value = "comId") String comId, @RequestParam(value = "storeNo") String storeNo) {
        stockCompareService.compareStockByGoods(comId, storeNo);
        return new ResponseEntity(true, HttpStatus.OK);
    }

    @GetMapping(value = {"/stock/compare/compareTransitByStoreNo"})
    @ApiOperation(value = "对比单个门店货品在途库存情况")
    public ResponseEntity<Boolean> compareTransitByStoreNo(@RequestParam(value = "comId") String comId, @RequestParam(value = "storeNo") String storeNo) {
        stockTransitCompareService.compareTransitByStoreNo(comId, storeNo);
        return new ResponseEntity(true, HttpStatus.OK);
    }

    @GetMapping(value = {"/stock/compare/comparePosByStoreNo"})
    @ApiOperation(value = "对比单个门店货品库存情况(删除线下不存在的库存)")
    public ResponseEntity<Boolean> comparePosByStoreNo(@RequestParam(value = "comId") String comId, @RequestParam(value = "storeNo") String storeNo) {
        stockCompareService.comparePosStock(comId, storeNo);
        return new ResponseEntity(true, HttpStatus.OK);
    }

    @GetMapping(value = {"/stock/compare/queryYKStoreCount"})
    @ApiOperation(value = "获取英克连锁下各个门店库存总数")
    public ResponseEntity<Map<String, Integer>> queryYKStoreCount(@RequestParam(value = "comId") String comId) {
        return new ResponseEntity(stockCompareService.queryYKStoreCount(comId), HttpStatus.OK);
    }

    @GetMapping(value = {"/stock/compare/deleteHistoryRecords"})
    @ApiOperation(value = "删除库存中心对比历史数据")
    public ResponseEntity<Boolean> deleteHistoryRecords(@RequestParam(value = "param") String param) {
        deleteHistoryCompareRecordsJobHandler.execute(param);
        return new ResponseEntity(true, HttpStatus.OK);
    }


    @PostMapping(value = {"/stock/compare/notifySAPSendStockData","/internal/stock/compare/notifySAPSendStockData"})
    @ApiOperation(value = "通知SAP发送库存数据")
    public ResponseEntity<Boolean> notifySAPSendStockData(@RequestBody ThirdPlatformStockDTO dto) {
        if(StringUtils.isBlank(dto.getType())){
            dto.setType(STOCK_CHECK.getNotifyType());
        }
        thirdPlatformService.notifySAPSendStockData(dto);
        return new ResponseEntity(true, HttpStatus.OK);
    }


    @PostMapping(value = {"/stock/compare/queryHdStockInfo", "/noauth/stock/compare/queryHdStockInfo"})
    @ApiOperation(value = "获取海典物料库存信息", notes = "获取海典物料库存信息")
    public ResponseEntity queryHdStockInfo(@RequestBody @Valid StockVO param) {

        return new ResponseEntity<List<Stock>>(hdDataService.queryPosStockInfo(param.getComId(),
            param.getBusNo(), param.getGoodsNos()), HttpStatus.OK);
    }

}
