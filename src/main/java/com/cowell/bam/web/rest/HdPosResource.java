package com.cowell.bam.web.rest;


import com.cowell.bam.service.HdDataService;
import com.cowell.bam.service.dto.HdOrderDTO;
import io.swagger.annotations.ApiOperation;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;


/**
 * <AUTHOR> xia
 * @ProjectName bam
 * @Description:
 * @date 2020/06/09
 */

@RestController
@RequestMapping("/api")
public class HdPosResource {

    private final Logger log = LoggerFactory.getLogger(CompareDataResource.class);

    @Autowired
    private HdDataService hdDataService;

    /**
     * 查询海典订单视图
     * @param orderCreateTime
     * @param dataSource
     * @param pageNumber,pageSize
     * @return
     */
    @GetMapping(value = {"/internal/queryHdOrderView", "/queryHdOrderView"})
    @ApiOperation(value = "查询海典订单视图")
    public List<HdOrderDTO> queryHdOrderInfo(@RequestParam("orderCreateTime") String orderCreateTime,
                                             @RequestParam("orderEndTime") String orderEndTime,
                                             @RequestParam("dataSource")String dataSource,
                                             @RequestParam("pageNumber")Integer pageNumber,
                                             @RequestParam("pageSize")Integer pageSize){
        // 如有异常，直接抛出，调用方进行重试。
        return hdDataService.queryHdOrderInfo(orderCreateTime, orderEndTime ,dataSource, pageNumber, pageSize);
    }

    /**
     * 查询海典订单视图
     * @param orderCreateTime
     * @param dataSource
     * @param pageNumber,pageSize
     * @return
     */
    @GetMapping(value = {"/internal/queryHdOrderViewByStatus", "/queryHdOrderViewByStatus"})
    @ApiOperation(value = "查询海典订单视图")
    public List<HdOrderDTO> queryHdOrderInfo(@RequestParam("orderCreateTime") String orderCreateTime,
                                             @RequestParam("orderEndTime") String orderEndTime,
                                             @RequestParam("dataSource")String dataSource,
                                             @RequestParam("status")Integer status,
                                             @RequestParam("pageNumber")Integer pageNumber,
                                             @RequestParam("pageSize")Integer pageSize){
        // 如有异常，直接抛出，调用方进行重试。
        return hdDataService.queryHdOrderInfo(orderCreateTime, orderEndTime ,dataSource, status, pageNumber, pageSize);
    }

    /**
     * 根据scrm_userid,cashno,scrm_compid查询海典订单视图
     * @param scrmUserid
     * @param cashno
     * @param scrmCompid
     * @return
     */
    @GetMapping(value = {"/internal/queryHdOrderViewByUserIdAndCashnoAndCompid","/queryHdOrderViewByUserIdAndCashnoAndCompid"})
    @ApiOperation(value = "查询海典订单视图")
    public List<HdOrderDTO> queryHdOrderViewByUserIdAndcashnoAndCompid(@RequestParam("scrmUserid") Long scrmUserid,
                                             @RequestParam("cashno") String cashno,
                                             @RequestParam("scrmCompid")String scrmCompid){
        // 如有异常，直接抛出，调用方进行重试。
        return hdDataService.queryHdOrderViewByScrmUserIdAndcashnoAndScrmCompid(scrmUserid, cashno ,scrmCompid);
    }

    /**
     * 根据scrm_userid,businessId查询积分视图
     * @param scrmUserid
     * @param businessId
     * @param scrmCount
     * @return
     */
    @GetMapping(value = {"/noauth/queryFundViewByUserIdAndBusinessId"})
    @ApiOperation(value = "查询积分视图")
    public List<HdOrderDTO> queryFundViewByUserIdAndBusinessId(@RequestParam("scrmUserid") Long scrmUserid,
                                                                     @RequestParam("businessId") Long businessId,
                                                                     @RequestParam(value = "scrmCount", defaultValue = "100", required = false) Integer scrmCount){
        // 如有异常，直接抛出，调用方进行重试。
        return hdDataService.queryFundViewByUserIdAndBusinessId(scrmUserid, businessId, scrmCount);
    }
}
