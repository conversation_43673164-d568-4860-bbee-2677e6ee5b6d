package com.cowell.bam.web.rest;


import com.cowell.bam.service.BangJianService;
import com.cowell.bam.service.dto.BangJianViewDTO;
import com.cowell.bam.service.dto.HdOrderDTO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;


/**
 * <AUTHOR>
 * @ProjectName bam
 * @Description:
 * @date 2021/01/14
 */
@Api(tags = "邦健数据查询")
@RestController
@RequestMapping("/api")
public class BangJianResource {

    private final Logger log = LoggerFactory.getLogger(BangJianResource.class);

    @Autowired
    private BangJianService bjService;

    /**
     * 查询邦健视图
     *
     * @return
     */
    @GetMapping(value = {"/internal/queryBangjianInfo", "/queryBangjianInfo"})
    @ApiOperation(value = "查询邦健视图")
    List<BangJianViewDTO> queryBangjianInfo(@RequestParam(value = "businessId") Long businessId,
                                            @RequestParam(value = "startTime") String startTime,
                                            @RequestParam(value = "endTime") String endTime,
                                            @RequestParam(value = "pageNumber") Integer pageNumber,
                                            @RequestParam(value = "pageSize") Integer pageSize) {
        return bjService.queryBangjianInfo(businessId, startTime, endTime, pageNumber, pageSize);
    }

    /**
     * 查询邦健视图
     *
     * @return
     */
    @GetMapping(value = "/queryOneByBusinessIdAndCode")
    @ApiOperation(value = "查询邦健单条数据")
    public BangJianViewDTO queryOneByBusinessIdAndCode(@RequestParam(value = "businessId") Long businessId,
                                                       @RequestParam(value = "code") String code) {
        return bjService.queryOneByBusinessIdAndCode(businessId, code);
    }

    /**
     * 按bdid查询邦健积分订单视图，校验积分状态
     *
     * @return
     */
    @GetMapping(value = {"/internal/queryBjOrderForCheckFund", "/queryBjOrderForCheckFund"})
    @ApiOperation(value = "按bdid查询邦健积分订单视图，校验积分状态")
    public List<HdOrderDTO> queryBjOrderForCheckFund(@RequestParam("userId") Long userId, @RequestParam("bdid") String bdid, @RequestParam("compId") String compId) {
        log.info("按bdid查询邦健积分订单视图，校验积分状态:{},{},{}",userId,bdid,compId);
        List<HdOrderDTO> result= bjService.queryBjOrderForCheckFund(userId, bdid, compId);
        return result;
    }

    @GetMapping(value = {"/internal/queryPageBjOrderForCheckFund", "/queryPageBjOrderForCheckFund"})
    @ApiOperation(value = "分页查询邦健积分订单视图，校验积分状态")
    public List<HdOrderDTO> queryPageBjOrderForCheckFund(@RequestParam("orderCreateTime") String orderCreateTime,
                                             @RequestParam("orderEndTime") String orderEndTime,
                                             @RequestParam("dataSource")String dataSource,
                                             @RequestParam("pageNumber")Integer pageNumber,
                                             @RequestParam("pageSize")Integer pageSize){
        log.info("分页查询邦健积分订单视图，校验积分状态:{},{},{},{},{}",orderCreateTime,orderEndTime,dataSource,pageNumber,pageSize);
        return bjService.queryPageBjOrderForCheckFund(orderCreateTime, orderEndTime ,dataSource, pageNumber, pageSize);
    }
}
