package com.cowell.bam.web.rest.util;

import com.cowell.bam.web.rest.errors.BusinessErrorException;
import com.cowell.bam.web.rest.errors.ErrorCodeEnum;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public  class ResponseUtils<T> {
    private static final Logger logger = LoggerFactory.getLogger(ResponseUtils.class);
        private  T data;

        private String code;

        private String message;

        public ResponseUtils() {
            this(null, null, null);
        }

        public ResponseUtils(T data) {
            this(data, null, null);
        }

        public ResponseUtils(ErrorCodeEnum code) {
            this(null, code.getCode(), code.getMsg());
        }

        public ResponseUtils(String code, String message) {
            this(null, code, message);
        }

        public ResponseUtils(T data, ErrorCodeEnum code) {
            this(data, code.getCode(), code.getMsg());
        }

        public ResponseUtils(T data, String code, String message) {
            this.data = data;
            this.code = code;
            this.message = message;
        }

        public static ResponseUtils  exceptionResponse(Exception e){
            if(e instanceof BusinessErrorException){
                BusinessErrorException bee = (BusinessErrorException)e;
                logger.info("执行[ResponseUtils.exceptionResponse]|验证信息：{}",e);
                return new ResponseUtils(bee.getErrorCode(),bee.getErrorMessage());
            }else{
                logger.error("执行[ResponseUtils.exceptionResponse]|系统错误：{}",e);
                return new ResponseUtils(ErrorCodeEnum.SYSTEM_ERROR.getCode(),
                    ErrorCodeEnum.SYSTEM_ERROR.getMsg());
            }
        }
    public ResponseUtils  exceptionResponse(T data, Exception e){
        if(e instanceof BusinessErrorException){
            BusinessErrorException bee = (BusinessErrorException)e;
            logger.info("执行[ResponseUtils.exceptionResponse]|验证信息：{}",e);
            return new ResponseUtils(data,bee.getErrorCode(),bee.getErrorMessage());
        }else{
            logger.error("执行[ResponseUtils.exceptionResponse]|系统错误：{}",e);
            return new ResponseUtils(data,ErrorCodeEnum.SYSTEM_ERROR.getCode(),
                ErrorCodeEnum.SYSTEM_ERROR.getMsg());
        }
    }
        public T getdata() {
            return data;
        }

        public void setdata(T data) {
            this.data = data;
        }

        public String getCode() {
            return code;
        }

        public void setCode(String code) {
            this.code = code;
        }

        public String getMessage() {
            return message;
        }

        public void setMessage(String message) {
            this.message = message;
        }


        public void setBusinessCode(ErrorCodeEnum code) {
            this.code = code.getCode();
            this.message = code.getMsg();
        }
    }
