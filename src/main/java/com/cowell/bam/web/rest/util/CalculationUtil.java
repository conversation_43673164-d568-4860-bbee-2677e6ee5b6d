package com.cowell.bam.web.rest.util;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.math.BigDecimal;
import java.text.NumberFormat;

/**
 * <AUTHOR> on 2018/6/7
 */
public class CalculationUtil {

    private static final Logger log = LoggerFactory.getLogger(CalculationUtil.class);

    /**
     * 计算同比/环比
     * @param todayValue 当前数据值
     * @param refValue 参考数据值
     * @return resultValue 商
     */
    public static BigDecimal getRatio(BigDecimal todayValue, BigDecimal refValue) {
        BigDecimal resultValue = null;
        if(todayValue == null || refValue == null || refValue.compareTo(BigDecimal.ZERO) == 0) {
            return null;
        }
        try {
            resultValue = (todayValue.subtract(refValue)).divide(refValue, 4,BigDecimal.ROUND_HALF_UP);
        }catch (Exception e) {
            log.warn("[CalculationUtil.getRatio]| Calculation exception: e {}", e);
            return null;
        }
        return resultValue;
    }

    /**
     * 除法
     * @param dividend 被除数
     * @param divisor 除数
     * @return resultValue 商
     */
    public static BigDecimal getDivideValue(BigDecimal dividend , BigDecimal divisor) {
        BigDecimal resultValue = null;
        if(dividend == null || divisor == null || divisor.compareTo(BigDecimal.ZERO) == 0) {
            return null;
        }
        try {
            resultValue = dividend.divide(divisor,4,BigDecimal.ROUND_HALF_UP);
        }catch (Exception e) {
            log.warn("[CalculationUtil.getRatio]| Calculation exception:{}", e);
        }
        return resultValue;
    }

    /**
     * 加法
     * @param first 被加数
     * @param second 加数
     * @return resultValue 和
     */
    public static BigDecimal add(BigDecimal first , BigDecimal second) {
        BigDecimal resultValue = null;
        if(first == null && second == null) {
            return null;
        }
        if(first == null) {
            return second.setScale(4, BigDecimal.ROUND_HALF_UP);
        }
        if(second == null) {
            return first.setScale(4, BigDecimal.ROUND_HALF_UP);
        }
        return first.add(second).setScale(4, BigDecimal.ROUND_HALF_UP);
    }

    /**
     * 将参数格式化
     * @param sourceValue 原参数
     * @param precisionScale 精度
     * @param isPercent 是否转换为百分数形式
     * @return resultValue
     */
    public static String format(BigDecimal sourceValue, int precisionScale, boolean isPercent) {
        String resultValue = "0";
        if(sourceValue == null) {
            return resultValue;
        }

        if(sourceValue.compareTo(BigDecimal.ZERO) == 0 ) {
            resultValue = "0";
            return resultValue;
        }

        if(isPercent) {
            NumberFormat percent = NumberFormat.getPercentInstance();
            percent.setMaximumFractionDigits(precisionScale);
            resultValue = percent.format(sourceValue);
        } else {
            sourceValue = sourceValue.setScale(precisionScale, BigDecimal.ROUND_HALF_UP);
            resultValue = sourceValue.toString();
        }
        return resultValue;
    }

}
