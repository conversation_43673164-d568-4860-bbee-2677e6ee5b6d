package com.cowell.bam.web.rest;

import com.codahale.metrics.annotation.Timed;
import com.cowell.bam.domain.DiffDataCompareInfoWithBLOBs;
import com.cowell.bam.entity.Stock;
import com.cowell.bam.service.*;
import com.cowell.bam.service.dto.*;
import com.cowell.bam.service.dto.base.CommonResponse;
import com.cowell.bam.service.impl.ThirdService;
import com.cowell.bam.web.rest.errors.BusinessErrorException;
import com.cowell.bam.web.rest.util.PageHelperUtil;
import com.cowell.cstore.commonstarter.common.annotation.SecurityAudit;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.Lists;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.collections4.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * <AUTHOR>
 * @ProjectName erpsaas
 * @Description:
 * @date 2018/09/19 11:05
 */
@Api(tags = "海典比对数据")
@RestController
@RequestMapping("/api")
public class CompareDataResource {

    private final Logger log = LoggerFactory.getLogger(CompareDataResource.class);

    @Autowired
    private IPriceCenterFeignService priceCenterFeignService;

    @Autowired
    private DataCompareService dataCompareService;
    @Autowired
    private HdDataService hdDataService;

    @Autowired
    private ThirdService thirdService;

    @Autowired
    private StockCompareService stockCompareService;

    @Autowired
    private StockTransitCompareService stockTransitCompareService;

    @Autowired
    private PriceCompareService priceCompareService;

    @Timed
    @GetMapping("/compareData/diffList")
    @ApiOperation(value = "数据比对不一致列表")
    @SecurityAudit(apiAction = "compareDataDiffList")
    public ResponseEntity<Object> diffList(Pageable pageable, @Valid DiffDataRequestDTO diffDataRequestDTO) {
        PageInfo<DiffDataResponseDTO> pageInfo = dataCompareService.diffCompareList(pageable, diffDataRequestDTO);
        HttpHeaders httpHeaders = PageHelperUtil.generatePaginationHttpHeaders(pageInfo);
        return new ResponseEntity<>(pageInfo.getList(),httpHeaders, HttpStatus.OK);
    }


    @Timed
    @GetMapping("/compareData/diffCollect")
    @ApiOperation(value = "数据比对不一致连锁汇总")
    @SecurityAudit(apiAction = "compareDataDiffCollect")
    public ResponseEntity<Object> diffCollect(Pageable pageable, @Valid DiffDataRequestDTO diffDataRequestDTO) {
        PageInfo<DiffCollectDataResponseDTO> pageInfo = dataCompareService.diffCollect(pageable, diffDataRequestDTO);
        HttpHeaders httpHeaders = PageHelperUtil.generatePaginationHttpHeaders(pageInfo);
        return new ResponseEntity<>(pageInfo.getList(),httpHeaders, HttpStatus.OK);
    }

    @Timed
    @GetMapping("/compareData/diffDetail")
    @ApiOperation(value = "数据比对不一致列表")
    public ResponseEntity<Object> diffDetail(@RequestParam Long id) {
        StockGoodsBaseDTO data = dataCompareService.diffCompareDetail(id);
        return new ResponseEntity<>(Lists.newArrayList(data),HttpStatus.OK);
    }
    @Timed
    @GetMapping("/query/hdData")
    @ApiOperation(value = "查询海典视图数据")
    public ResponseEntity<Object> acceptBdpNotice(Integer type,String comId){
        boolean result = dataCompareService.acceptBdpData(type,comId);
        return new ResponseEntity<>(Lists.newArrayList(result),HttpStatus.OK);
    }

    @Timed
    @GetMapping("/delete")
    @ApiOperation(value = "删除生成的差异数据")
    public ResponseEntity<Object> deleteDiffData(Long businessId,Integer type,String time){
        Integer num = dataCompareService.deleteDiffData(businessId,type,time);
        return new ResponseEntity<>(Lists.newArrayList(num),HttpStatus.OK);
    }

    @Timed
    @GetMapping("/batch/delete")
    @ApiOperation(value = "删除生成的差异数据")
    public ResponseEntity<Object> batchDeleteDiffData(String businessIds,Integer type,String time){
        Integer num = dataCompareService.batchDeleteDiffData(businessIds,type,time);
        return new ResponseEntity<>(Lists.newArrayList(num),HttpStatus.OK);
    }




    @Timed
    @GetMapping("/query/count")
    @ApiOperation(value = "查询海典视图数据")
    public ResponseEntity<Object> acceptBdpNotice(@RequestParam String comid){
        return new ResponseEntity<>(hdDataService.queryHdData(comid),HttpStatus.OK);
    }

    @Timed
    @PostMapping(value = {"/hd/noticeHdRecorrectStock","/internal/hd/noticeHdRecorrectStock"})
    @ApiOperation(value = "通知海典矫正库存")
    public ResponseEntity<Object> sendStockAllData(@RequestBody RecorrectStockDTO recorrectStockDTO){

        stockCompareService.noticeHdRecorrectStock(recorrectStockDTO);

        return new ResponseEntity<>(true,HttpStatus.OK);
    }
    @Timed
    @GetMapping("/hd/sendStockAllData")
    @ApiOperation(value = "发送门店给海典")
    public ResponseEntity<Object> sendStockAllData(@RequestParam("businessId") Long businessId,@RequestParam(value = "storeId",required = false)Long storeId){

        if (storeId == null) {
            List<Long> storeIdList = thirdService.getStoreIdsByBusinessId(businessId);
            if (CollectionUtils.isEmpty(storeIdList)) {
                throw new BusinessErrorException("根据连锁id:"+businessId+"查询不到门店id列表");
            }
            storeIdList.stream().forEach(sid ->{
                stockCompareService.sendDiffData2Hd(businessId, sid);
            });
        }else {
            stockCompareService.sendDiffData2Hd(businessId, storeId);
        }
        return new ResponseEntity<>(true,HttpStatus.OK);
    }

    @Timed
    @GetMapping("/hd/sendTransitStockAllData")
    @ApiOperation(value = "发送门店在途给海典")
    public ResponseEntity<Object> sendTransitStockAllData(@RequestParam("businessId") Long businessId,@RequestParam(value = "storeId",required = false)Long storeId,@RequestParam(value = "transitStock",required = false)Integer transitStock){

        if (storeId == null) {
            List<Long> storeIdList = thirdService.getStoreIdsByBusinessId(businessId);
            if (CollectionUtils.isEmpty(storeIdList)) {
                throw new BusinessErrorException("根据连锁id:"+businessId+"查询不到门店id列表");
            }
            storeIdList.stream().forEach(sid ->{
                stockTransitCompareService.sendDiffData2Hd(businessId, sid, transitStock);
            });
        }else {
            stockTransitCompareService.sendDiffData2Hd(businessId, storeId, transitStock);
        }
        return new ResponseEntity<>(true,HttpStatus.OK);
    }

    @Timed
    @GetMapping("/hd/sendPriceAllData")
    @ApiOperation(value = "发送门店给海典")
    public ResponseEntity<Object> sendPriceAllData(@RequestParam("businessId") Long businessId,@RequestParam("storeId")Long storeId){
        List<DiffDataCompareInfoWithBLOBs> list = new ArrayList<>();
        DiffDataCompareInfoWithBLOBs diffDataCompareInfoWithBLOB = new DiffDataCompareInfoWithBLOBs();
        diffDataCompareInfoWithBLOB.setBusinessId(businessId);
        diffDataCompareInfoWithBLOB.setStoreId(storeId);
        list.add(diffDataCompareInfoWithBLOB);
        priceCompareService.send2HD(list);
        return new ResponseEntity<>(true,HttpStatus.OK);
    }

    @Timed
    @GetMapping("/hd/sendGoodsPriceData")
    @ApiOperation(value = "发送门店给海典")
    public ResponseEntity<Object> sendGoodsPriceData(@RequestParam("businessId") Long businessId,
                                                     @RequestParam("storeId")Long storeId,
                                                     @RequestParam("goodsNo")String goodsNos){
        List<DiffDataCompareInfoWithBLOBs> list = new ArrayList<>();
        String[] split = goodsNos.split(",");
        for (String goodsNo : split) {
            DiffDataCompareInfoWithBLOBs diffDataCompareInfoWithBLOB = new DiffDataCompareInfoWithBLOBs();
            diffDataCompareInfoWithBLOB.setBusinessId(businessId);
            diffDataCompareInfoWithBLOB.setStoreId(storeId);
            diffDataCompareInfoWithBLOB.setGoodsNo(goodsNo);
            list.add(diffDataCompareInfoWithBLOB);
        }

        priceCompareService.send2HD(list);
        return new ResponseEntity<>(true,HttpStatus.OK);
    }

    @Timed
    @GetMapping("/hd/compareAndSendGoodsPriceData")
    @ApiOperation(value = "发送门店给海典")
    public ResponseEntity<Object> compareAndSendGoodsPriceData(@RequestParam("businessId") Long businessId,
                                                     @RequestParam(name = "storeId", required = false) Long storeId,
                                                     @RequestParam(name = "goodsNo", required = false) String goodsNos){
        List<DiffDataCompareInfoWithBLOBs> list = new ArrayList<>();
        String[] split = goodsNos.split(",");
        for (String goodsNo : split) {
            DiffDataCompareInfoWithBLOBs diffDataCompareInfoWithBLOB = new DiffDataCompareInfoWithBLOBs();
            diffDataCompareInfoWithBLOB.setBusinessId(businessId);
            diffDataCompareInfoWithBLOB.setStoreId(storeId);
            diffDataCompareInfoWithBLOB.setGoodsNo(goodsNo);
            list.add(diffDataCompareInfoWithBLOB);
        }

        priceCompareService.send2HD(list);
        return new ResponseEntity<>(true,HttpStatus.OK);
    }


    @Timed
    @PostMapping(value = {"/internal/hd/sendParam2Hd","/hd/sendParam2Hd"})
    @ApiOperation(value = "根据门店和商品编码让海典重新推送矫正库存")
    public ResponseEntity<Object> sendParam2Hd(@RequestBody HDDataInfoDTO hdDataInfoDTO){
        log.info("CompareDataResource|sendParam2Hd|根据商品编码通知海典推送库存comId:{},busNo:{},details:{}",
            hdDataInfoDTO.getComId(),hdDataInfoDTO.getBusNo(),hdDataInfoDTO.getCompareDataInfoList());
        stockCompareService.sendParam2HD(hdDataInfoDTO.getComId(),hdDataInfoDTO.getBusNo(),hdDataInfoDTO.getCompareDataInfoList());
        return new ResponseEntity<>(true,HttpStatus.OK);
    }


    @GetMapping(value = "/priceCenterFeignService")
    @ApiOperation(value = "根据门店和商品编码让海典重新推送矫正库存")
    @Timed
    public void priceCenterFeignService(@RequestParam("businessId") Long businessId,@RequestParam("storeId")Long storeId,@RequestParam("goodsNos")String goodsNos) {

        PriceQueryParam priceQueryParam = new PriceQueryParam();

        priceQueryParam.setBusinessId(businessId);
        priceQueryParam.setStoreId(storeId);
        priceQueryParam.setGoodsNoList(Arrays.asList(goodsNos.split(",")));


        CommonResponse<List<PriceQueryGoodsNoDTO>> commonResponse = priceCenterFeignService.getPriceByStoreIdAndRelateNo(priceQueryParam);
        log.info("commonResponse:{}",commonResponse);

        List<PriceQueryGoodsNoDTO> priceQueryGoodsNoDTOList = priceCenterFeignService.getPriceByStoreIdAndRelateNoLists(priceQueryParam);
        log.info("priceQueryGoodsNoDTOList:{}",priceQueryGoodsNoDTOList);

    }


    /**
     * 查询海典商品价格，O2O商品上架判断使用
     * @param businessId
     * @param storeId
     * @param goodsNos
     * @return
     */
    @GetMapping(value = {"/internal/queryBJStoreGoodsStock","/noauth/queryBJStoreGoodsStock"})
    @ApiOperation(value = "查询邦建门店商品库存")
    public List<Stock> queryBJStoreGoodsStock(@RequestParam("businessId") Long businessId, @RequestParam("storeId")Long storeId
        , @RequestParam(value = "goodsNos",required = false) List<String> goodsNos){
        try {
            return hdDataService.queryBjStockData(businessId, storeId, goodsNos);
        }catch (Exception e){
            log.error("|queryStoreGoodsPrice|查询邦健库存信息异常",e);
        }
        return Lists.newArrayList();
    }


}
