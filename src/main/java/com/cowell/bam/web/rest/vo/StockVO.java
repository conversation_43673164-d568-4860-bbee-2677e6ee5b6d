package com.cowell.bam.web.rest.vo;


import lombok.Data;
import org.hibernate.validator.constraints.NotBlank;
import org.hibernate.validator.constraints.NotEmpty;

import java.io.Serializable;
import java.util.List;

@Data
public class StockVO implements Serializable{
    /**
     * 连锁编码
     */
    @NotBlank
    private String comId ;
    /**
     * 门店编码
     */
    @NotBlank
    private String busNo ;
    /**
     * 物料集合
     */
    @NotEmpty
    private List<String> goodsNos ;

    @Override
    public String toString() {
        return "StockVO{" +
            "comId='" + comId + '\'' +
            ", busNo='" + busNo + '\'' +
            ", goodsNos=" + goodsNos +
            '}';
    }
}
