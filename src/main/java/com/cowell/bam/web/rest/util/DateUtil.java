package com.cowell.bam.web.rest.util;

import com.cowell.bam.web.rest.errors.BusinessErrorException;
import com.cowell.bam.web.rest.errors.ErrorCodeEnum;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.Instant;
import java.time.LocalDate;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Calendar;
import java.util.Date;
import java.util.Objects;
import java.util.regex.Pattern;

/**
 * <AUTHOR> on 2018/5/7
 */
public class DateUtil {
    public final static Logger log = LoggerFactory.getLogger(DateUtil.class);
    public final static ZoneId ZONE_ID = ZoneId.systemDefault();
    public final static String TIME_FORMAT = "yyyy-MM-dd HH:mm:ss";
    public final static String DATE_FORMAT = "yyyy-MM-dd";
    public final static String DATE_FORMAT_YYYYMMDDHHMM = "yyyy-MM-dd HH:mm";
    public final static String DATE_FORMAT_BDP = "yyyy-MM-dd HH:mm:ss.SSS";
    public final static String DOT_DATE_FORMAT = "yyyy.MM.dd";
    public final static String DOT_DATE_TIME_FORMAT = "yyyy.MM.dd HH:mm:ss";
    public final static String HHmm = "HH:mm";

    private final static String START_OF_MONTH_SUFFIX = "-01 00:00:00";
    private final static String START_OF_DAY_SUFFIX = " 00:00:00";
    private final static String REGEX_DATE_YEAR = "[0-9]{4}";//"2018"
    private final static String REGEX_DATE_MONTH = "[0-9]{4}-[0-9]{2}";//"2018-01"
    private final static String REGEX_DATE_DAY = "[0-9]{4}-[0-9]{2}-[0-9]{2}";//"2018-01-01"

    private static DateTimeFormatter format = DateTimeFormatter.ofPattern(TIME_FORMAT).withZone(ZONE_ID);
    private static DateTimeFormatter format_BDP = DateTimeFormatter.ofPattern(DATE_FORMAT_BDP).withZone(ZONE_ID);
    private static SimpleDateFormat dotDateFormat = new SimpleDateFormat(DOT_DATE_FORMAT);
    private static SimpleDateFormat dotDateTimeFormat = new SimpleDateFormat(DOT_DATE_TIME_FORMAT);
    private static SimpleDateFormat hyphenDateTimeFormat = new SimpleDateFormat(TIME_FORMAT);

    /**
     * JDK8日期转换 instant->string
     *
     * @param instant
     * @return
     */
    public static String conventDateStrByDate(Instant instant) {
        Date date = Date.from(instant);
        SimpleDateFormat format = new SimpleDateFormat(TIME_FORMAT);
        return format.format(date);
    }

    public static ZonedDateTime convertDateStrToZdt(String dateStr) {
        DateTimeFormatter dtFormatter = DateTimeFormatter.ofPattern(TIME_FORMAT).withZone(ZONE_ID);
        ZonedDateTime dtDateTime = ZonedDateTime.parse(dateStr, dtFormatter);
        return dtDateTime.withZoneSameInstant(ZONE_ID);
    }


    /**
     * 校验字符串是否为规定时间格式
     *
     * @param str
     * @return
     */
    public static boolean checkDateStr(String str) {
        boolean ret = false;
        try {
            if (getDateByString(str) != null) {
                ret = true;
            }
        } catch (Exception e) {
        }
        return ret;

    }

    /**
     * 将时间转换为规定格式的字符串
     *
     * @param date
     * @return
     */
    public static String getStringByDate(Date date) {
        return date != null ? format.format(date.toInstant()) : null;
    }

    /**
     * 将时间转换为格式："yyyy.MM.dd"
     *
     * @param date
     * @return
     */
    public static String dotDateFormat(Date date) {
        return date == null ? "" : dotDateFormat.format(date);
    }

    public static Date dotDateStrToDate(String dateStr) {
        try {
            return org.springframework.util.StringUtils.isEmpty(dateStr) ? null : dotDateFormat.parse(dateStr);
        } catch (ParseException e) {
            log.error("[DateUtil.dotDateStrToDate]|dateStr:{}", dateStr, e);
            e.printStackTrace();
        }
        return null;
    }

    public static String dotDateTimeFormat(Date date) {
        return date == null ? "" : dotDateTimeFormat.format(date);
    }

    /**
     * 将字符串按照规定格式转换为时间
     *
     * @param str
     * @return
     */
    public static Date getDateByString(String str) {
        try {
            return !StringUtils.isEmpty(str) ? Date.from(ZonedDateTime.parse(str, format).toInstant()) : null;
        } catch (Exception e) {
            throw new BusinessErrorException(ErrorCodeEnum.DATE_PARSE_EXCEPTION);
        }
    }

    /**
     * 将字符串按照规定格式转换为时间
     *
     * @param str
     * @return
     */
    public static Date getDateByString4BDP(String str) {
        try {

            return !StringUtils.isEmpty(str) ? Date.from(ZonedDateTime.parse(str, format_BDP).toInstant()) : null;
        } catch (Exception e) {
            throw new BusinessErrorException(ErrorCodeEnum.DATE_PARSE_EXCEPTION);
        }
    }


    public static Date getDate(String str) {
        try {
            return !StringUtils.isEmpty(str) ? hyphenDateTimeFormat.parse(str) : null;
        } catch (Exception e) {
            throw new BusinessErrorException(ErrorCodeEnum.DATE_PARSE_EXCEPTION);
        }
    }

    /**
     * 将时间转换为规定格式的字符串
     *
     * @param date
     * @return
     */
    public static String getStringByZonedDateTime(ZonedDateTime date) {
        return date != null ? format.format(date.toInstant()) : null;
    }

    /**
     * 将字符串按照规定格式转换为时间
     *
     * @param str
     * @return
     */
    public static ZonedDateTime getZonedDateTimeByString(String str) {
        ZonedDateTime ret = null;
        try {
            if (!StringUtils.isEmpty(str)) {
                ret = ZonedDateTime.parse(str, format);
                ret = ret.withZoneSameInstant(ZONE_ID);
            }
        } catch (Exception e) {
            log.error(Thread.currentThread().getId() + "::" + str + "--" + getDateByString(str));
            throw new BusinessErrorException(ErrorCodeEnum.DATE_PARSE_EXCEPTION);
        }
        return ret;
    }

    /**
     * 将字符串按照规定格式转换为时间
     *
     * @param str
     * @return
     */
    public static ZonedDateTime getZonedDateTimeOrNullByString(String str) {
        ZonedDateTime ret = null;
        try {
            if (!StringUtils.isEmpty(str)) {
                ret = ZonedDateTime.parse(str, format);
                ret = ret.withZoneSameInstant(ZONE_ID);
            }
        } catch (Exception e) {
            log.error(Thread.currentThread().getId() + "::" + str + "--" + getDateByString(str));
//            throw new BusinessErrorException(ErrorCodeEnum.DATE_PARSE_EXCEPTION);
        } finally {
            return ret;
        }
    }

    public static Date getDateOrNullByString(String str) {
        Date ret = null;
        ZonedDateTime zonedDateTime = getZonedDateTimeOrNullByString(str);
        if (zonedDateTime != null) {
            ret = Date.from(ZonedDateTime.parse(str, format).toInstant());
        }
        return ret;
    }

    public static Instant stringToInstant(String instant) {
        return StringUtils.isEmpty(instant) ? Instant.now() : convertDateStrToZdt(instant).toInstant();
    }

    public static boolean isYear(String dateStr) {
        if (StringUtils.isEmpty(dateStr)) {
            return false;
        }
        return Pattern.compile(REGEX_DATE_YEAR).matcher(dateStr).matches();
    }

    public static boolean isYearAndMonth(String dateStr) {
        if (StringUtils.isEmpty(dateStr)) {
            return false;
        }
        return Pattern.compile(REGEX_DATE_MONTH).matcher(dateStr).matches();
    }

    public static boolean isYearAndMonthAndDay(String dateStr) {
        if (StringUtils.isEmpty(dateStr)) {
            return false;
        }
        return Pattern.compile(REGEX_DATE_DAY).matcher(dateStr).matches();
    }

    public static String getDateStrForTarget(String dateStr) {
        if (StringUtils.isEmpty(dateStr)) {
            return null;
        }
        if (isYearAndMonth(dateStr)) {
            return dateStr.concat(START_OF_MONTH_SUFFIX);
        }
        if (isYearAndMonthAndDay(dateStr)) {
            return dateStr.concat(START_OF_DAY_SUFFIX);
        }
        return null;
    }

    public static String dateToStr(Date date, String pattern) {
        if ((date == null)) {
            return null;
        }
        SimpleDateFormat formatter = new SimpleDateFormat(pattern);
        return formatter.format(date);
    }

    public static Date getDate(String date, String pattern) {
        if (StringUtils.isBlank(date)){
            return null;
        }
        try {
            SimpleDateFormat formatter = new SimpleDateFormat(pattern);
            return formatter.parse(date);
        } catch (Exception e) {
            throw new BusinessErrorException(ErrorCodeEnum.DATE_PARSE_EXCEPTION);
        }
    }

    public static boolean checkDate(String dateStr,String dateFormat){
        boolean isTrue = false;
        SimpleDateFormat formateTemp = new SimpleDateFormat("hh:mm");
        try {
            if(StringUtils.isNotEmpty(dateStr)) {
                Date t = formateTemp.parse(dateStr);
                isTrue = true;
            }
        }
        catch (Exception ex) {
            return isTrue;
        }
        return isTrue;
    }

    public static Instant dateToInstant(Date date) {
        return date != null ? date.toInstant() : null;
    }

    public static Date localDateToDate(LocalDate localDate) {
        ZoneId zone = ZoneId.systemDefault();
        Instant instant = localDate.atStartOfDay().atZone(zone).toInstant();
        Date date = Date.from(instant);
        return date;
    }

    public static String localDateToString(LocalDate localDate, String pattern) {
        try {
            Date date = localDateToDate(localDate);
            String dateStr = dateToStr(date, pattern);
            return dateStr;
        } catch (Exception e) {
            throw new BusinessErrorException(ErrorCodeEnum.DATE_PARSE_EXCEPTION);
        }
    }

    public static Date parse(String dateStr, String pattern) {
        if (org.apache.commons.lang.StringUtils.isBlank(dateStr)) {
            return null;
        }
        SimpleDateFormat sf = new SimpleDateFormat(pattern);
        try {
            return sf.parse(dateStr);
        } catch (ParseException e) {
            log.error("=====================>>时间格式化失败:", e);
            return null;
        }
    }


    /**
     * 获取指定时间前几天
     */
    public static Date getPreviousDay(Date date, int days) {
        if (Objects.isNull(date)) {
            return null;
        }
        Calendar c = Calendar.getInstance();
        c.setTime(date);
        c.set(Calendar.DAY_OF_MONTH, c.get(Calendar.DAY_OF_MONTH) - days);
        Date time = c.getTime();
        return time;
    }


     /**
     * 获取当前时间前3分钟
     * @param date
     * @param minutes
     * @return
     */
    public static Date getPreviousMinutes(Date date, int minutes){
        if (Objects.isNull(date)) {
            return null;
        }
        Calendar c = Calendar.getInstance();
        c.setTime(date);
        c.set(Calendar.MINUTE, c.get(Calendar.MINUTE) - minutes);
        return c.getTime();
    }

}
