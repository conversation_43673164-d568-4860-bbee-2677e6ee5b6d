package com.cowell.bam.web.rest;

import com.cowell.bam.entity.GoodsCounterDO;
import com.cowell.bam.entity.Stock;
import com.cowell.bam.entity.WareDxDateVO;
import com.cowell.bam.service.HdDataService;
import com.cowell.bam.service.impl.ThirdService;
import com.cowell.bam.web.rest.vo.ApiParamVo;
import com.cowell.bam.web.rest.vo.HdDxParamVO;
import io.swagger.annotations.ApiOperation;
import java.util.ArrayList;
import java.util.List;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

/**
 * Controller for view and managing Log Level at runtime.
 */
@RestController
@RequestMapping("/api")
public class ApiResource{
    private final Logger log = LoggerFactory.getLogger(ApiResource.class);
    @Autowired
    private ThirdService thirdService;
    @Autowired
    private HdDataService hdDataService;


    @PostMapping("/store/getStoreComId")
    @ApiOperation(value = "获取门店comid", notes = "获取门店comid")
    public ResponseEntity getStoreComId(@RequestBody ApiParamVo apiParamVo) {
        String comId = thirdService.getComId(apiParamVo.getBusinessId());
        return new ResponseEntity<>(comId, HttpStatus.OK);
    }

    @PostMapping("/noauth/getHdItemInfo")
    @ApiOperation(value = "获取海典的商品信息", notes = "获取海典的商品信息")
    public ResponseEntity getHdItemInfo(@RequestBody ApiParamVo apiParamVo) {

        List<Stock> list = hdDataService.queryHdStockData(apiParamVo.getBusinessId(), apiParamVo.getStoreId(), apiParamVo.getGoodsNos());
        return new ResponseEntity<List<Stock>>(list, HttpStatus.OK);
    }

    @GetMapping("/noauth/test")
    @ApiOperation(value = "获取海典的商品信息", notes = "获取海典的商品信息")
    public ResponseEntity test() {

        return new ResponseEntity<>("21", HttpStatus.OK);
    }

    @PostMapping("/noauth/testPost")
    public ResponseEntity testPost(@RequestBody ApiParamVo apiParamVo) {
        List<Stock> list = new ArrayList<>();
        return new ResponseEntity<List<Stock>>(list, HttpStatus.OK);
    }

    @PostMapping("/mpos/queryGoodsCounterInfo")
    @ApiOperation(value = "获取商品柜组信息", notes = "获取商品柜组信息")
    public ResponseEntity queryGoodsCounterInfo(@RequestBody ApiParamVo apiParamVo) {
        List<GoodsCounterDO> goodsCounterDOS = hdDataService.queryGoodsCounterInfo(apiParamVo);
        return new ResponseEntity<>(goodsCounterDOS, HttpStatus.OK);
    }

    /**
     * 库存变动
     */
    @PostMapping(value = {"/mpos/getDXHdItemInfo","/internal/mpos/getDXHdItemInfo"})
    @ApiOperation(value = "获取动销海典的商品信息", notes = "获取动销海典的商品信息")
    public ResponseEntity getDXHdItemInfo(@RequestBody ApiParamVo apiParamVo) {
        List<WareDxDateVO> list = hdDataService.getDXHdItemInfo(apiParamVo.getBusinessId(), apiParamVo.getStoreId(), apiParamVo.getLastDxDate(),apiParamVo.getStartDxDate(), apiParamVo.getGoodsNos());
        return new ResponseEntity<>(list, HttpStatus.OK);
    }

    /**
     * 功    能：销售和退货库存变动
     * 作    者：郜玉皓
     * 时    间：2023-06-06
     */
    @PostMapping(value = {"/mpos/getHdDxGoodsList", "/internal/mpos/getHdDxGoodsList"})
    @ApiOperation(value = "获取动销海典的商品信息（销售和退货库存变动）", notes = "获取动销海典的商品信息（销售和退货库存变动）")
    public ResponseEntity getHdDxGoodsList(@RequestBody HdDxParamVO param) {
        List<WareDxDateVO> list = hdDataService.getHdDxGoodsList(param);
        return new ResponseEntity<>(list, HttpStatus.OK);
    }

    /**
     * 功    能：销售和退货库存变动总条数
     * 作    者：郜玉皓
     * 时    间：2023-06-08
     */
    @PostMapping(value = {"/mpos/getHdDxGoodsListCount", "/internal/mpos/getHdDxGoodsListCount"})
    @ApiOperation(value = "获取动销海典的商品总条数", notes = "获取动销海典的商品总条数")
    public ResponseEntity getHdDxGoodsListCount(@RequestBody HdDxParamVO param) {
        Integer totalCount = hdDataService.getHdDxGoodsListCount(param);
        return new ResponseEntity<>(totalCount, HttpStatus.OK);
    }


}
