package com.cowell.bam.entity;

import lombok.Data;

/**
 * <AUTHOR>
 * @date 2023/9/21 13:55
 */
@Data
public class ItemBaseChangeDTO {
    private ColumnsList afterColumnsList;
    private ColumnsList beforeColumnsList;
    private String destination;
    private Boolean oneToOneRelation;
    private String schemaName;
    private String tableName;
    private Integer type;

    @Data
    public static class ColumnsList {
        private String gmt_create;
        private String search_pv;
        private Integer is_soldout;
        private String merchant_code;
        private Integer flag_bin;
        private Integer item_type;
        private Integer buy_stock;
        private String features;
        private String out_item_info;
        private Integer price;
        private String drug_type;
        private String mnemonic_code;
        private String sort_value;
        private Integer spu_id;
        private Integer stock;
        private String sync_date;
        private String sync_price_time;
        private String created_by;
        private Integer version;
        private Integer brand_id;
        private String show_type;
        private String out_org_code;
        private String extend_3;
        private String extend_2;
        private String extend_1;
        private Integer status;
        private String search_property;
        private Integer flag;
        private Long item_union_id;
        private Long bg_cate_id;
        private String img_head;
        private String out_item_code;
        private String off_shelf_time;
        private String is_quality;
        private Integer convert_price;
        private String cash_no;
        private Integer turnover;
        private Long store_id;
        private String brief;
        private Integer sold;
        private String binlog_dump_biz_type;
        private Long item_id;
        private String item_name;
        private String convert_integral;
        private String last_modified_by;
        private String convert_type;
        private String gmt_update;
        private String template_id;
        private Long business_id;
    }
}
