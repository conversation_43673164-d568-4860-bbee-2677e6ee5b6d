package com.cowell.bam.entity;

import lombok.Data;

/**
 * <AUTHOR>
 * @ProjectName bam
 * @Description:
 * @date 2019/10/24 15:44
 */
@Data
public class ItemPrice {
    /**
     * 门店编码
     */
    private String busno;
    /**
     * 连锁编码
     */
    private String compid;
    /**
     * 零售价
     */
    private String saleprice;
    /**
     * 会员价
     */
    private String memprice;
    /**
     * 拆零价
     */
    private String minprice;
    /**
     * 会员拆零价
     */
    private String memminprice;
    /**
     * 商品编码
     */
    private String warecode;
    /**
     * 价格
     */
    private String price;
    /**
     * 价格类型 1 零售价 2 会员价
     */
    private String type;
    /**
     * 价格总数量
     */
    private Long total;

    /**
     * 创建时间
     */
    private String credate;

    /**
     * 同步时间
     */
    private String syncDate;
    /**
     * 是否特价 0否   1是
     */
    private Integer isSpecial;
}
