package com.cowell.bam.entity;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @ProjectName bam
 * @Description:
 * @date 2019/10/24 15:44
 */
public class Stock {
    private String orgname;
    private String warecode;
    private String barcode;
    private String makeno;
    private String expiredate;
    private String unit;
    private String tnum;
    private String num;
    private String syncdate;
    private String batchno;
    private String zonename;
    private String qualifiedstock;
    private String checkstock;
    private String unqualifiedstock;

    private String busno;
    private String compid;

    private Integer total;

    private Date syncTime;

    /**
     * 请货在途
     */
    private String ztqtyqh;
    /**
     * 调拨在途
     */
    private String ztqtydb;
    /**
     * 铺货在途
     */
    private String ztqtyph;

    //上面三者相加=在途
    private String transitStock;

    /**
     * 门店名称
     */
    private String storeName;

    /**
     * 有效类型(0 不管控 ，1 近效期销售提醒 ，2 近效期禁止销售)
     */
    private Integer expDateType;

    public String getStoreName() {
        return storeName;
    }

    public void setStoreName(String storeName) {
        this.storeName = storeName;
    }

    public String getTransitStock() {
        return transitStock;
    }

    public void setTransitStock(String transitStock) {
        this.transitStock = transitStock;
    }

    public String getZtqtyqh() {
        return ztqtyqh;
    }

    public void setZtqtyqh(String ztqtyqh) {
        this.ztqtyqh = ztqtyqh;
    }

    public String getZtqtydb() {
        return ztqtydb;
    }

    public void setZtqtydb(String ztqtydb) {
        this.ztqtydb = ztqtydb;
    }

    public String getZtqtyph() {
        return ztqtyph;
    }

    public void setZtqtyph(String ztqtyph) {
        this.ztqtyph = ztqtyph;
    }

    public Integer getTotal() {
        return total;
    }

    public void setTotal(Integer total) {
        this.total = total;
    }

    public String getOrgname() {
        return orgname;
    }

    public void setOrgname(String orgname) {
        this.orgname = orgname;
    }

    public String getWarecode() {
        return warecode;
    }

    public void setWarecode(String warecode) {
        this.warecode = warecode;
    }

    public String getBarcode() {
        return barcode;
    }

    public void setBarcode(String barcode) {
        this.barcode = barcode;
    }

    public String getMakeno() {
        return makeno;
    }

    public void setMakeno(String makeno) {
        this.makeno = makeno;
    }

    public String getExpiredate() {
        return expiredate;
    }

    public void setExpiredate(String expiredate) {
        this.expiredate = expiredate;
    }

    public String getUnit() {
        return unit;
    }

    public void setUnit(String unit) {
        this.unit = unit;
    }

    public String getTnum() {
        return tnum;
    }

    public void setTnum(String tnum) {
        this.tnum = tnum;
    }

    public String getNum() {
        return num;
    }

    public void setNum(String num) {
        this.num = num;
    }

    public String getSyncdate() {
        return syncdate;
    }

    public void setSyncdate(String syncdate) {
        this.syncdate = syncdate;
    }

    public String getBatchno() {
        return batchno;
    }

    public void setBatchno(String batchno) {
        this.batchno = batchno;
    }

    public String getZonename() {
        return zonename;
    }

    public void setZonename(String zonename) {
        this.zonename = zonename;
    }

    public String getQualifiedstock() {
        return qualifiedstock;
    }

    public void setQualifiedstock(String qualifiedstock) {
        this.qualifiedstock = qualifiedstock;
    }

    public String getCheckstock() {
        return checkstock;
    }

    public void setCheckstock(String checkstock) {
        this.checkstock = checkstock;
    }

    public String getUnqualifiedstock() {
        return unqualifiedstock;
    }

    public void setUnqualifiedstock(String unqualifiedstock) {
        this.unqualifiedstock = unqualifiedstock;
    }

    public String getBusno() {
        return busno;
    }

    public void setBusno(String busno) {
        this.busno = busno;
    }

    public String getCompid() {
        return compid;
    }

    public void setCompid(String compid) {
        this.compid = compid;
    }

    public Date getSyncTime() {
        return syncTime;
    }

    public void setSyncTime(Date syncTime) {
        this.syncTime = syncTime;
    }

    public Integer getExpDateType() {
        return expDateType;
    }

    public void setExpDateType(Integer expDateType) {
        this.expDateType = expDateType;
    }

    @Override
    public String toString() {
        return "Stock{" +
            "orgname='" + orgname + '\'' +
            ", warecode='" + warecode + '\'' +
            ", barcode='" + barcode + '\'' +
            ", makeno='" + makeno + '\'' +
            ", expiredate='" + expiredate + '\'' +
            ", unit='" + unit + '\'' +
            ", tnum='" + tnum + '\'' +
            ", num='" + num + '\'' +
            ", syncdate='" + syncdate + '\'' +
            ", batchno='" + batchno + '\'' +
            ", zonename='" + zonename + '\'' +
            ", qualifiedstock='" + qualifiedstock + '\'' +
            ", checkstock='" + checkstock + '\'' +
            ", unqualifiedstock='" + unqualifiedstock + '\'' +
            ", busno='" + busno + '\'' +
            ", compid='" + compid + '\'' +
            ", total=" + total +
            ", syncTime=" + syncTime +
            ", ztqtyqh='" + ztqtyqh + '\'' +
            ", ztqtydb='" + ztqtydb + '\'' +
            ", ztqtyph='" + ztqtyph + '\'' +
            ", transitStock='" + transitStock + '\'' +
            ", storeName='" + storeName + '\'' +
            ", expDateType=" + expDateType +
            '}';
    }
}
