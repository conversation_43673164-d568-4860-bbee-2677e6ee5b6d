package com.cowell.bam.entity;

public class PurchaseStockMain {
    private String trans_type;//业务交易类型	购进上报填写为 1
    private String s_ent_name;//供应商名
//    private String b_ent_name;//购进企业名称	可省
    private String trans_date;//购进交易日期	格式如：2012-04-05
    private String create_date;//数据创建导入时间	格式如：2012-04-05
    private String report_user;//导入人
//    private String user_link;//导入人联系方式	可省
    private String if_network;//IF_NETWORK_N	（xml文件导入时为0）
    private String s_licence_no;//供货企业许可证		境外采购如没有许可证号请填“境外”
    private String s_ent_type;//供货企业类型	生产为：1；批发为：2;又是生产又是批发:6
    private String s_ent_address;//供货企业地址
    private String mdm_busno;

    public PurchaseStockMain() {
    }

    public String getTrans_type() {
        return trans_type;
    }

    public void setTrans_type(String trans_type) {
        this.trans_type = trans_type;
    }

    public String getS_ent_name() {
        return s_ent_name;
    }

    public void setS_ent_name(String s_ent_name) {
        this.s_ent_name = s_ent_name;
    }

    public String getTrans_date() {
        return trans_date;
    }

    public void setTrans_date(String trans_date) {
        this.trans_date = trans_date;
    }

    public String getCreate_date() {
        return create_date;
    }

    public void setCreate_date(String create_date) {
        this.create_date = create_date;
    }

    public String getReport_user() {
        return report_user;
    }

    public void setReport_user(String report_user) {
        this.report_user = report_user;
    }

    public String getIf_network() {
        return if_network;
    }

    public void setIf_network(String if_network) {
        this.if_network = if_network;
    }

    public String getS_licence_no() {
        return s_licence_no;
    }

    public void setS_licence_no(String s_licence_no) {
        this.s_licence_no = s_licence_no;
    }

    public String getS_ent_type() {
        return s_ent_type;
    }

    public void setS_ent_type(String s_ent_type) {
        this.s_ent_type = s_ent_type;
    }

    public String getS_ent_address() {
        return s_ent_address;
    }

    public void setS_ent_address(String s_ent_address) {
        this.s_ent_address = s_ent_address;
    }

    public String getMdm_busno() {
        return mdm_busno;
    }

    public void setMdm_busno(String mdm_busno) {
        this.mdm_busno = mdm_busno;
    }
}
