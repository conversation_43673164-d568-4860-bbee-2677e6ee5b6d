package com.cowell.bam.entity;

import java.io.Serializable;

/**
 * sys_action
 * <AUTHOR>
public class SysAction implements Serializable {
    /**
     * 编号
     */
    private String zstepId;

    /**
     * 系统动作
     */
    private String zstepAct;

    private static final long serialVersionUID = 1L;

    public String getZstepId() {
        return zstepId;
    }

    public void setZstepId(String zstepId) {
        this.zstepId = zstepId;
    }

    public String getZstepAct() {
        return zstepAct;
    }

    public void setZstepAct(String zstepAct) {
        this.zstepAct = zstepAct;
    }

    @Override
    public boolean equals(Object that) {
        if (this == that) {
            return true;
        }
        if (that == null) {
            return false;
        }
        if (getClass() != that.getClass()) {
            return false;
        }
        SysAction other = (SysAction) that;
        return (this.getZstepId() == null ? other.getZstepId() == null : this.getZstepId().equals(other.getZstepId()))
            && (this.getZstepAct() == null ? other.getZstepAct() == null : this.getZstepAct().equals(other.getZstepAct()));
    }

    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((getZstepId() == null) ? 0 : getZstepId().hashCode());
        result = prime * result + ((getZstepAct() == null) ? 0 : getZstepAct().hashCode());
        return result;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", zstepId=").append(zstepId);
        sb.append(", zstepAct=").append(zstepAct);
        sb.append(", serialVersionUID=").append(serialVersionUID);
        sb.append("]");
        return sb.toString();
    }
}