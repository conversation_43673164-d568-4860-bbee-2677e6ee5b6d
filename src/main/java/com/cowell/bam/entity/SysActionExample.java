package com.cowell.bam.entity;

import java.util.ArrayList;
import java.util.List;

public class SysActionExample {
    protected String orderByClause;

    protected boolean distinct;

    protected List<Criteria> oredCriteria;

    private Integer limit;

    private Integer offset;

    public SysActionExample() {
        oredCriteria = new ArrayList<Criteria>();
    }

    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    public String getOrderByClause() {
        return orderByClause;
    }

    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    public boolean isDistinct() {
        return distinct;
    }

    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    public void setLimit(Integer limit) {
        this.limit = limit;
    }

    public Integer getLimit() {
        return limit;
    }

    public void setOffset(Integer offset) {
        this.offset = offset;
    }

    public Integer getOffset() {
        return offset;
    }

    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andZstepIdIsNull() {
            addCriterion("zstep_id is null");
            return (Criteria) this;
        }

        public Criteria andZstepIdIsNotNull() {
            addCriterion("zstep_id is not null");
            return (Criteria) this;
        }

        public Criteria andZstepIdEqualTo(String value) {
            addCriterion("zstep_id =", value, "zstepId");
            return (Criteria) this;
        }

        public Criteria andZstepIdNotEqualTo(String value) {
            addCriterion("zstep_id <>", value, "zstepId");
            return (Criteria) this;
        }

        public Criteria andZstepIdGreaterThan(String value) {
            addCriterion("zstep_id >", value, "zstepId");
            return (Criteria) this;
        }

        public Criteria andZstepIdGreaterThanOrEqualTo(String value) {
            addCriterion("zstep_id >=", value, "zstepId");
            return (Criteria) this;
        }

        public Criteria andZstepIdLessThan(String value) {
            addCriterion("zstep_id <", value, "zstepId");
            return (Criteria) this;
        }

        public Criteria andZstepIdLessThanOrEqualTo(String value) {
            addCriterion("zstep_id <=", value, "zstepId");
            return (Criteria) this;
        }

        public Criteria andZstepIdLike(String value) {
            addCriterion("zstep_id like", value, "zstepId");
            return (Criteria) this;
        }

        public Criteria andZstepIdNotLike(String value) {
            addCriterion("zstep_id not like", value, "zstepId");
            return (Criteria) this;
        }

        public Criteria andZstepIdIn(List<String> values) {
            addCriterion("zstep_id in", values, "zstepId");
            return (Criteria) this;
        }

        public Criteria andZstepIdNotIn(List<String> values) {
            addCriterion("zstep_id not in", values, "zstepId");
            return (Criteria) this;
        }

        public Criteria andZstepIdBetween(String value1, String value2) {
            addCriterion("zstep_id between", value1, value2, "zstepId");
            return (Criteria) this;
        }

        public Criteria andZstepIdNotBetween(String value1, String value2) {
            addCriterion("zstep_id not between", value1, value2, "zstepId");
            return (Criteria) this;
        }

        public Criteria andZstepActIsNull() {
            addCriterion("zstep_act is null");
            return (Criteria) this;
        }

        public Criteria andZstepActIsNotNull() {
            addCriterion("zstep_act is not null");
            return (Criteria) this;
        }

        public Criteria andZstepActEqualTo(String value) {
            addCriterion("zstep_act =", value, "zstepAct");
            return (Criteria) this;
        }

        public Criteria andZstepActNotEqualTo(String value) {
            addCriterion("zstep_act <>", value, "zstepAct");
            return (Criteria) this;
        }

        public Criteria andZstepActGreaterThan(String value) {
            addCriterion("zstep_act >", value, "zstepAct");
            return (Criteria) this;
        }

        public Criteria andZstepActGreaterThanOrEqualTo(String value) {
            addCriterion("zstep_act >=", value, "zstepAct");
            return (Criteria) this;
        }

        public Criteria andZstepActLessThan(String value) {
            addCriterion("zstep_act <", value, "zstepAct");
            return (Criteria) this;
        }

        public Criteria andZstepActLessThanOrEqualTo(String value) {
            addCriterion("zstep_act <=", value, "zstepAct");
            return (Criteria) this;
        }

        public Criteria andZstepActLike(String value) {
            addCriterion("zstep_act like", value, "zstepAct");
            return (Criteria) this;
        }

        public Criteria andZstepActNotLike(String value) {
            addCriterion("zstep_act not like", value, "zstepAct");
            return (Criteria) this;
        }

        public Criteria andZstepActIn(List<String> values) {
            addCriterion("zstep_act in", values, "zstepAct");
            return (Criteria) this;
        }

        public Criteria andZstepActNotIn(List<String> values) {
            addCriterion("zstep_act not in", values, "zstepAct");
            return (Criteria) this;
        }

        public Criteria andZstepActBetween(String value1, String value2) {
            addCriterion("zstep_act between", value1, value2, "zstepAct");
            return (Criteria) this;
        }

        public Criteria andZstepActNotBetween(String value1, String value2) {
            addCriterion("zstep_act not between", value1, value2, "zstepAct");
            return (Criteria) this;
        }
    }

    /**
     */
    public static class Criteria extends GeneratedCriteria {

        protected Criteria() {
            super();
        }
    }

    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}