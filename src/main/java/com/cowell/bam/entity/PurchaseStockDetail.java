package com.cowell.bam.entity;

public class PurchaseStockDetail {
    private String trans_type;//业务交易类型	购进上报填写为 1
    private String s_ent_name;//供应商
    private String pass_num;//批准文号
    private String goods_name;//商品名1
    private String drug_name;//药品名
    private String essential_drug;//药物类型（新增字段）	（0 = 非基本药物，1 = 国家基本药物，2 = 上海基本药物）
    private String goods_manu;//生产厂家
//    private String drug_ename;//药品英文名（可省）
    private String standard;//规格
    private String form;//剂型
    private String lot_num;//批号
    private String uom;//单位
    private String trans_amount;//数量
//    private String report_amount;//剩余库存量(可省)
    private String mdm_busno;
//    private String trans_date;//购进日期

    public PurchaseStockDetail() {
    }

    public String getTrans_type() {
        return trans_type;
    }

    public void setTrans_type(String trans_type) {
        this.trans_type = trans_type;
    }

    public String getS_ent_name() {
        return s_ent_name;
    }

    public void setS_ent_name(String s_ent_name) {
        this.s_ent_name = s_ent_name;
    }

    public String getPass_num() {
        return pass_num;
    }

    public void setPass_num(String pass_num) {
        this.pass_num = pass_num;
    }

    public String getGoods_name() {
        return goods_name;
    }

    public void setGoods_name(String goods_name) {
        this.goods_name = goods_name;
    }

    public String getDrug_name() {
        return drug_name;
    }

    public void setDrug_name(String drug_name) {
        this.drug_name = drug_name;
    }

    public String getEssential_drug() {
        return essential_drug;
    }

    public void setEssential_drug(String essential_drug) {
        this.essential_drug = essential_drug;
    }

    public String getGoods_manu() {
        return goods_manu;
    }

    public void setGoods_manu(String goods_manu) {
        this.goods_manu = goods_manu;
    }

    public String getStandard() {
        return standard;
    }

    public void setStandard(String standard) {
        this.standard = standard;
    }

    public String getForm() {
        return form;
    }

    public void setForm(String form) {
        this.form = form;
    }

    public String getUom() {
        return uom;
    }

    public void setUom(String uom) {
        this.uom = uom;
    }

    public String getTrans_amount() {
        return trans_amount;
    }

    public void setTrans_amount(String trans_amount) {
        this.trans_amount = trans_amount;
    }

    public String getMdm_busno() {
        return mdm_busno;
    }

    public void setMdm_busno(String mdm_busno) {
        this.mdm_busno = mdm_busno;
    }

    public String getLot_num() {
        return lot_num;
    }

    public void setLot_num(String lot_num) {
        this.lot_num = lot_num;
    }
}
