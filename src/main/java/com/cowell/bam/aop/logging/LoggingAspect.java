package com.cowell.bam.aop.logging;
import com.cowell.bam.web.rest.errors.BusinessErrorException;
import com.cowell.bam.web.rest.errors.ErrorCodeEnum;
import com.cowell.bam.web.rest.util.HeaderUtil;
import io.github.jhipster.config.JHipsterConstants;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.Signature;
import org.aspectj.lang.annotation.AfterThrowing;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.aspectj.lang.reflect.MethodSignature;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.core.env.Environment;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Component;

import java.lang.reflect.Method;

/**
 * Aspect for logging execution of service and repository Spring components.
 *
 * By default, it only runs with the "dev" profile.
 */
@Aspect
@Component
public class LoggingAspect {

    private final Logger log = LoggerFactory.getLogger(this.getClass());

    private final Environment env;

    public LoggingAspect(Environment env) {
        this.env = env;
    }

    /**
     * Pointcut that matches all repositories, services and Web REST endpoints.
     */
    @Pointcut("within(@org.springframework.stereotype.Repository *)" +
        " || within(@org.springframework.web.bind.annotation.RestController *)")
    public void springBeanPointcut() {
        // Method is empty as this is just a Pointcut, the implementations are in the advices.
    }

    @Pointcut("within(@org.springframework.stereotype.Service *)")
    public void springBeanPointcutService() {
        // Method is empty as this is just a Pointcut, the implementations are in the advices.
    }

    @Pointcut("within(com.cowell.bam.repository..*)"+
        " || within(com.cowell.bam.web.rest..*)")
    public void applicationPackagePointcut() {
        // Method is empty as this is just a Pointcut, the implementations are in the advices.
    }

    @Pointcut("within(com.cowell.bam.service..*)")
    public void applicationPackagePointcutService() {
        // Method is empty as this is just a Pointcut, the implementations are in the advices.
    }

    /**
     * Advice that logs methods throwing exceptions.
     *
     * @param joinPoint join point for advice
     * @param e exception
     */
    @AfterThrowing(pointcut = "applicationPackagePointcut() && springBeanPointcut()", throwing = "e")
    public void logAfterThrowing(JoinPoint joinPoint, Throwable e) {

        if(e instanceof BusinessErrorException){
            //正常的业务异常直接打warn级别的日志
//            log.warn("执行{}#{}方法出错，入参为:{}，ErrorCode={}，ErrorMessage={}", joinPoint.getTarget().getClass().getSimpleName(),joinPoint.getSignature().getName(),joinPoint.getArgs(),((BusinessErrorException) e).getErrorCode(),((BusinessErrorException) e).getErrorMessage());
            return ;
        }
        if (env.acceptsProfiles(JHipsterConstants.SPRING_PROFILE_DEVELOPMENT)) {
            log.error("执行{}#{}方法出错，入参为:{}，message={}", joinPoint.getTarget().getClass().getSimpleName(),joinPoint.getSignature().getName(),joinPoint.getArgs(), e.getMessage(),e);

        } else {
            log.error("执行{}#{}方法出错，入参为:{}，message={}", joinPoint.getTarget().getClass().getSimpleName(),joinPoint.getSignature().getName(),joinPoint.getArgs(), e.getMessage(),e);
        }
    }

    /**
     * Advice that logs when a method is entered and exited.
     *
     * @param joinPoint join point for advice
     * @return result
     * @throws Throwable throws IllegalArgumentException
     */
    @Around("applicationPackagePointcut() && springBeanPointcut()")
    public Object logAround(ProceedingJoinPoint joinPoint) throws Throwable {
        try {
            log.info("开始执行{}#{}方法，入参为{}", joinPoint.getTarget().getClass().getSimpleName(), joinPoint.getSignature().getName(), joinPoint.getArgs());
            Object result = joinPoint.proceed();
            log.info("执行{}#{}方法结束，入参为{},结果为{}", joinPoint.getTarget().getClass().getSimpleName(), joinPoint.getSignature().getName(), joinPoint.getArgs(), result);
            return result;
        } catch (BusinessErrorException e) {
            log.warn("执行{}#{}方法出错，入参为:{}，ErrorCode={}，ErrorMessage={}", joinPoint.getTarget().getClass().getSimpleName(), joinPoint.getSignature().getName(), joinPoint.getArgs(), ((BusinessErrorException) e).getErrorCode(), ((BusinessErrorException) e).getErrorMessage());
            throw e;
        } catch (Exception e) {
            log.error("执行{}#{}方法出错，入参为:{}，message={}", joinPoint.getTarget().getClass().getSimpleName(), joinPoint.getSignature().getName(), joinPoint.getArgs(), e.getMessage(), e);
            Signature sig = joinPoint.getSignature();
            MethodSignature msig = (MethodSignature) sig;
            Method currentMethod = joinPoint.getTarget().getClass().getMethod(msig.getName(), msig.getParameterTypes());
            if (currentMethod.getReturnType().getName().equals("ResponseEntity")) {
                return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).headers(HeaderUtil.createErrorHeaders(ErrorCodeEnum.SYSTEM_ERROR)).build();
            }else{
                throw e;
            }

        }

    }



    @Around("applicationPackagePointcutService() && springBeanPointcutService()")
    public Object logAroundService(ProceedingJoinPoint joinPoint) throws Throwable {
        log.info("开始执行{}#{}方法，入参为{}", joinPoint.getTarget().getClass().getSimpleName(),joinPoint.getSignature().getName(),joinPoint.getArgs());
        Object result = joinPoint.proceed();
        log.info("执行{}#{}方法结束，入参为{},结果为{}", joinPoint.getTarget().getClass().getSimpleName(),joinPoint.getSignature().getName(),joinPoint.getArgs(),result);
        return result;
    }
}
