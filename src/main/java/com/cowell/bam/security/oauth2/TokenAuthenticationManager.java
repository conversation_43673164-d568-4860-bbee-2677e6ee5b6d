package com.cowell.bam.security.oauth2;

import com.cowell.bam.config.oauth2.OAuth2JwtAccessTokenConverter;
import com.cowell.bam.service.dto.TokenUserDTO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.core.Authentication;
import org.springframework.security.oauth2.common.exceptions.InvalidTokenException;
import org.springframework.security.oauth2.provider.authentication.BearerTokenExtractor;
import org.springframework.security.oauth2.provider.authentication.TokenExtractor;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletRequest;
import java.util.Map;


@Service
public class TokenAuthenticationManager {

    @Autowired
    public OAuth2JwtAccessTokenConverter oAuth2JwtAccessTokenConverter;

    private TokenExtractor tokenExtractor = new BearerTokenExtractor();

    public String getToken(HttpServletRequest request) {
        Authentication authentication=tokenExtractor.extract(request);
        if (authentication == null) {
            throw new InvalidTokenException("Invalid token (token not found)");
        }
        String token = (String) authentication.getPrincipal();
        return token;
    }

    public TokenUserDTO getUserInfobyToken(String token){
        Map<String, Object> map=oAuth2JwtAccessTokenConverter.decode(token);
        if (map == null) {
            throw new InvalidTokenException("Invalid token (token not found)");
        }
        TokenUserDTO tokenUserDTO=TokenUserDTO.toDTO(map);
        return tokenUserDTO;
    }

    public TokenUserDTO getUserInfobyToken(HttpServletRequest request){
        String token= getToken(request);
        return getUserInfobyToken(token);
    }

}

