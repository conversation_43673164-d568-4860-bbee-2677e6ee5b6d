package com.cowell.bam.query;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * bam
 * 2020/9/8 10:13
 * 对比数据查询
 *
 * <AUTHOR>
 * @since
 **/
@Data
public class CompareDataQuery implements Serializable {
    /**
     * 开始时间
     */
    private Date startDate;

    /**
     * 结束时间
     */
    private Date endDate;

    /**
     * 1：未确认，2：已确认
     */
    private Integer dataType;

    /**
     * 每次查询数量
     */
    private Integer pageSize;

    /**
     * 任务UUID
     */
    private String uuid;
}
