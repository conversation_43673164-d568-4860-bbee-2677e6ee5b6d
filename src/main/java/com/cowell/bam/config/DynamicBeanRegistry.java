package com.cowell.bam.config;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.druid.pool.DruidDataSource;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeansException;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.support.BeanDefinitionBuilder;
import org.springframework.beans.factory.support.DefaultListableBeanFactory;
import org.springframework.context.ApplicationContext;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
public class DynamicBeanRegistry implements InitializingBean {

    private final Logger log = LoggerFactory.getLogger(DynamicBeanRegistry.class);

    private final DefaultListableBeanFactory beanFactory;

    @Autowired
    private ApplicationContext applicationContext;

    private final static String DATA_SOURCE_BEAN_NAME = "DynamicDataSource";

    private final static String TEMPLATE_BEAN_NAME = "DynamicJdbcTemplate";

    @Autowired
    private ApolloConfig apolloConfig;

    public DynamicBeanRegistry(ApplicationContext applicationContext) {
        this.beanFactory = (DefaultListableBeanFactory) applicationContext.getAutowireCapableBeanFactory();
    }

    public void registerDataSourceBean(DatasourceConfigDTO dataSourceConfig) {
        String prefix = dataSourceConfig.getPrefix();
        String url = dataSourceConfig.getUrl();
        String username = dataSourceConfig.getUsername();
        String password = dataSourceConfig.getPassword();
        String dataSourceBeanName = prefix + DATA_SOURCE_BEAN_NAME;
        if (beanFactory.containsBean(dataSourceBeanName)) {
            log.info("DynamicBeanRegistry|Bean with name '" + dataSourceBeanName + "' already exists!");
            return;
        }
        BeanDefinitionBuilder datasourceBeanDefinitionBuilder = BeanDefinitionBuilder
                .genericBeanDefinition(DruidDataSource.class);
        datasourceBeanDefinitionBuilder.addPropertyValue("url",url);
        datasourceBeanDefinitionBuilder.addPropertyValue("username",username);
        datasourceBeanDefinitionBuilder.addPropertyValue("password",password);
        datasourceBeanDefinitionBuilder.addPropertyValue("driverClassName","oracle.jdbc.OracleDriver");
        datasourceBeanDefinitionBuilder.addPropertyValue("maxActive",100);
        datasourceBeanDefinitionBuilder.addPropertyValue("minIdle",5);
        datasourceBeanDefinitionBuilder.addPropertyValue("maxWait",120000);
        datasourceBeanDefinitionBuilder.addPropertyValue("poolPreparedStatements",true);
        datasourceBeanDefinitionBuilder.addPropertyValue("testOnBorrow",false);
        datasourceBeanDefinitionBuilder.addPropertyValue("testOnReturn",false);
        datasourceBeanDefinitionBuilder.addPropertyValue("removeAbandoned",false);
        datasourceBeanDefinitionBuilder.addPropertyValue("maxPoolPreparedStatementPerConnectionSize",100);
        datasourceBeanDefinitionBuilder.addPropertyValue("keepAlive",true);
        datasourceBeanDefinitionBuilder.addPropertyValue("minEvictableIdleTimeMillis",300000);
        datasourceBeanDefinitionBuilder.addPropertyValue("validationQuery","SELECT 'x' FROM DUAL");
        datasourceBeanDefinitionBuilder.addPropertyValue("testWhileIdle",false);
        datasourceBeanDefinitionBuilder.addPropertyValue("filters","stat");
        beanFactory.registerBeanDefinition(dataSourceBeanName, datasourceBeanDefinitionBuilder.getBeanDefinition());

        BeanDefinitionBuilder jdbcTemplateBeanDefinitionBuilder = BeanDefinitionBuilder
            .genericBeanDefinition(JdbcTemplate.class);
        jdbcTemplateBeanDefinitionBuilder.addConstructorArgReference(dataSourceBeanName);
        beanFactory.registerBeanDefinition(prefix+TEMPLATE_BEAN_NAME, jdbcTemplateBeanDefinitionBuilder.getBeanDefinition());
        log.info("DynamicBeanRegistry|Dynamic Bean '" + dataSourceBeanName + "' registered successfully!");
    }

    public JdbcTemplate getJdbcTemplate(String prefix) {
        log.info("DynamicBeanRegistry|Getting JdbcTemplate for prefix: " + prefix);
        Object bean;
        try {
            bean = applicationContext.getBean(prefix + TEMPLATE_BEAN_NAME);
            if (bean == null) {
                log.info("DynamicBeanRegistry|动态数据源为空'" + prefix + TEMPLATE_BEAN_NAME + "' not found!");
                return null;
            }
        } catch (Exception e) {
            log.info("DynamicBeanRegistry|动态数据源为空'" + prefix + TEMPLATE_BEAN_NAME + "' not found!");
            return null;
        }
        log.info("DynamicBeanRegistry|动态数据源为 for prefix: " + prefix);
        return (JdbcTemplate) bean;
    }

    @Override
    public void afterPropertiesSet() throws Exception {
        log.info("DynamicBeanRegistry|Initializing Bean...");
        List<DatasourceConfigDTO> datasourceConfigList = apolloConfig.getDatasourceConfig();
        if (CollUtil.isEmpty(datasourceConfigList)) {
            log.info("DynamicBeanRegistry|No datasource config found in apollo!");
            return;
        }
        for (DatasourceConfigDTO dataSourceConfig : datasourceConfigList) {
            registerDataSourceBean(dataSourceConfig);
        }
    }
}
