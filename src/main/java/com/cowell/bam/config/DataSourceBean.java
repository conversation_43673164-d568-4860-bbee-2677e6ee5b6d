package com.cowell.bam.config;

import com.alibaba.druid.pool.DruidDataSource;

public class DataSourceBean extends DruidDataSource {

    public DataSourceBean(String url, String username, String password) throws Exception {
        DataSourceBean dataSource = new DataSourceBean(url, username, password);
        dataSource.setDriverClassName("com.alibaba.druid.pool.DruidDataSource");
        dataSource.setUrl(url);
        dataSource.setUsername(username);
        dataSource.setPassword(password);
        dataSource.setMaxActive(100);
        dataSource.setMinIdle(5);
        dataSource.setMaxWait(120000);
        dataSource.setPoolPreparedStatements(true);
        dataSource.setMaxPoolPreparedStatementPerConnectionSize(100);
        dataSource.setKeepAlive(true);
        dataSource.setMinEvictableIdleTimeMillis(300000);
        dataSource.setValidationQuery("SELECT 'x' FROM DUAL");
        dataSource.setTestWhileIdle(false);
        dataSource.setFilters("stat");
    }

}
