package com.cowell.bam.config;

import com.codahale.metrics.MetricRegistry;
import com.zipkin.druid.util.MyMetricRegistry;
import com.zipkinMysql.MySQLStatementInterceptorManagementBean;
import io.prometheus.client.CollectorRegistry;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.sleuth.Tracer;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * Zipkin
 *
 * <AUTHOR>
 * @date 2021/4/7
 */
@Configuration
public class ZipkinConfig {

    @Autowired
    private Tracer tracer;
    @Autowired
    private CollectorRegistry registry;
    @Autowired
    private MetricRegistry metricRegistry;

    @Bean
    public MySQLStatementInterceptorManagementBean mySQLStatementInterceptorManagementBean() {
        return new MySQLStatementInterceptorManagementBean(tracer, metricRegistry);
    }

    @Bean
    public MyMetricRegistry myMetricRegistry() {
        return new MyMetricRegistry(registry, metricRegistry);
    }
}
