package com.cowell.bam.config;

import com.alibaba.druid.spring.boot.autoconfigure.DruidDataSourceBuilder;
import org.mybatis.spring.SqlSessionFactoryBean;
import org.mybatis.spring.annotation.MapperScan;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import org.springframework.core.env.Environment;
import org.springframework.core.io.ClassPathResource;
import org.springframework.jdbc.datasource.DataSourceTransactionManager;
import org.springframework.transaction.PlatformTransactionManager;

import javax.sql.DataSource;

/**
 * Druid配置
 */
@Configuration
@MapperScan({"com.cowell.bam.repository.mybatis.dao","com.cowell.bam.service.mapper"})
public class DruidDataSourceConfig {
    private final Logger log = LoggerFactory.getLogger(DruidDataSourceConfig.class);

    @Autowired
    private Environment env;

    /**
     * 配置扫描mapper.xml的bean
     * @param applicationContext
     * @return
     * @throws Exception
     */
    @Bean(name = "sqlSessionFactory")
    public SqlSessionFactoryBean sqlSessionFactory(ApplicationContext applicationContext) throws Exception {
        SqlSessionFactoryBean sessionFactory = new SqlSessionFactoryBean();
        sessionFactory.setDataSource(applicationContext.getBean(DataSource.class));
        log.info("<================dataSource init SUCCESS");
        sessionFactory.setConfigLocation(new ClassPathResource("mybatis/mybatis-config.xml"));
        sessionFactory.setMapperLocations(applicationContext.getResources("classpath*:mybatis/mapper/*.xml"));
        sessionFactory.setTypeAliasesPackage("com.cowell.forest.po");
        log.info("<================SqlSessionFactoryBean init SUCCESS");
        return sessionFactory;
    }

    /**
     * 配置事务管理
     * @return
     */
    @Bean
    public PlatformTransactionManager transactionManager(ApplicationContext applicationContext) {
        log.info("init transactionManager");
        return new DataSourceTransactionManager(applicationContext.getBean(DataSource.class));
    }

    /**
     * 初始化dataSource
     * @return
     */
    @Primary
    @Bean("mysqlDatasource")
    public DataSource dataSource() {
        log.info("Configuring JDBC datasource from a cloud provider");
        try {
            return DruidDataSourceBuilder.create().build(env, "spring.datasource");
        } catch (Exception e) {
            log.error("Configuring JDBC datasource from a cloud provider exception",e);
        }
        return null;
    }

}
