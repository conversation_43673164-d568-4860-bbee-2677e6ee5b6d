package com.cowell.bam.config;

import org.apache.http.HttpHost;
import org.apache.http.auth.AuthScope;
import org.apache.http.auth.UsernamePasswordCredentials;
import org.apache.http.client.CredentialsProvider;
import org.apache.http.impl.client.BasicCredentialsProvider;
import org.elasticsearch.client.RestClient;
import org.elasticsearch.client.RestHighLevelClient;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;



/**
 * <AUTHOR>
 * @date: 2018/12/31 18:15
 * @description:
 */
@Configuration
public class EsConfig {

    @Value("${spring.data.elasticsearch.cluster-node1}")
    private String EsNode1;

    @Value("${spring.data.elasticsearch.cluster-node2}")
    private String EsNode2;

    @Value("${spring.data.elasticsearch.cluster-node3}")
    private String EsNode3;

    @Value("${spring.data.elasticsearch.port}")
    private String port;
    @Value("${spring.data.elasticsearch.cluster-name}")
    private String EsClusterName;

    @Value("${spring.data.elasticsearch.x-pack.username}")
    private String xpackUsername;

    @Value("${spring.data.elasticsearch.x-pack.password}")
    private String xpackPwd;

    @Bean(name = "restHighLevelClient")
    public RestHighLevelClient initRestHighLevelClient() throws Exception {
        final CredentialsProvider credentialsProvider = new BasicCredentialsProvider();
        credentialsProvider.setCredentials(AuthScope.ANY, new UsernamePasswordCredentials(xpackUsername, xpackPwd));
        return new RestHighLevelClient(
            RestClient.builder(
                new HttpHost(EsNode1, Integer.parseInt(port), "http"),
                new HttpHost(EsNode2, Integer.parseInt(port), "http"),
                new HttpHost(EsNode3, Integer.parseInt(port), "http")
            ).setHttpClientConfigCallback(builder -> builder.setDefaultCredentialsProvider(credentialsProvider))
        );
    }

}
