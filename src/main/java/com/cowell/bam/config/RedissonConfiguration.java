package com.cowell.bam.config;

import com.codahale.metrics.MetricRegistry;
import io.prometheus.client.CollectorRegistry;
import org.redisson.Redisson;
import org.redisson.api.RedissonClient;
import org.redisson.codec.JsonJacksonCodec;
import org.redisson.config.ClusterServersConfig;
import org.redisson.config.Config;
import org.redisson.config.ReadMode;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.cache.annotation.CachingConfigurerSupport;
import org.springframework.cloud.sleuth.Tracer;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.List;

/**
 * author:<EMAIL>
 * Date:2018/6/13
 */
@Configuration
@ConfigurationProperties(prefix = "spring.redis.cluster")
public class RedissonConfiguration extends CachingConfigurerSupport {

    @Value("${spring.redis.cluster.read-mode:MASTER_SLAVE}")
    private String readMode;

//    @Autowired
//    private Tracer tracer;
//    @Autowired
//    private CollectorRegistry registry;
//    @Autowired
//    private MetricRegistry metricRegistry;

    private final Logger log = LoggerFactory.getLogger(RedissonConfiguration.class);

    private List<String> nodes;

    @Bean
    public RedissonClient redissonClient() {
        Config config = new Config();
        ClusterServersConfig clusterConfig = config.useClusterServers()
            .setScanInterval(2000)
            .setKeepAlive(true)
            .setMasterConnectionPoolSize(20)
            .setMasterConnectionMinimumIdleSize(10)
            .setSlaveConnectionPoolSize(20)
            .setSlaveConnectionMinimumIdleSize(10)
            .setReadMode(ReadMode.valueOf(readMode));
        clusterConfig.setPingConnectionInterval(3000);
        for (String str : nodes) {
            clusterConfig.addNodeAddress("redis://" + str);
            log.info("cluster address========"+str);
        }
        log.info("cluster redisson readMode======="+readMode);
        config.setCodec(new JsonJacksonCodec());

        //        config.setMetricRegistry(registry, metricRegistry);
//        config.setTrace(tracer);
        return Redisson.create(config);
    }

    public List<String> getNodes() {
        return nodes;
    }

    public void setNodes(List<String> nodes) {
        this.nodes = nodes;
    }
}
