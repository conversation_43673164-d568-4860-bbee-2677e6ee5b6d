package com.cowell.bam.config;


import com.alibaba.druid.pool.DruidDataSource;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.bind.RelaxedPropertyResolver;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.env.Environment;
import org.springframework.jdbc.core.JdbcTemplate;

import javax.sql.DataSource;

@Configuration
public class OracleDataSourcesConfig {

    private final Logger log = LoggerFactory.getLogger(OracleDataSourcesConfig.class);



    @Autowired
    private Environment env;

    @Qualifier("hdJdbcTemplate")
    @Bean(name ="hdJdbcTemplate")
    public JdbcTemplate hdJdbcTemplate(){
        JdbcTemplate jdbcTemplate = new JdbcTemplate(hdDatasource());
        return jdbcTemplate;
    }

    @Qualifier("hbJdbcTemplate")
    @Bean(name ="hbJdbcTemplate")
    public JdbcTemplate hbJdbcTemplate(){
        return new JdbcTemplate(hbDatasource());
    }

    @Qualifier("hzJdbcTemplate")
    @Bean(name ="hzJdbcTemplate")
    public JdbcTemplate hzJdbcTemplate(){
        return new JdbcTemplate(hzDatasource());
    }

    @Qualifier("xbJdbcTemplate")
    @Bean(name ="xbJdbcTemplate")
    public JdbcTemplate xbJdbcTemplate(){
        return new JdbcTemplate(xbDatasource());
    }

    @Qualifier("bjJdbcTemplate")
    @Bean(name ="bjJdbcTemplate")
    public JdbcTemplate bjJdbcTemplate(){
        return new JdbcTemplate(bjDatasource());
    }

    @Qualifier("xszkJdbcTemplate")
    @Bean(name ="xszkJdbcTemplate")
    public JdbcTemplate xszkJdbcTemplate(){
        return new JdbcTemplate(xszkDatasource());
    }

    @Qualifier("hnJdbcTemplate")
    @Bean(name ="hnJdbcTemplate")
    public JdbcTemplate hnJdbcTemplate(){
        return new JdbcTemplate(hnDatasource());
    }

    /**
     * 湖北心连心
     * @return
     */
    @Qualifier("hubeiJdbcTemplate")
    @Bean(name ="hubeiJdbcTemplate")
    public JdbcTemplate hubJdbcTemplate(){
        return new JdbcTemplate(hubDatasource());
    }

    /**
     * 新疆专区
     * @return
     */
    @Qualifier("xjJdbcTemplate")
    @Bean(name ="xjJdbcTemplate")
    public JdbcTemplate xjJdbcTemplate(){
        return new JdbcTemplate(xjDatasource());
    }

    /**
     * 西南平台(新)
     * @return
     */
    @Qualifier("xnNewJdbcTemplate")
    @Bean(name ="xnNewJdbcTemplate")
    public JdbcTemplate xnNewJdbcTemplate(){
        return new JdbcTemplate(xnNewDatasource());
    }

    /**
     * 龙一
     * @return
     */
    @Qualifier("longyiNewJdbcTemplate")
    @Bean(name ="longyiNewJdbcTemplate")
    public JdbcTemplate longyiNewJdbcTemplate(){
        return new JdbcTemplate(longyiNewDatasource());
    }

    /**
     * 西南平台
     * @return
     */
    @Qualifier("xn1JdbcTemplate")
    @Bean(name ="xn1JdbcTemplate")
    public JdbcTemplate xn1JdbcTemplate(){
        return new JdbcTemplate(xn1Datasource());
    }

    //@Primary
    @Bean(name="hdDatasource")
    @Qualifier("hdDatasource")
    @ConfigurationProperties(prefix="oracle.druid.hd")
    public DataSource hdDatasource() {
        log.info("Configuring JDBC datasource from a cloud provider");
        try {
            DruidDataSource build = buildDruidDataSource("oracle.druid.hd.");
            return build;
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

    @Bean(name="hbDatasource")
    @Qualifier("hbDatasource")
    @ConfigurationProperties(prefix="oracle.druid.hb")
    public DataSource hbDatasource() {
        log.info("Configuring JDBC datasource from a cloud provider");
        try {
            return buildDruidDataSource("oracle.druid.hb.");
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }


    @Bean(name="hzDatasource")
    @Qualifier("hzDatasource")
    @ConfigurationProperties(prefix="oracle.druid.hz")
    public DataSource hzDatasource() {
        log.info("Configuring JDBC datasource from a cloud provider");
        try {
            return buildDruidDataSource("oracle.druid.hz.");
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }
    @Bean(name="xbDatasource")
    @Qualifier("xbDatasource")
    @ConfigurationProperties(prefix="oracle.druid.xb")
    public DataSource xbDatasource() {
        log.info("Configuring JDBC bj datasource from a cloud provider");
        try {
            return buildDruidDataSource("oracle.druid.xb.");
        } catch (Exception e) {
            log.info("Configuring|JDBC|bj错误",e);
        }
        return null;
    }

    @Bean(name="bjDatasource")
    @Qualifier("bjDatasource")
    @ConfigurationProperties(prefix="oracle.druid.bj")
    public DataSource bjDatasource() {
        log.info("Configuring JDBC bj datasource from a cloud provider");
        try {
            return buildDruidDataSource("oracle.druid.bj.");
        } catch (Exception e) {
            log.info("Configuring|JDBC|bj错误",e);
        }
        return null;
    }

    @Bean(name="xszkDatasource")
    @Qualifier("xszkDatasource")
    @ConfigurationProperties(prefix="oracle.druid.xszk")
    public DataSource xszkDatasource() {
        log.info("Configuring JDBC xszk datasource from a cloud provider");
        try {
            return buildDruidDataSource("oracle.druid.xszk.");
        } catch (Exception e) {
            log.info("Configuring|JDBC|xszk错误",e);
        }
        return null;
    }

    @Bean(name="hnDatasource")
    @Qualifier("hnDatasource")
    @ConfigurationProperties(prefix="oracle.druid.hn")
    public DataSource hnDatasource() {
        log.info("Configuring JDBC hn datasource from a cloud provider");
        try {
            return buildDruidDataSource("oracle.druid.hn.");
        } catch (Exception e) {
            log.info("Configuring|JDBC|hn错误",e);
        }
        return null;
    }

    /**
     * 湖北心连心数据源
     * @return DataSource
     */
    @Bean(name="hubDatasource")
    @Qualifier("hubDatasource")
    @ConfigurationProperties(prefix="oracle.druid.hub")
    public DataSource hubDatasource() {
        log.info("Configuring hubDatasource datasource");
        try {
            DruidDataSource build = buildDruidDataSource("oracle.druid.hub.");
            return build;
        } catch (Exception e) {
            log.info("Configuring|hubDatasource|JDBC|hn错误",e);
        }
        return null;
    }

    /**
     * 新疆专区数据源
     *
     * @return {@link DataSource }
     */
    @Bean(name="xjDatasource")
    @Qualifier("xjDatasource")
    @ConfigurationProperties(prefix="oracle.druid.xj")
    public DataSource xjDatasource() {
        log.info("Configuring xjDatasource datasource");
        try {
            DruidDataSource build = buildDruidDataSource("oracle.druid.xj.");
            return build;
        } catch (Exception e) {
            log.info("Configuring|xjDatasource|JDBC|hn错误",e);
        }
        return null;
    }

    /**
     * 西南平台(新)
     *
     * @return {@link DataSource }
     */
    @Bean(name="xnNewDatasource")
    @Qualifier("xnNewDatasource")
    @ConfigurationProperties(prefix="oracle.druid.xn_new")
    public DataSource xnNewDatasource() {
        log.info("Configuring xnNewDatasource datasource");
        try {
            DruidDataSource build = buildDruidDataSource("oracle.druid.xn_new.");
            return build;
        } catch (Exception e) {
            log.info("Configuring|xnNewDatasource|JDBC|xn_new错误",e);
        }
        return null;
    }

    /**
     * 龙一
     *
     * @return {@link DataSource }
     */
    @Bean(name="longyiNewDatasource")
    @Qualifier("longyiNewDatasource")
    @ConfigurationProperties(prefix="oracle.druid.longyi_new")
    public DataSource longyiNewDatasource() {
        log.info("Configuring longyiNewDatasource datasource");
        try {
            DruidDataSource build = buildDruidDataSource("oracle.druid.longyi_new.");
            return build;
        } catch (Exception e) {
            log.info("Configuring|longyiNewDatasource|JDBC|longyi_new错误",e);
        }
        return null;
    }

    /**
     * 西南数据源
     *
     * @return {@link DataSource }
     */
    @Bean(name="xn1Datasource")
    @Qualifier("xn1Datasource")
    @ConfigurationProperties(prefix="oracle.druid.xn_1")
    public DataSource xn1Datasource() {
        log.info("Configuring xn1Datasource datasource from a cloud provider");
        try {
            return buildDruidDataSource("oracle.druid.xn_1.");
        } catch (Exception e) {
            log.error("Configuring|JDBC|xn_1错误",e);
        }
        return null;
    }


    private DruidDataSource buildDruidDataSource(String envPath) throws Exception {
        RelaxedPropertyResolver propertyResolver = new RelaxedPropertyResolver(env, envPath);
        DruidDataSource dataSource = new DruidDataSource();
        dataSource.setDriverClassName(propertyResolver.getProperty("driverClassName"));
        dataSource.setUrl(propertyResolver.getProperty("url"));
        dataSource.setUsername(propertyResolver.getProperty("username"));
        dataSource.setPassword(propertyResolver.getProperty("password"));
        dataSource.setMaxActive(Integer.parseInt(propertyResolver.getProperty("maxActive")));
        dataSource.setMinIdle(Integer.parseInt(propertyResolver.getProperty("minIdle")));
        dataSource.setMaxWait(Long.parseLong(propertyResolver.getProperty("maxWait")));
        dataSource.setPoolPreparedStatements(Boolean.parseBoolean(propertyResolver.getProperty("poolPreparedStatements")));
        dataSource.setMaxPoolPreparedStatementPerConnectionSize(Integer.parseInt(propertyResolver.getProperty("maxPoolPreparedStatementPerConnectionSize")));
        dataSource.setKeepAlive(Boolean.parseBoolean(propertyResolver.getProperty("keepAlive")));
        dataSource.setMinEvictableIdleTimeMillis(Long.parseLong(propertyResolver.getProperty("minEvictableIdleTimeMillis")));
        dataSource.setValidationQuery(propertyResolver.getProperty("validationQuery"));
        dataSource.setTestWhileIdle(Boolean.parseBoolean(propertyResolver.getProperty("testWhileIdle")));
        dataSource.setFilters(propertyResolver.getProperty("filters"));
        return dataSource;
    }


}
