//package com.cowell.bam.config;
//
//import javax.sql.DataSource;
//
//import org.slf4j.Logger;
//import org.slf4j.LoggerFactory;
//import org.springframework.beans.factory.annotation.Qualifier;
//import org.springframework.boot.autoconfigure.liquibase.LiquibaseProperties;
//import org.springframework.context.annotation.Bean;
//import org.springframework.context.annotation.Configuration;
//import org.springframework.core.env.Environment;
//import org.springframework.core.task.TaskExecutor;
//
//import io.github.jhipster.config.JHipsterConstants;
//import io.github.jhipster.config.liquibase.AsyncSpringLiquibase;
//import liquibase.integration.spring.SpringLiquibase;
//
//@Configuration
//public class LiquibaseConfiguration {
//
//    private final Logger log = LoggerFactory.getLogger(LiquibaseConfiguration.class);
//
//    private final Environment env;
//
//
//    public LiquibaseConfiguration(Environment env) {
//        this.env = env;
//    }
//
//    @Bean
//    public SpringLiquibase liquibase(@Qualifier("taskExecutor") TaskExecutor taskExecutor,
//            DataSource dataSource, LiquibaseProperties liquibaseProperties) {
//
//        // Use liquibase.integration.spring.SpringLiquibase if you don't want Liquibase to start asynchronously
//        SpringLiquibase liquibase = new AsyncSpringLiquibase(taskExecutor, env);
//        liquibase.setDataSource(dataSource);
//        liquibase.setChangeLog("classpath:config/liquibase/master.xml");
//        liquibase.setContexts(liquibaseProperties.getContexts());
//        liquibase.setDefaultSchema(liquibaseProperties.getDefaultSchema());
//        liquibase.setDropFirst(liquibaseProperties.isDropFirst());
//        if (env.acceptsProfiles(JHipsterConstants.SPRING_PROFILE_NO_LIQUIBASE)) {
//            liquibase.setShouldRun(false);
//        } else {
//            liquibase.setShouldRun(liquibaseProperties.isEnabled());
//            log.debug("Configuring Liquibase");
//        }
//        return liquibase;
//    }
//}
