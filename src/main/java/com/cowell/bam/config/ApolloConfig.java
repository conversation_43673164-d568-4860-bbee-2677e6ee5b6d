package com.cowell.bam.config;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.cloud.service.relational.DataSourceConfig;
import org.springframework.stereotype.Component;
import org.w3c.dom.stylesheets.LinkStyle;

import java.util.List;
import java.util.Map;

/**
 * 阿波罗配置
 */
@Component
@ConfigurationProperties
@Data
public class ApolloConfig {
    /**
     * DC列表
     */
    @Value("${dc.config:}")
    private String dcConfigs;

    @Value("${datasource.config:{}}")
    private String datasourceConfig;

    /**
     * 所有门店库存比对
     */
    @Value("${compareStockAllStore:false}")
    private boolean compareStockAllStore;


    public boolean isDC(String busNo) {
        if (StringUtils.isBlank(dcConfigs) || StringUtils.isBlank(busNo)) {
            return false;
        }
        for (String DC : dcConfigs.split(";")) {
            if (busNo.equals(DC)) {
                return true;
            }
        }
        return false;
    }

    public List<DatasourceConfigDTO> getDatasourceConfig() {
        return JSON.parseArray(datasourceConfig, DatasourceConfigDTO.class);
    }

    public boolean isCompareStockAllStore() {
        return compareStockAllStore;
    }
}
