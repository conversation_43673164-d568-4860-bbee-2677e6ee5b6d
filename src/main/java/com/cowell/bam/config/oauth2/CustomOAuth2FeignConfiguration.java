package com.cowell.bam.config.oauth2;

import com.cowell.bam.client.UserFeignClientInterceptor;
import com.cowell.bam.client.UserFeignClientInterceptor;
import feign.RequestInterceptor;
import io.github.jhipster.security.uaa.LoadBalancedResourceDetails;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.security.oauth2.client.feign.OAuth2FeignRequestInterceptor;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContext;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.oauth2.client.DefaultOAuth2ClientContext;
import org.springframework.security.oauth2.common.OAuth2AccessToken;
import org.springframework.security.oauth2.provider.authentication.OAuth2AuthenticationDetails;
import org.springframework.security.oauth2.provider.token.TokenStore;

import java.io.IOException;

@Configuration
public class CustomOAuth2FeignConfiguration {

    private final Logger log = LoggerFactory.getLogger(CustomOAuth2FeignConfiguration.class);

    private final LoadBalancedResourceDetails loadBalancedResourceDetails;

    @Autowired
    private TokenStore tokenStore;

    public CustomOAuth2FeignConfiguration(LoadBalancedResourceDetails loadBalancedResourceDetails) {
        this.loadBalancedResourceDetails = loadBalancedResourceDetails;
    }

    @Bean(name = "customOAuth2FeignClientInterceptor")
    public RequestInterceptor getUserFeignClientInterceptor() throws IOException {
        SecurityContext securityContext = SecurityContextHolder.getContext();
        Authentication authentication = securityContext.getAuthentication();
        if (authentication != null && authentication.getDetails() instanceof OAuth2AuthenticationDetails) {
            String principal = ((OAuth2AuthenticationDetails)authentication.getDetails()).getTokenValue();
            log.debug("current principal {}",principal);
            OAuth2AccessToken token = tokenStore.readAccessToken(principal);
            //check if token is expired or about to expire
            if (token.isExpired()) {
                SecurityContextHolder.clearContext();
                return new OAuth2FeignRequestInterceptor(new DefaultOAuth2ClientContext(), loadBalancedResourceDetails);
            }
            return new UserFeignClientInterceptor();
        }
        return new OAuth2FeignRequestInterceptor(new DefaultOAuth2ClientContext(), loadBalancedResourceDetails);
    }
}
