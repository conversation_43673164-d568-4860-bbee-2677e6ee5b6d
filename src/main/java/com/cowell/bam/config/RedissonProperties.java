package com.cowell.bam.config;

import java.util.Arrays;

/**
 * author:<EMAIL>
 * Date:2018/6/13
 */
//@ConfigurationProperties(prefix = "redisson")
public class RedissonProperties {

    private int timeout = 3000;

    private String address;

    private String password;

    private int connectionPoolSize = 64;

    private int connectionMinimumIdleSize=10;

    private int slaveConnectionPoolSize = 250;

    private int masterConnectionPoolSize = 250;

    private String[] nodeAddresses;

//    private String masterAddress;
//
//    private String masterName;

    public int getTimeout() {
        return timeout;
    }

    public void setTimeout(int timeout) {
        this.timeout = timeout;
    }

    public String getAddress() {
        return address;
    }

    public void setAddress(String address) {
        this.address = address;
    }

    public String getPassword() {
        return password;
    }

    public void setPassword(String password) {
        this.password = password;
    }

    public int getConnectionPoolSize() {
        return connectionPoolSize;
    }

    public void setConnectionPoolSize(int connectionPoolSize) {
        this.connectionPoolSize = connectionPoolSize;
    }

    public int getConnectionMinimumIdleSize() {
        return connectionMinimumIdleSize;
    }

    public void setConnectionMinimumIdleSize(int connectionMinimumIdleSize) {
        this.connectionMinimumIdleSize = connectionMinimumIdleSize;
    }

    public int getSlaveConnectionPoolSize() {
        return slaveConnectionPoolSize;
    }

    public void setSlaveConnectionPoolSize(int slaveConnectionPoolSize) {
        this.slaveConnectionPoolSize = slaveConnectionPoolSize;
    }

    public int getMasterConnectionPoolSize() {
        return masterConnectionPoolSize;
    }

    public void setMasterConnectionPoolSize(int masterConnectionPoolSize) {
        this.masterConnectionPoolSize = masterConnectionPoolSize;
    }

    public String[] getNodeAddresses() {
        return nodeAddresses;
    }

    public void setNodeAddresses(String[] nodeAddresses) {
        this.nodeAddresses = nodeAddresses;
    }

    @Override
    public String toString() {
        return "RedissonProperties{" +
            "timeout=" + timeout +
            ", address='" + address + '\'' +
            ", password='" + password + '\'' +
            ", connectionPoolSize=" + connectionPoolSize +
            ", connectionMinimumIdleSize=" + connectionMinimumIdleSize +
            ", slaveConnectionPoolSize=" + slaveConnectionPoolSize +
            ", masterConnectionPoolSize=" + masterConnectionPoolSize +
            ", nodeAddresses=" + Arrays.toString(nodeAddresses) +
            '}';
    }
}
