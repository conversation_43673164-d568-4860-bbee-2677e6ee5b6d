//package com.cowell.bam.config;
//
//import com.cowell.bam.security.AuthoritiesConstants;
//import com.cowell.bam.config.oauth2.OAuth2JwtAccessTokenConverter;
//import com.cowell.bam.config.oauth2.OAuth2Properties;
//import com.cowell.bam.security.oauth2.OAuth2SignatureVerifierClient;
//import com.cowell.bam.security.AuthoritiesConstants;
//
//import org.springframework.beans.factory.annotation.Qualifier;
//import org.springframework.cloud.client.loadbalancer.RestTemplateCustomizer;
//import org.springframework.context.annotation.Bean;
//import org.springframework.context.annotation.Configuration;
//import org.springframework.security.config.annotation.method.configuration.EnableGlobalMethodSecurity;
//import org.springframework.security.config.annotation.web.builders.HttpSecurity;
//import org.springframework.security.config.http.SessionCreationPolicy;
//import org.springframework.security.oauth2.config.annotation.web.configuration.EnableResourceServer;
//import org.springframework.security.oauth2.config.annotation.web.configuration.ResourceServerConfigurerAdapter;
//import org.springframework.security.oauth2.provider.token.TokenStore;
//import org.springframework.security.oauth2.provider.token.store.JwtAccessTokenConverter;
//import org.springframework.security.oauth2.provider.token.store.JwtTokenStore;
//import org.springframework.security.web.csrf.CookieCsrfTokenRepository;
//import org.springframework.web.client.RestTemplate;
//
//@Configuration
//@EnableResourceServer
//@EnableGlobalMethodSecurity(prePostEnabled = true, securedEnabled = true)
//public class SecurityConfiguration extends ResourceServerConfigurerAdapter {
//    private final OAuth2Properties oAuth2Properties;
//
//    public SecurityConfiguration(OAuth2Properties oAuth2Properties) {
//        this.oAuth2Properties = oAuth2Properties;
//    }
//
//    @Override
//    public void configure(HttpSecurity http) throws Exception {
//        http
//            .csrf()
//            .disable()
//            .headers()
//            .frameOptions()
//            .disable()
//        .and()
//            .sessionManagement()
//            .sessionCreationPolicy(SessionCreationPolicy.STATELESS)
//        .and()
//            .authorizeRequests()
//            .antMatchers("/api/**").authenticated()
//            .antMatchers("/management/health").permitAll()
//            .antMatchers("/management/info").permitAll()
//            .antMatchers("/management/**").hasAuthority(AuthoritiesConstants.ADMIN)
//            .antMatchers("/swagger-resources/configuration/ui").permitAll();
//    }
//
//    @Bean
//    public TokenStore tokenStore(JwtAccessTokenConverter jwtAccessTokenConverter) {
//        return new JwtTokenStore(jwtAccessTokenConverter);
//    }
//
////    @Bean
////    public JwtAccessTokenConverter jwtAccessTokenConverter(OAuth2SignatureVerifierClient signatureVerifierClient) {
////        return new OAuth2JwtAccessTokenConverter(oAuth2Properties, signatureVerifierClient);
////    }
//
//    @Bean
//	@Qualifier("loadBalancedRestTemplate")
//    public RestTemplate loadBalancedRestTemplate(RestTemplateCustomizer customizer) {
//        RestTemplate restTemplate = new RestTemplate();
//        customizer.customize(restTemplate);
//        return restTemplate;
//    }
//
//    @Bean
//    @Qualifier("vanillaRestTemplate")
//    public RestTemplate vanillaRestTemplate() {
//        return new RestTemplate();
//    }
//}
