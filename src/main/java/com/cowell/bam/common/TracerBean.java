package com.cowell.bam.common;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.BeanFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.sleuth.Span;
import org.springframework.cloud.sleuth.SpanNamer;
import org.springframework.cloud.sleuth.TraceKeys;
import org.springframework.cloud.sleuth.Tracer;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;

@Component
public class TracerBean {
    private static Logger logger = LoggerFactory.getLogger(TracerBean.class);
    private Tracer tracer;
    private String spanName;
    private TraceKeys traceKeys;
    private SpanNamer spanNamer;
    @Autowired
    private BeanFactory beanFactory;

    @PostConstruct
    void init() {
        tracer = tracer();
        traceKeys = traceKeys();
    }

    Tracer tracer() {
        if (this.tracer == null && this.beanFactory != null) {
            this.tracer = this.beanFactory.getBean(Tracer.class);
        }
        if (tracer == null)
            logger.error("can't create Tracer as beanFactory != null is {}", beanFactory != null);
        return this.tracer;
    }

    TraceKeys traceKeys() {
        if (this.traceKeys == null && this.beanFactory != null) {
            this.traceKeys = this.beanFactory.getBean(TraceKeys.class);
        }
        return this.traceKeys;
    }

    public Span startSpan() {
        if (tracer == null) {
            return null;
        }
        Span span = getTracer().createSpan(getSpanName(), (Span) null);
        getTracer().addTag(Span.SPAN_LOCAL_COMPONENT_TAG_NAME, "MetaQReceiver");
        getTracer().addTag(this.traceKeys.getMessage().getPrefix() +
            this.traceKeys.getMessage().getPrefix(), Thread.currentThread().getName());
        return span;
    }

    protected String getSpanName() {
        return "MetaQReceiver";
    }

    public void close(Span span) {
        if (span == null) return;
        if (!this.tracer.isTracing()) {
            this.tracer.continueSpan(span);
        }
        this.tracer.close(span);
    }

    protected Span continueSpan(Span span) {
        return this.tracer.continueSpan(span);
    }

    protected Span detachSpan(Span span) {
        if (this.tracer.isTracing()) {
            return this.tracer.detach(span);
        }
        return span;
    }

    public Tracer getTracer() {
        return this.tracer;
    }
}
