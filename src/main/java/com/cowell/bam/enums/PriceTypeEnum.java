package com.cowell.bam.enums;

import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2022/9/6 16:02
 */
@Getter
public enum PriceTypeEnum {
    LSJ(1, "<PERSON>SJ","零售价"),
    <PERSON>Y<PERSON>(2, "<PERSON>Y<PERSON>", "会员价"),
    <PERSON><PERSON><PERSON>(3, "<PERSON><PERSON><PERSON>", "拆零价"),
    <PERSON><PERSON><PERSON>(4, "<PERSON>YJ", "会员拆零价"),
    ;
    int code;
    String priceTypeCode;
    String priceTypeName;

    PriceTypeEnum(int code, String priceTypeCode, String priceTypeName) {
        this.code = code;
        this.priceTypeCode = priceTypeCode;
        this.priceTypeName = priceTypeName;
    }

    /**
     * 根据code获取对应枚举
     */
    public static PriceTypeEnum getByCode(int code) {
        for (PriceTypeEnum commonEnum : values()) {
            if (commonEnum.getCode() == code) {
                return commonEnum;
            }
        }
        return null;
    }
}
