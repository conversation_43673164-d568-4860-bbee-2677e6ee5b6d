package com.cowell.bam.enums;

/**
 * 公共枚举类
 */
public enum SyncTypeEnum {
    STOCK(0,"库存比对"),
    PRICE(1,"价格比对"),
    ORDER(2,"订单比对"),
    JF(3,"积分"),
    STOCK_POS(4,"以线上库存为基础，比对线下库存"),
    STOCK_TRANSIT(5,"在途库存比较");
    private Integer code;//标识码
    private String message;//描述
    SyncTypeEnum(int code, String message) {
        this.code = code;
        this.message = message;
    }

    public Integer getCode() {
        return code;
    }


    public String getMessage() {
        return message;
    }


    /**
     * 根据code获取Enum
     *
     * @param code
     * @return
     */
    public static SyncTypeEnum getEnum(Integer code) {
        if (null == code) {
            return null;
        }
        for (SyncTypeEnum syncTypeEnum : SyncTypeEnum.values()) {
            if (syncTypeEnum.getCode().equals(code)) {
                return syncTypeEnum;
            }
        }
        return null;
    }

    /**
     * 根据code获取错误信息
     * @param index
     * @return
     */
    public static String getName(Integer index) {
        for (SyncTypeEnum syncTypeEnum : SyncTypeEnum.values()) {
            if (syncTypeEnum.getCode() == index) {
                return syncTypeEnum.getMessage();
            }
        }
        return null;
    }

}
