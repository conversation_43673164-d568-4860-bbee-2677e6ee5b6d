package com.cowell.bam.enums;

public enum SysType {
    HDSYS_SYNC(100, "海典库存同步"),
    YKSYS_SYNC(200, "英克库存同步"),
    SAPSYS_SYNC(300, "SAP库存同步");

    private Integer code;
    private String message;

    SysType(Integer code, String message) {
        this.code = code;
        this.message = message;
    }

    public Integer getCode() {
        return code;
    }


    public String getMessage() {
        return message;
    }


    /**
     * 根据code获取错误信息
     *
     * @param code
     * @return
     */
    public static String getMsg(int code) {
        for (SysType sysType : SysType.values()) {
            if (sysType.getCode() == code) {
                return sysType.getMessage();
            }
        }
        return null;
    }
}
