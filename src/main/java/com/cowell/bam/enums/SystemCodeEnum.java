package com.cowell.bam.enums;

public enum SystemCodeEnum {

    POS("603", "云pos系统"),
    HD_POS("602", "海典pos系统"),
    SCRM ("300", "scrm系统"),
    CMALL("200", "cmall系统");
    private final String value;

    private final String name;

    SystemCodeEnum(String value, String name) {
        this.value = value;
        this.name = name;
    }

    public String getValue() {
        return value;
    }

    public String getName() {
        return name;
    }

    public static SystemCodeEnum getName(String name) {
        for (SystemCodeEnum instance : values()) {
            if (instance.name.equals(name)) {
                return instance;
            }
        }
        throw new IllegalArgumentException("No matching constant for [" + name + "]");
    }
}
