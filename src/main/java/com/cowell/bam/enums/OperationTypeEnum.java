package com.cowell.bam.enums;

import org.apache.commons.lang3.StringUtils;

/**
 * 订单运营类型
 */
public enum OperationTypeEnum {

    B2C_WAREHOUSE_SEND_GOODS(0, "b2c大仓发货",14,"门店对应二进制数值位数"),
    B2C_STORE_SEND_GOODS(1,"b2c门店发货",15,"门店对应二进制数值位数"),
    O2O_STORE_SEND_GOODS(2, "o2o门店发货",16,"门店对应二进制数值位数"),
    WAREHOUSE(3, "大仓",13,"门店对应二进制数值位数"),

    ;
    private int code;
    private String name;
    private int bit;
    private String desc;

    OperationTypeEnum(int code, String name, int bit, String desc) {
        this.code = code;
        this.name = name;
        this.bit = bit;
        this.desc = desc;
    }

    public int getCode() {
        return code;
    }

    public void setCode(int code) {
        this.code = code;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public int getBit() {
        return bit;
    }

    public void setBit(int bit) {
        this.bit = bit;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }

    public static OperationTypeEnum getEnumByName(String name){
        if(StringUtils.isBlank(name)){
            return null;
        }
        for(OperationTypeEnum operationTypeEnum:values()){
            if(operationTypeEnum.getName().equals(name)){
                return operationTypeEnum;
            }
        }
        return null;
    }

}
