package com.cowell.bam.enums;

/**
 * 公共枚举类
 */
public enum ReturnCodeEnum {
    SUCCESS(0, "成功"),
    HTTP_STATUS_OK(200, "成功"),
    FAIL(201, "失败"),
    SYSTEM_ERROR(500, "系统异常"),
    TIME_OUT(501, "操作超时"),
    PARAM_ERROR(502, "参数错误"),
    UPDATE_ERROR(511, "更新失败"),
    DELETE_ERROR(512, "删除失败"),
    INSERT_ERROR(513, "新增失败"),
    CALL_PERMISSION_ERROR(514, "调权限系统异常"),
    CALL_SEARCH_ERROR(515, "调搜索异常"),



    ERROR1(1001,"价格类型"),
    PRICE_TYPE_ERROR_NAME_IS_EXIST(1002,"价格名称重复，请修改后保存"),
    PRICE_TYPE_ERROR_CODE_IS_EXIST(1003,"唯一标识已经存在"),
    PRICE_TYPE_ERROR_TYPE_IS_EXIST(1004,"最低/最高限价类型已经存在"),
    PRICE_TYPE_ERROR_CODE(1005,"唯一标识仅能输入字母加数字"),
    PRICE_TYPE_NOT_EXIST(1006,"价格类型不存在"),
    PRICE_FORMAT_ERROR(1007,"价格格式不正确"),

    PRICE_ERROR(2000,"价格组"),
    PRICE_GROUP_ERROR_NAME_IS_EXIST(2001,"价格组名称已经存在"),
    PRICE_GROUP_ERROR_ADD_ERROR(2002,"新增价格组失败"),
    PRICE_GROUP_ERROR_ADD_DETAIL_ERROR(2003,"新增价格组明细失败"),
    PRICE_GROUP_ERROR_QUERY_NO_DATA(2004,"无价格组记录"),
    PRICE_GROUP_ERROR_QUERY_DETAIL_NO_DATA(2005,"无价格组明细记录"),
    PRICE_GROUP_QUERY_PARAM_ERROR(2006,"价格组查询参数错误"),



    ERROR3(3001,"定价目录"),
    PRICE_ORG_GOODS_NOT_EXIST(3002, "定价目录商品不存在"),
    PRICE_ORG_GOODS_CHOOSE_TOO_MANY(3003, "每次最多挑选数量为一百"),
    AUTH_GOODS_NOT_EXIST(3004, "授权商品不存在"),

    ERROR4(4000,"调价单"),
    ADJUST_ORDER_PARAM_CHECK_ERROR(4001,"参数校验失败！"),
    ADJUST_ORDER_NOT_EXIST_ERROR(4002,"调价单不存在！"),
    ADJUST_ORDER_AUDIT_STATUS_ERROR(4003,"调价单状态非待审核状态！"),
    ADJUST_ORDER_SUBMIT_STATUS_ERROR(4003,"调价单状态非待提交状态！"),
    ADJUST_ORDER_SAVE_ERROR(4004,"保存调价单异常！"),
    GET_AUTH_ERROR(4005,"查询权限异常!"),
    ADJUST_ORDER_SUBMIT_PRICE_ORG_GOODS_NOT_EXIST(4006,"调价单中商品不在定价目录里！"),


    ERROR5(5001,"商品价格查询"),
    GET_ES_RESULT_ERROR(5002,"获取ES查询结果异常"),
    PRICE_DETAIL_NO_EXIST(5003,"获取价格详情为空"),
    GET_ES_STORE_ITEM_LIST_ERROR(5004,"获取ES门店售卖商品异常"),
    QUERY_PRICE_PARAM_NULL_ERROR(5005,"获取商品价格spuId和goodsNo至少有一个"),
    GET_PERMISSION_BY_ORG_ID_ERROR(5006,"未获取到机构ID对应的机构信息"),


    EXCEL_NO_DATE(5007,"excel没有解析出数据"),
    EXCEL_TOO_MANY_DATA(5007,"excel导入数据量过多"),
    EXCEL_DATE_ERROR1(5008,"excel中数据有误"),
    EXCEL_EXPORT_ERROR(5009,"excel导出异常"),
    EXCEL_ROW1_ERROR(5010,"excel中第一行价格类型位置有误"),

    PRICE_COPY(6000,"价格初始化"),
    STORE_NO_PRICE(6001,"参考的门店下没有价格记录"),
    STORE_IS_PRICE(6002,"新门店下已经存在价格记录"),

    ERROR_ADJUSTED(7001,"已经审核过了！"),
    ERROR_ADJUST_INSERT_PERMISSIONERROR(7002,"新增失败！权限系统异常"),
    ERROR_ADJUSTED_STATUS(7003,"审核状态有误！"),
    ;

    private Integer code;//标识码
    private String message;//描述
    ReturnCodeEnum(int code, String message) {
        this.code = code;
        this.message = message;
    }

    public Integer getCode() {
        return code;
    }

    public void setCode(int code) {
        this.code = code;
    }

    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }

    /**
     * 根据code获取错误信息
     * @param index
     * @return
     */
    public static String getName(Integer index) {
        for (ReturnCodeEnum commonEnum : ReturnCodeEnum.values()) {
            if (commonEnum.getCode().equals(index)) {
                return commonEnum.getMessage();
            }
        }
        return null;
    }

}
