package com.cowell.bam.enums;

import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2022/5/7 15:20
 */
@Getter
public enum PriceComparisonEnum {
    //0：表示进行价格对比 1：不对比价格直接推价格中台  2：中台价格比对POS价格
    EMPTY(-1, "EMPTY"),
    COMPARE_PRICE(0, "价格对比"),
    PUSH_PRICE_CENTER(1, "不对比价格直接推价格中台"),
    COMPARE_POS_PRICE(2, "中台价格比对POS价格"),
    COMPARE_AND_NOTIFY(3, "比对价格并通知海典下发差异数据"),
    COMPARE_PRICE_SIGN(4, "比对特价标签"),
    BAM_COMPARE_PRICE_SIGN(5, "比对特价标签"),
    BAM_COMPARE_STORE_PRICE(6, "比对门店价格"),
    ;
    private final Integer code;
    private final String msg;

    PriceComparisonEnum(Integer code, String msg) {
        this.code = code;
        this.msg = msg;
    }

    public static PriceComparisonEnum getByCode(int code) {
        for (PriceComparisonEnum priceComparisonEnum : values()) {
            if (priceComparisonEnum.code == code) {
                return priceComparisonEnum;
            }
        }
        return EMPTY;
    }
}
