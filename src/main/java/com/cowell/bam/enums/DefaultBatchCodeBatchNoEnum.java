package com.cowell.bam.enums;

import lombok.Getter;

/**
 * 默认批次批号
 * <AUTHOR>
 * @date 2022/7/12 14:41
 */
@Getter
public enum DefaultBatchCodeBatchNoEnum {

    /**
     * 在途库存默认批次批号
     */
    TRANSIT_STOCK("GaoJiTransitCode", "GaoJiTransitNo")
    ;

    /**
     * 批次号
     */
    private final String batchCode;

    /**
     * 生产批号
     */
    private final String batchNo;


    DefaultBatchCodeBatchNoEnum(String batchCode, String batchNo) {
        this.batchCode = batchCode;
        this.batchNo = batchNo;
    }
}
