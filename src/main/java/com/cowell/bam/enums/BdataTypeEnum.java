package com.cowell.bam.enums;

/**
 * <AUTHOR>
 * @Description: erpsaas接收请求业务方需要传输的
 * bdata字段类型
 * @date 2018-11-16 10:29
 */
public enum BdataTypeEnum {
    BATA_TYPE_STRING("string", "string类型转义之后的json字符串"),
    BDATA_TYPE_JSON("json", "json对象类型，没有经过转义的字符串"),
    BDATA_TYPE_JSONARRAY("jsonArray", "jsonArray类型的数据对象，没有经过转义的!")
    ;

    private String type;
    private String msg;

    BdataTypeEnum(String type, String msg) {
        this.type = type;
        this.msg = msg;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getMsg() {
        return msg;
    }

    public void setMsg(String msg) {
        this.msg = msg;
    }

    public static BdataTypeEnum getByType(String type) {
        for (BdataTypeEnum temp : values()) {
            if (temp.type.equals(type)) {
                return temp;
            }
        }
        return null;
    }
}
