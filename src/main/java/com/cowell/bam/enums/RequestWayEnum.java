package com.cowell.bam.enums;

/**
 * <AUTHOR>
 * @Description: 请求方式，POST,GET,MQ
 * @date 2018-10-23 20:51
 */
public enum RequestWayEnum {

    REQUEST_MODE_POST("POST", "post请求"),
    REQUEST_MODE_MQ("MQ", "mq异步发送"),
    REQUEST_MODE_SYN("GET", "get请求");

    private String code;
    private String msg;

    RequestWayEnum(String code, String msg) {
        this.code = code;
        this.msg = msg;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getMsg() {
        return msg;
    }

    public void setMsg(String msg) {
        this.msg = msg;
    }
}
