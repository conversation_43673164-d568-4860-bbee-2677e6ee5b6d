package com.cowell.bam.enums;

import java.io.Serializable;

/**
 * bam
 * 2020/9/17 15:05
 * 通知同步库存类型
 *
 * <AUTHOR>
 * @since
 **/
public enum NotifySyncStockTypeEnum implements Serializable {
    STOCK_CHECK("6073", "库存核对"),
    STOCK_UPDATE("6074", "库存更新");

    private String notifyType;
    private String comment;

    public String getNotifyType() {
        return notifyType;
    }

    public void setNotifyType(String notifyType) {
        this.notifyType = notifyType;
    }

    NotifySyncStockTypeEnum(String notifyType, String comment) {
        this.notifyType = notifyType;
        this.comment = comment;
    }

}
