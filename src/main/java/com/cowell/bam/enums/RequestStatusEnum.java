package com.cowell.bam.enums;

/**
 * <AUTHOR>
 * @Description: 海典，英克，sap处理状态类型
 * @date 2018-09-11 17:22
 */
public enum RequestStatusEnum {

    SENT("1", "已发送"),
    RECIEVE("2", "已接受"),
    SENTFAIL("3", "发送失败"),
    PROCESSING("4", "处理中"),
    PROCESS_SUCCESS("5", "处理成功"),
    PROCESS_EXCEPTION("6", "处理异常");

    private String code;
    private String msg;

    RequestStatusEnum(String code, String msg) {
        this.code = code;
        this.msg = msg;
    }
    public String getCode() {
        return code;
    }

    public String getMsg() {
        return msg;
    }

    public void setMsg(String msg) {
        this.msg = msg;
    }

}
