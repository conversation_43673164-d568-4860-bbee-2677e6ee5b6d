package com.cowell.bam.mq.message;

import lombok.Data;

/**
 * <AUTHOR>
 * @date 2022/12/20 10:05
 */
@Data
public class ComparisonModelConfig {
    private Integer id;
    /**
     * 应用ID
     */
    private Integer app_id;
    /**
     * 扩展
     */
    private String extend;
    /**
     * 版本号
     */
    private Integer version;
    private String gmt_create;
    private String gmt_update;
    private String create_by;
    private String update_by;
    /**
     * 模型状态
     */
    private String model_status;
    private Integer status;

    /**
     * 定时任务编号
     */
    private String cron_job_no;
    /**
     * 定时任务ID
     */
    private String cron_job_id;
    /**
     * 比对结果表ID
     */
    private String comparison_result_id;
    /**
     * 比对结果表名
     */
    private String comparison_result_name;
    /**
     * 视图模型编号
     */
    private String view_model_id;
    /**
     * 视图模型名称
     */
    private String view_model_name;
    /**
     * 数据权限关联角色
     */
    private String data_permission_role;
    /**
     * 比对状态回显左表
     */
    private String status_update_left;
    /**
     * 比对状态回显右表
     */
    private String status_update_right;
    /**
     * 比对字段左表
     */
    private String comparison_field_left;
    /**
     * 比对字段右表
     */
    private String comparison_field_right;
    /**
     * 关联项
     */
    private String related_items;
    /**
     * 比对范围左表
     */
    private String scope_left;
    /**
     * 比对范围右表
     */
    private String scope_right;
    /**
     * 数据来源左表
     */
    private String source_left_table;
    /**
     * 数据来源右表
     */
    private String source_right_table;
    /**
     * 比对模式
     */
    private String comparison_mode;
    /**
     * 描述
     */
    private String model_describe;
    /**
     * 执行间隔时间
     */
    private Integer execute_interval;
    /**
     * 执行频率
     */
    private String execute_rate;
    /**
     * 生效开始时间
     */
    private String start_time;
    /**
     * 生效结束时间
     */
    private String end_time;
    /**
     * 公司字段
     */
    private String company_field;
    /**
     * 模型级别
     */
    private String model_level;
    /**
     * 模型名称
     */
    private String model_name;
    /**
     * 手工比对确认视图模型名称
     */
    private String confirm_view_model_name;

    /**
     * 当前通知的批次
     */
    private String batchNo;
}
