package com.cowell.bam.mq.message;

import com.cowell.bam.entity.ItemPrice;
import com.cowell.bam.service.dto.ItemPriceResponse;
import com.cowell.bam.service.dto.MdmStoreBaseDTO;
import com.cowell.bam.service.dto.PricePushDTO;
import lombok.Data;

import java.util.List;

/**
 * 类说明
 *
 * @Author: liw
 * @Date: 2020-05-24 14:12
 */
@Data
public class CompareOneselfMessage {

    private List<ItemPrice> itemPriceList;
    private List<ItemPriceResponse> itemPriceResponseList;
    private Long businessId;
    private Long storeId;

    private Integer type;
    private String comId;
    private String busNo;
    // 0：表示进行价格对比 1：不对比价格直接推价格中台  2：中台价格比对POS价格
    private Integer isPriceComparison = 0;

    private List<PricePushDTO> itemList;

    private MdmStoreBaseDTO mdmStoreBaseDTO;

    private boolean priceRepair;


    public CompareOneselfMessage() {
    }

    public CompareOneselfMessage(List<ItemPrice> itemPriceList, List<ItemPriceResponse> itemPriceResponseList, Long businessId, Long storeId) {
        this.itemPriceList = itemPriceList;
        this.itemPriceResponseList = itemPriceResponseList;
        this.businessId = businessId;
        this.storeId = storeId;
    }
}
