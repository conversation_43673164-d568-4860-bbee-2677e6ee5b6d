package com.cowell.bam.mq;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.cowell.bam.mq.message.CompareOneselfMessage;
import com.cowell.tools.utils.RocketMqHook;
import org.apache.rocketmq.client.exception.MQClientException;
import org.apache.rocketmq.client.producer.DefaultMQProducer;
import org.apache.rocketmq.client.producer.SendResult;
import org.apache.rocketmq.client.producer.SendStatus;
import org.apache.rocketmq.common.message.Message;
import org.apache.rocketmq.remoting.common.RemotingHelper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.annotation.PreDestroy;

/**
 * 价格中台对比价格 不一致 发送消息给 pricecenter 修改价格
 * <AUTHOR>
 */
@Component
public class PriceCenterComparePriceProducer {

    private static final Logger logger = LoggerFactory.getLogger(PriceCenterComparePriceProducer.class);

    @Value("${apache.rocketmq.namesrvAddr}")
    private String namesrvAddr;

    @Value("${apache.rocketmq.compare_price_self.topic}")
    private String topic;
    @Value("${apache.rocketmq.compare_price_self.group}")
    private String groupName;
    @Value("${apache.rocketmq.compare_price_self.tag}")
    private String tag;

    private static final String instanceName = "compare-price-self";

    private final DefaultMQProducer producer = new DefaultMQProducer();

    @Autowired
    private RocketMqHook rocketMqHook;

    /**
     * 初始化
     */
    @PostConstruct
    public void start() {
        try {
            logger.info("MQ：启动生产者[PriceCenterComparePriceProducer] namesrvAddr:"+namesrvAddr);
            producer.setInstanceName(instanceName);
            producer.setNamesrvAddr(namesrvAddr);
            producer.setProducerGroup(groupName);
            producer.start();
            rocketMqHook.producer(producer);
        } catch (MQClientException e) {
            logger.error("MQ：启动生产者失败[PriceCenterComparePriceProducer]：{}-{}", e.getResponseCode(), e.getErrorMessage());
            throw new RuntimeException(e.getMessage(), e);
        }
    }

    public void sendMessage(CompareOneselfMessage message) {

        try {
            String data = JSONObject.toJSONString(message);
            byte[] messageBody = data.getBytes(RemotingHelper.DEFAULT_CHARSET);
            logger.info("MQ: 生产者[PriceCenterComparePriceProducer]发送价格对比消息 {}", data);

            Message mqMsg = new Message(topic, tag,
                String.valueOf(System.currentTimeMillis()), messageBody);


            SendResult sendResult = producer.send(mqMsg);
            if (sendResult != null && !sendResult.getSendStatus().equals(SendStatus.SEND_OK)) {
                logger.error("MQ: 生产者[PriceCenterComparePriceProducer]发送价格对比消息-异常,Message:{},sendResult={}", mqMsg, sendResult);
            } else {
                logger.info("MQ: 生产者[PriceCenterComparePriceProducer]发送价格对比消息-成功,Message:{},sendResult={}", mqMsg, sendResult);
            }

        } catch (Exception e) {
            if(message != null){
                logger.error("生产者[PriceCenterComparePriceProducer]发送价格对比消息失败2 Message:{}", JSON.toJSON(message));
            }
            logger.error("MQ: 生产者[PriceCenterComparePriceProducer]发送消息异常", e);
        }

    }


    public void sendMessageToCompareTaskSplit(CompareOneselfMessage message) {

        try {
            String data = JSONObject.toJSONString(message);
            byte[] messageBody = data.getBytes(RemotingHelper.DEFAULT_CHARSET);
            logger.info("MQ: 生产者[PriceCenterComparePriceProducer]发送价格对比消息 {}", data);

            Message mqMsg = new Message(topic, tag+"_TASK_SPLIT",
                String.valueOf(System.currentTimeMillis()), messageBody);


            SendResult sendResult = producer.send(mqMsg);
            if (sendResult != null && !sendResult.getSendStatus().equals(SendStatus.SEND_OK)) {
                logger.error("MQ: 生产者[PriceCenterComparePriceProducer]发送价格对比消息-异常,Message:{},sendResult={}", mqMsg, sendResult);
            } else {
                logger.info("MQ: 生产者[PriceCenterComparePriceProducer]发送价格对比消息-成功,Message:{},sendResult={}", mqMsg, sendResult);
            }

        } catch (Exception e) {
            if(message != null){
                logger.error("生产者[PriceCenterComparePriceProducer]发送价格对比消息失败2 Message:{}", JSON.toJSON(message));
            }
            logger.error("MQ: 生产者[PriceCenterComparePriceProducer]发送消息异常", e);
        }

    }

    @PreDestroy
    public void stop() {
        if (producer != null) {
            producer.shutdown();
            logger.info("MQ：关闭生产者[PriceCenterComparePriceProducer]");
        }
    }


}
