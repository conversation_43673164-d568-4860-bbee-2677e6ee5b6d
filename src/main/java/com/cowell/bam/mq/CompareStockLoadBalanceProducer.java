package com.cowell.bam.mq;

import com.alibaba.fastjson.JSON;
import com.cowell.bam.mq.message.CompareStockDTO;
import com.cowell.tools.utils.RocketMqHook;
import org.apache.rocketmq.client.exception.MQClientException;
import org.apache.rocketmq.client.producer.DefaultMQProducer;
import org.apache.rocketmq.client.producer.SendCallback;
import org.apache.rocketmq.client.producer.SendResult;
import org.apache.rocketmq.common.message.Message;
import org.apache.rocketmq.remoting.common.RemotingHelper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.annotation.PreDestroy;

/**
 * 对比库存和以及有库存商品的价格异常 自产自销 mq
 * <AUTHOR>
 */
@Component
public class CompareStockLoadBalanceProducer {

    private static final Logger logger = LoggerFactory.getLogger(CompareStockLoadBalanceProducer.class);

    @Value("${apache.rocketmq.namesrvAddr}")
    private String namesrvAddr;

    @Value("${apache.rocketmq.compare_stock_balance.topic}")
    private String topic;
    @Value("${apache.rocketmq.compare_stock_balance.producer-group}")
    private String group;

    private static final String instanceName = "compare_stock_balance-producer";

    public static final String COM_ID_TAG = "comId";

    public static final String STORE_NO_TAG = "storeNo";

    private final DefaultMQProducer producer = new DefaultMQProducer();

    @Autowired
    private RocketMqHook rocketMqHook;

    /**
     * 初始化
     */
    @PostConstruct
    public void start() {
        try {
            logger.info("MQ：启动生产者[CompareStockLoadBalanceProducer] namesrvAddr:"+namesrvAddr);
            producer.setInstanceName(instanceName);
            producer.setNamesrvAddr(namesrvAddr);
            producer.setProducerGroup(group + topic);
            producer.start();
            rocketMqHook.producer(producer);
        } catch (MQClientException e) {
            logger.error("MQ：启动生产者失败[CompareStockLoadBalanceProducer]：{}-{}", e.getResponseCode(), e.getErrorMessage());
            throw new RuntimeException(e.getMessage(), e);
        }
    }

    public void sendMessage(String data) {

        try {
            byte[] messageBody = data.getBytes(RemotingHelper.DEFAULT_CHARSET);
            logger.info("CompareStockLoadBalanceProducer|sendMessage|发送库存事件消息:{}", data);

            /**
             * tag 订单类型+事件类型
             */
            Message mqMsg = new Message(topic, "*",
                String.valueOf(System.currentTimeMillis()), messageBody);
            producer.send(mqMsg, new SendCallback() {
                @Override
                public void onSuccess(SendResult sendResult) {
                    logger.info("CompareStockLoadBalanceProducer|sendMessage|发送库存结果 {}", sendResult);
                }

                @Override
                public void onException(Throwable throwable) {
                    logger.error("CompareStockLoadBalanceProducer|sendMessage|发送库存消息失败1 Message:{}", JSON.toJSON(mqMsg));
                    logger.error(throwable.getMessage(), throwable);
                }
            });

        } catch (Exception e) {
            logger.error("CompareStockLoadBalanceProducer|sendMessage|发送消息异常", e);
        }


    }

    public void sendMessage(CompareStockDTO message) {

        try {
            byte[] messageBody = JSON.toJSONString(message).getBytes(RemotingHelper.DEFAULT_CHARSET);
            logger.info("CompareStockLoadBalanceProducer|sendMessage|发送库存事件消息:{}", message);
            /**
             * tag 订单类型+事件类型
             */
            String tag = message.getTag();
            String key = message.getKey();
            Message mqMsg = new Message(topic, tag,
                key, messageBody);
            producer.send(mqMsg, new SendCallback() {
                @Override
                public void onSuccess(SendResult sendResult) {
                    logger.info("CompareStockLoadBalanceProducer|sendMessage|发送库存结果 {}", sendResult);
                }

                @Override
                public void onException(Throwable throwable) {
                    logger.error("CompareStockLoadBalanceProducer|sendMessage|发送库存消息失败1 Message:{}", JSON.toJSON(mqMsg));
                    logger.error(throwable.getMessage(), throwable);
                }
            });

        } catch (Exception e) {
            logger.error("CompareStockLoadBalanceProducer|sendMessage|发送消息异常", e);
        }

    }

    @PreDestroy
    public void stop() {
        if (producer != null) {
            producer.shutdown();
            logger.info("MQ：关闭生产者[CompareStockLoadBalanceProducer]");
        }
    }


}
