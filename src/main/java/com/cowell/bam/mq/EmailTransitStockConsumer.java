package com.cowell.bam.mq;

import com.alibaba.fastjson.JSONObject;
import com.cowell.bam.service.StockTransitCompareService;
import com.cowell.bam.service.dto.EmailTransitStockDTO;
import com.cowell.tools.utils.RocketMqHook;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.rocketmq.client.consumer.DefaultMQPushConsumer;
import org.apache.rocketmq.client.consumer.listener.ConsumeConcurrentlyContext;
import org.apache.rocketmq.client.consumer.listener.ConsumeConcurrentlyStatus;
import org.apache.rocketmq.client.consumer.listener.MessageListenerConcurrently;
import org.apache.rocketmq.client.exception.MQClientException;
import org.apache.rocketmq.common.consumer.ConsumeFromWhere;
import org.apache.rocketmq.common.message.MessageExt;
import org.apache.rocketmq.common.protocol.heartbeat.MessageModel;
import org.apache.rocketmq.remoting.common.RemotingHelper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.sleuth.annotation.NewSpan;
import org.springframework.stereotype.Component;

import javax.annotation.PreDestroy;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2022/11/2 14:05
 */
@Component
public class EmailTransitStockConsumer implements MessageListenerConcurrently, ConsumerAdapter {

    private static final Logger logger = LoggerFactory.getLogger(EmailTransitStockConsumer.class);

    @Value("${apache.rocketmq.namesrvAddr}")
    private String namesrvAddr;

    @Value("${apache.rocketmq.email_transit_stock.topic}")
    private String topic;
    @Value("${apache.rocketmq.email_transit_stock.producer-group}")
    private String group;

    private final DefaultMQPushConsumer consumer = new DefaultMQPushConsumer();

    @Autowired
    private EmailTransitStockConsumer emailTransitStockConsumer;
    @Autowired
    private StockTransitCompareService stockTransitCompareService;

    @Autowired
    private RocketMqHook rocketMqHook;

    /**
     * 初始化
     *
     * @throws MQClientException
     */
    @Override
    public void start() {
        try {
            logger.info("MQ：启动消费者[EmailTransitStockConsumer]namesrvAddr:"+ namesrvAddr);
            consumer.setNamesrvAddr(namesrvAddr);
            consumer.setConsumerGroup(group+topic);
            consumer.setInstanceName(topic);
            consumer.setConsumeFromWhere(ConsumeFromWhere.CONSUME_FROM_LAST_OFFSET);
            // 集群消费模式
            consumer.setMessageModel(MessageModel.CLUSTERING);
            // 订阅主题
            consumer.subscribe(topic, "*");
            // 注册消息监听器
            consumer.registerMessageListener(emailTransitStockConsumer);
            //单位分钟，默认15分钟发一版
            consumer.setConsumeTimeout(600);
            consumer.setPullThresholdForQueue(50);
            consumer.setPullThresholdForTopic(50);
            // 启动消费端
            consumer.start();
            rocketMqHook.consumer(consumer);
        } catch (MQClientException e) {
            logger.info("MQ：启动消费者[EmailTransitStockConsumer]失败！异常: {}-{}", e.getResponseCode(), e.getErrorMessage());
            throw new RuntimeException(e.getMessage(), e);
        }

    }

    @Override
    @NewSpan
    public ConsumeConcurrentlyStatus consumeMessage(List<MessageExt> msgs, ConsumeConcurrentlyContext context) {

        try {
            logger.info("EmailTransitStockConsumer|consumeMessage:{}.size:{}..", msgs, CollectionUtils.isNotEmpty(msgs)?msgs.size():0);
            for (int index = 0; index < msgs.size(); index++) {
                MessageExt msg = msgs.get(index);
                String messageBody = new String(msg.getBody(), RemotingHelper.DEFAULT_CHARSET);
                logger.info("EmailTransitStockConsumer|consumeMessage|keys:{}.", msg.getKeys());
                EmailTransitStockDTO messageDTO = JSONObject.parseObject(messageBody, EmailTransitStockDTO.class);
                if(Objects.isNull(messageDTO)){
                    logger.warn("messageDTO为空");
                    continue;
                }
//                stockTransitCompareService.sendDifferEmmailByCom(messageDTO.getDiffDataRequestDTO(), messageDTO.getDate(), messageDTO.getToUsers());
                stockTransitCompareService.sendBatchDifferEmmailByCom(messageDTO);
                logger.info("EmailTransitStockConsumer|consumeMessage|发送邮件完成comId:{}",messageDTO.getBusinessIdList());
            }
        } catch (Exception e) {
            logger.warn("EmailTransitStockConsumer|consumeMessage|发送邮件失败", e);
            return ConsumeConcurrentlyStatus.RECONSUME_LATER;
        }
        return ConsumeConcurrentlyStatus.CONSUME_SUCCESS;
    }



    @PreDestroy
    public void stop() {
        consumer.shutdown();
        logger.warn("MQ：EmailTransitStockConsumer consumer!");
    }


}
