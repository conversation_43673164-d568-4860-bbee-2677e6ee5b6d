package com.cowell.bam.mq;

import com.alibaba.fastjson.JSONObject;
import com.cowell.bam.mq.message.CompareOneselfMessage;
import com.cowell.bam.service.PriceCompareService;
import com.cowell.tools.utils.RocketMqHook;
import org.apache.rocketmq.client.consumer.DefaultMQPushConsumer;
import org.apache.rocketmq.client.consumer.listener.ConsumeConcurrentlyContext;
import org.apache.rocketmq.client.consumer.listener.ConsumeConcurrentlyStatus;
import org.apache.rocketmq.client.consumer.listener.MessageListenerConcurrently;
import org.apache.rocketmq.client.exception.MQClientException;
import org.apache.rocketmq.common.consumer.ConsumeFromWhere;
import org.apache.rocketmq.common.message.MessageExt;
import org.apache.rocketmq.common.protocol.heartbeat.MessageModel;
import org.apache.rocketmq.remoting.common.RemotingHelper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.sleuth.annotation.NewSpan;
import org.springframework.stereotype.Component;

import javax.annotation.PreDestroy;
import java.util.List;

/**
 * 对比价格异常 自产自销 mq
 * <AUTHOR>
 */
@Component
public class CompareOneselfConsumer implements MessageListenerConcurrently, ConsumerAdapter {


    private static final Logger logger = LoggerFactory.getLogger(CompareOneselfConsumer.class);

    @Value("${apache.rocketmq.namesrvAddr}")
    private String namesrvAddr;

    @Value("${apache.rocketmq.compare_producer_oneself.topic}")
    private String topic;
    @Value("${apache.rocketmq.compare_producer_oneself.groupName}")
    private String groupName;

    private final DefaultMQPushConsumer consumer = new DefaultMQPushConsumer();
    private static final String INSTANCE_NAME = "compare-oneself-consumer-instance";

    @Autowired
    private CompareOneselfConsumer compareOneselfConsumer;
    @Autowired
    private PriceCompareService priceCompareService;

    @Autowired
    private RocketMqHook rocketMqHook;

    /**
     * 初始化
     *
     * @throws MQClientException
     */
    @Override
    public void start() {
        try {
            logger.info("MQ：启动消费者[OrderEventConsumer]namesrvAddr:"+ namesrvAddr);
            consumer.setNamesrvAddr(namesrvAddr);
            consumer.setConsumerGroup(groupName+topic);
            consumer.setInstanceName(INSTANCE_NAME);
            consumer.setConsumeFromWhere(ConsumeFromWhere.CONSUME_FROM_LAST_OFFSET);
            // 集群消费模式
            consumer.setMessageModel(MessageModel.CLUSTERING);
            // 订阅主题
            consumer.subscribe(topic, "*");
            // 注册消息监听器
            consumer.registerMessageListener(compareOneselfConsumer);
            //单位分钟，默认15分钟发一版
            consumer.setConsumeTimeout(5);
            consumer.setPullThresholdForQueue(50);
            consumer.setPullThresholdForTopic(50);
            // 启动消费端
            consumer.start();
            rocketMqHook.consumer(consumer);
        } catch (MQClientException e) {
            logger.info("MQ：启动消费者[OrderEventConsumer]失败！异常: {}-{}", e.getResponseCode(), e.getErrorMessage());
            throw new RuntimeException(e.getMessage(), e);
        }

    }

    @Override
    @NewSpan
    public ConsumeConcurrentlyStatus consumeMessage(List<MessageExt> msgs, ConsumeConcurrentlyContext context) {
        logger.info("MQ：消费者[OrderEventConsumer]请求参数 msgs :{} context:{}", msgs,context);
        int index = 0;
        try {
            for (; index < msgs.size(); index++) {
                MessageExt msg = msgs.get(index);
                String messageBody = new String(msg.getBody(), RemotingHelper.DEFAULT_CHARSET);
                logger.info("MQ：消费者[OrderEventConsumer]接受的参数 msg :{} ", messageBody);
                // 订单事件对象
                CompareOneselfMessage message = JSONObject.parseObject(messageBody,CompareOneselfMessage.class);

                priceCompareService.checkStoreItemPrice(message.getItemPriceList(),message.getItemPriceResponseList(),
                    message.getBusinessId(),message.getStoreId(),48);

                logger.info("对比事件消息消费成功 :{} :{}",message.getBusinessId(),message.getStoreId());

            }
        } catch (Exception e) {
            logger.warn("对比事件消息消费失败: ", e);
            return ConsumeConcurrentlyStatus.RECONSUME_LATER;
        }
        return ConsumeConcurrentlyStatus.CONSUME_SUCCESS;
    }



    @PreDestroy
    public void stop() {
        consumer.shutdown();
        logger.warn("MQ：OrderEventConsumer consumer!");
    }

}
