package com.cowell.bam.mq;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.cowell.bam.mq.message.CompareOneselfMessage;
import com.cowell.tools.utils.RocketMqHook;
import org.apache.rocketmq.client.exception.MQClientException;
import org.apache.rocketmq.client.producer.DefaultMQProducer;
import org.apache.rocketmq.client.producer.SendCallback;
import org.apache.rocketmq.client.producer.SendResult;
import org.apache.rocketmq.common.message.Message;
import org.apache.rocketmq.remoting.common.RemotingHelper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.annotation.PreDestroy;

/**
 * 对比价格异常 自产自销 mq
 * <AUTHOR>
 */
@Component
public class CompareOneselfProducer {

    private static final Logger logger = LoggerFactory.getLogger(CompareOneselfProducer.class);

    @Value("${apache.rocketmq.namesrvAddr}")
    private String namesrvAddr;

    @Value("${apache.rocketmq.compare_producer_oneself.topic}")
    private String topic;
    @Value("${apache.rocketmq.compare_producer_oneself.groupName}")
    private String groupName;

    private static final String instanceName = "compare-oneself-producer";

    private final DefaultMQProducer producer = new DefaultMQProducer();

    @Autowired
    private RocketMqHook rocketMqHook;

    /**
     * 初始化
     */
    @PostConstruct
    public void start() {
        try {
            logger.info("MQ：启动生产者[CompareOneselfProducer] namesrvAddr:"+namesrvAddr);
            producer.setInstanceName(instanceName);
            producer.setNamesrvAddr(namesrvAddr);
            producer.setProducerGroup(groupName + topic);
            producer.start();
            rocketMqHook.producer(producer);
        } catch (MQClientException e) {
            logger.error("MQ：启动生产者失败[CompareOneselfProducer]：{}-{}", e.getResponseCode(), e.getErrorMessage());
            throw new RuntimeException(e.getMessage(), e);
        }
    }

    public void sendMessage(CompareOneselfMessage message) {

        try {
            String data = JSONObject.toJSONString(message);
            byte[] messageBody = data.getBytes(RemotingHelper.DEFAULT_CHARSET);
            logger.info("MQ: 生产者[CompareOneselfProducer]发送订单事件消息 {}", data);

            /**
             * tag 订单类型+事件类型
             */
            Message mqMsg = new Message(topic, "*",
                String.valueOf(System.currentTimeMillis()), messageBody);
            producer.send(mqMsg, new SendCallback() {
                @Override
                public void onSuccess(SendResult sendResult) {
                    logger.info("MQ: 生产者[CompareOneselfProducer]发送结果 {}", sendResult);
                }

                @Override
                public void onException(Throwable throwable) {
                    logger.error("生产者[CompareOneselfProducer]发送订单事件消息失败1 Message:{}", JSON.toJSON(mqMsg));
                    logger.error(throwable.getMessage(), throwable);
                }
            });

        } catch (Exception e) {
            if(message != null){
                logger.error("生产者[CompareOneselfProducer]发送订单事件消息失败2 Message:{}", JSON.toJSON(message));
            }
            logger.error("MQ: 生产者[CompareOneselfProducer]发送消息异常", e);
        }

    }

    @PreDestroy
    public void stop() {
        if (producer != null) {
            producer.shutdown();
            logger.info("MQ：关闭生产者[CompareOneselfProducer]");
        }
    }


}
