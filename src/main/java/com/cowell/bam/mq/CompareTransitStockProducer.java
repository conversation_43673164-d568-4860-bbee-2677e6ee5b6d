package com.cowell.bam.mq;

import com.alibaba.fastjson.JSON;
import com.cowell.tools.utils.RocketMqHook;
import org.apache.rocketmq.client.exception.MQClientException;
import org.apache.rocketmq.client.producer.DefaultMQProducer;
import org.apache.rocketmq.client.producer.SendCallback;
import org.apache.rocketmq.client.producer.SendResult;
import org.apache.rocketmq.common.message.Message;
import org.apache.rocketmq.remoting.common.RemotingHelper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.annotation.PreDestroy;
import java.util.UUID;

/**
 * <AUTHOR>
 * @date 2022/10/30 22:17
 */
@Component
public class CompareTransitStockProducer {
    private static final Logger logger = LoggerFactory.getLogger(CompareTransitStockProducer.class);

    @Value("${apache.rocketmq.namesrvAddr}")
    private String namesrvAddr;

    @Value("${apache.rocketmq.compare_transit_stock.topic}")
    private String topic;
    @Value("${apache.rocketmq.compare_transit_stock.producer-group}")
    private String group;

    private static final String instanceName = "compare_transit_stock-producer";

    private final DefaultMQProducer producer = new DefaultMQProducer();

    @Autowired
    private RocketMqHook rocketMqHook;

    /**
     * 初始化
     */
    @PostConstruct
    public void start() {
        try {
            logger.info("MQ：启动生产者[CompareTransitStockProducer] namesrvAddr:"+namesrvAddr);
            producer.setInstanceName(instanceName);
            producer.setNamesrvAddr(namesrvAddr);
            producer.setProducerGroup(group + topic);
            producer.start();
            rocketMqHook.producer(producer);
        } catch (MQClientException e) {
            logger.error("MQ：启动生产者失败[CompareTransitStockProducer]：{}-{}", e.getResponseCode(), e.getErrorMessage());
            throw new RuntimeException(e.getMessage(), e);
        }
    }

    public void sendMessage(String data) {

        try {
            String keys = UUID.randomUUID().toString().replaceAll("-", "");
            byte[] messageBody = data.getBytes(RemotingHelper.DEFAULT_CHARSET);
            logger.info("CompareTransitStockProducer|sendMessage|发送库存事件消息|data:{}.|keys:{}", data, keys);

            Message mqMsg = new Message(topic, "*", keys, messageBody);
            producer.send(mqMsg, new SendCallback() {
                @Override
                public void onSuccess(SendResult sendResult) {
                    logger.info("CompareTransitStockProducer|sendMessage|发送库存结果 {}", sendResult);
                }

                @Override
                public void onException(Throwable throwable) {
                    logger.error("CompareTransitStockProducer|sendMessage|发送库存消息失败1 Message:{}", JSON.toJSON(mqMsg));
                    logger.error(throwable.getMessage(), throwable);
                }
            });

        } catch (Exception e) {
            logger.error("CompareTransitStockProducer|sendMessage|发送消息异常", e);
        }

    }

    @PreDestroy
    public void stop() {
        if (producer != null) {
            producer.shutdown();
            logger.info("MQ：关闭生产者[CompareTransitStockProducer]");
        }
    }


}
