package com.cowell.bam.mq;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.cowell.bam.common.TracerBean;
import com.cowell.bam.config.ApolloConfig;
import com.cowell.bam.service.StockCompareService;
import com.cowell.bam.service.dto.CmallRequestDTO;
import com.cowell.bam.service.dto.CmallStockRequestDTO;
import com.cowell.bam.service.dto.CmallTableRequestDTO;
import com.cowell.bam.service.dto.SendRequestBodyDTO;
import com.cowell.bam.service.impl.ThirdService;
import com.cowell.bam.util.CommonUtil;
import com.cowell.tools.utils.RocketMqHook;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.rocketmq.client.consumer.DefaultMQPushConsumer;
import org.apache.rocketmq.client.consumer.listener.ConsumeConcurrentlyContext;
import org.apache.rocketmq.client.consumer.listener.ConsumeConcurrentlyStatus;
import org.apache.rocketmq.client.consumer.listener.MessageListenerConcurrently;
import org.apache.rocketmq.client.exception.MQClientException;
import org.apache.rocketmq.common.message.MessageExt;
import org.apache.rocketmq.remoting.common.RemotingHelper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.sleuth.Span;
import org.springframework.cloud.sleuth.annotation.NewSpan;
import org.springframework.stereotype.Component;

import javax.annotation.PreDestroy;
import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * @Autor <EMAIL>
 * @Date 2020-09-14 10:18:00
 */
@Slf4j
@Component
public class CompareConsumer implements MessageListenerConcurrently, ConsumerAdapter {

    /**
     * 大仓库存默认批次号
     */
    private static final String DEFAULT_BATCH_NO = "gaojicode";

    /**
     * 大仓库存默认批号
     */
    private static final String DEFAULT_BATCH_CODE = "gaojino";

    @Autowired
    private ThirdService thirdService;

    @Resource
    private TracerBean tracerBean;

    private DefaultMQPushConsumer consumer = null;


    @Value("${apache.rocketmq.saasNamesrvAddr}")
    private String namesrvAddr;

    @Value("${apache.rocketmq.compare_cosumer.topic}")
    private String topic;

    @Value("${apache.rocketmq.compare_cosumer.groupName}")
    private String groupName;

    @Value("${apache.rocketmq.compare_cosumer.stockTag}")
    private String stockTag;


    @Autowired
    private StockCompareService compareService;


    @Autowired
    private ApolloConfig apolloConfig;


    @Autowired
    private SyncStockProducer syncStockProducer;

    @Autowired
    private RocketMqHook rocketMqHook;

    @Override
    public void start() {
        try {
            log.info("CompareConsumer|start|对比consumer开始启动");

            consumer = new DefaultMQPushConsumer(groupName);
            consumer.setNamesrvAddr(namesrvAddr);
            consumer.subscribe(topic, "*");
            consumer.registerMessageListener(this);
            consumer.setInstanceName(topic);

            CommonUtil.fillConsumerInfo(consumer);

            consumer.start();
            rocketMqHook.consumer(consumer);
            log.info("CompareConsumer|start|对比consumer启动成功|group={},topic={}", groupName, topic);

        } catch (MQClientException e) {
            log.error("CompareConsumer|start|对比consumer启动失败", e);
            throw new RuntimeException(e.getMessage(), e);
        }
    }

    @PreDestroy
    public void stop() {
        if (consumer != null) {
            consumer.shutdown();
            log.info("CompareConsumer|stop|对比consumer关闭成功! ");
        }
    }


    @Override
    @NewSpan
    public ConsumeConcurrentlyStatus consumeMessage(List<MessageExt> msgs, ConsumeConcurrentlyContext context) {

        Span span = tracerBean.startSpan();

        try {

            MessageExt msg = msgs.get(0);
            String tags = msg.getTags();

            if (!stockTag.equals(tags)) {
                return ConsumeConcurrentlyStatus.CONSUME_SUCCESS;
            }

            String messageBody = new String(msg.getBody(), RemotingHelper.DEFAULT_CHARSET);
            log.info("CompareConsumer|consumeMessage|接收库存对比消息|msgId={},topic={},keys={},data={}",
                msg.getMsgId(), msg.getTopic(), msg.getKeys(), messageBody);


            List<CmallStockRequestDTO> syncStockList = new ArrayList<>();

            SendRequestBodyDTO sendRequestBodyDTO;

            try {

                sendRequestBodyDTO = JSONObject.parseObject(new String(msg.getBody(), RemotingHelper.DEFAULT_CHARSET), SendRequestBodyDTO.class);
                if (Objects.isNull(sendRequestBodyDTO) || StringUtils.isEmpty(sendRequestBodyDTO.getRequestBody())) {
                    log.info("CompareConsumer|consumeMessage|接收库存对比消息缺少参数{}", sendRequestBodyDTO);
                    return ConsumeConcurrentlyStatus.CONSUME_SUCCESS;
                }

                CmallTableRequestDTO requestDTO = JSONObject.parseObject(sendRequestBodyDTO.getRequestBody(), CmallTableRequestDTO.class);

                if (Objects.isNull(requestDTO) || CollectionUtils.isEmpty(requestDTO.getTable())) {
                    log.info("CompareConsumer|consumeMessage|英克海典库存同步缺少参数{}", sendRequestBodyDTO);
                    return ConsumeConcurrentlyStatus.CONSUME_SUCCESS;
                }

                syncStockList.addAll(convert(requestDTO.getTable()));
            } catch (Exception e) {
                log.error("CompareConsumer|consumeMessage|消费英克、海典库存同步消息失败", e);
                return ConsumeConcurrentlyStatus.RECONSUME_LATER;
            }
            log.info("CompareConsumer|consumeMessage|size={}", CollectionUtils.isEmpty(syncStockList) ? 0 : syncStockList.size());

            boolean diff = compareService.compareStock(syncStockList);

            //如果存在差异同步库存中心更新库存
            if (Objects.nonNull(diff) && true == diff) {
                log.info("CompareConsumer|consumeMessage|存在库存差异,同步消息到库存中心|msgId={}", msg.getMsgId());
                syncStockProducer.sendMessage(messageBody);
            }

        } catch (Exception e) {
            log.error("CompareConsumer|consumeMessage|bam消费库存对比数据失败", e);
            return ConsumeConcurrentlyStatus.RECONSUME_LATER;
        } finally {
            tracerBean.close(span);
        }
        return ConsumeConcurrentlyStatus.CONSUME_SUCCESS;
    }

    /**
     * 获取真实的同步库存数据
     *
     * @param tableList
     * @return
     */
    private List<CmallStockRequestDTO> convert(List<CmallRequestDTO> tableList) {

        List<CmallStockRequestDTO> syncStockList = new ArrayList<>();
        if (CollectionUtils.isEmpty(tableList)) {
            return syncStockList;
        }

        for (CmallRequestDTO requestDTO : tableList) {

            if (Objects.isNull(requestDTO.getBdata())) {
                log.info("CompareConsumer|convert|bdata为空:{}", requestDTO);
                continue;
            }
            //处理Bdata数据
            syncStockList.addAll(getRequestStockList(requestDTO));
        }
        return syncStockList;
    }

    /**
     * 获取BData里面真实的库存同步数据
     *
     * @param requestDTO
     * @return
     */
    private List<CmallStockRequestDTO> getRequestStockList(CmallRequestDTO requestDTO) {

        List<CmallStockRequestDTO> requestStockList = Lists.newArrayList();

        JSONArray bDataArr = requestDTO.getBdata();
        for (int i = 0; i < bDataArr.size(); i++) {
            CmallStockRequestDTO stockDTO = JSONObject.parseObject(JSON.toJSONString(bDataArr.getJSONObject(i)), CmallStockRequestDTO.class);

            log.info("CompareConsumer|processBData|同步数据:{}|{}|{}|{}|{}|{}|{}|{}|{}|{}|{}|{}|{}|{}",
                requestDTO.getBguid(), stockDTO.getCompId(), stockDTO.getBusNo(), stockDTO.getWareCode(),
                stockDTO.getMakeNo(), stockDTO.getBatchNo(), stockDTO.gettNum(),
                stockDTO.getNum(), stockDTO.getQualifiedStock(), stockDTO.getCheckStock(),
                stockDTO.getUnqualifiedStock(), stockDTO.getQualifiedLockStock(),
                stockDTO.getUnQualifiedLockStock(),  stockDTO.getSyncDate());

            //判断同步的数据是否是DC大仓对比数据

//            if (!apolloConfig.isDC(stockDTO.getBusNo())) {
//                continue;
//            }

            //设置默认批次号
            if (StringUtils.isBlank(stockDTO.getBatchNo())) {
                stockDTO.setBatchNo(DEFAULT_BATCH_NO);
            }

            //设置默认批号
            if (StringUtils.isBlank(stockDTO.getMakeNo())) {
                stockDTO.setMakeNo(DEFAULT_BATCH_CODE);
            }

            if (StringUtils.isBlank(stockDTO.getBatchNo())
                || StringUtils.isBlank(stockDTO.getBusNo())
                || StringUtils.isBlank(stockDTO.getWareCode())
                || StringUtils.isBlank(stockDTO.getMakeNo())) {
                log.error("CompareConsumer|processBData|同步数据中存在必填字段为空:{}", stockDTO);
                continue;
            }
            stockDTO.setBdatetime(requestDTO.getBdatetime());
            requestStockList.add(stockDTO);
        }
        return requestStockList;
    }


}
