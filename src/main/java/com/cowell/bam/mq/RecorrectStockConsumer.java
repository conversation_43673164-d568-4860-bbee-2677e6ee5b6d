package com.cowell.bam.mq;

import com.alibaba.fastjson.JSONObject;
import com.cowell.bam.common.TracerBean;
import com.cowell.bam.service.StockCompareService;
import com.cowell.bam.service.dto.HDDataInfoDTO;
import com.cowell.tools.utils.RocketMqHook;
import org.apache.commons.lang3.StringUtils;
import org.apache.rocketmq.client.consumer.DefaultMQPushConsumer;
import org.apache.rocketmq.client.consumer.listener.ConsumeConcurrentlyContext;
import org.apache.rocketmq.client.consumer.listener.ConsumeConcurrentlyStatus;
import org.apache.rocketmq.client.consumer.listener.MessageListenerConcurrently;
import org.apache.rocketmq.client.exception.MQClientException;
import org.apache.rocketmq.common.consumer.ConsumeFromWhere;
import org.apache.rocketmq.common.message.MessageExt;
import org.apache.rocketmq.common.protocol.heartbeat.MessageModel;
import org.apache.rocketmq.remoting.common.RemotingHelper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.sleuth.Span;
import org.springframework.cloud.sleuth.annotation.NewSpan;
import org.springframework.stereotype.Component;

import javax.annotation.PreDestroy;
import javax.annotation.Resource;
import java.util.Arrays;
import java.util.List;

@Component
public class RecorrectStockConsumer implements MessageListenerConcurrently, ConsumerAdapter {

    private static final Logger logger = LoggerFactory.getLogger(RecorrectStockConsumer.class);

    @Resource
    private TracerBean tracerBean;

    private DefaultMQPushConsumer consumer = null;

    @Value("${apache.rocketmq.namesrvAddr}")
    private String namesrvAddr;

    @Value("${apache.rocketmq.recorrect_stock_consumer.topic}")
    private String topic;
    @Value("${apache.rocketmq.recorrect_stock_consumer.group}")
    private String group;
    @Value("${apache.rocketmq.recorrect_stock_consumer.tag}")
    private String tag;
    @Autowired
    private StockCompareService stockCompareService;

    @Autowired
    private RecorrectStockConsumer recorrectStockConsumer;

    @Autowired
    private RocketMqHook rocketMqHook;

    @Value("${recorrectStockStoreNoBlack:}")
    private String recorrectStockStoreNoBlack;

    @Override
    public void start() {
        try {

            consumer = new DefaultMQPushConsumer(group);
            logger.info("MQ：starting recorrect_stock_consumer ......");
            consumer.setNamesrvAddr(namesrvAddr);
            consumer.setConsumeFromWhere(ConsumeFromWhere.CONSUME_FROM_LAST_OFFSET);
            consumer.setMessageModel(MessageModel.CLUSTERING);
            consumer.subscribe(topic, "*");
            consumer.registerMessageListener(recorrectStockConsumer);
            consumer.setMaxReconsumeTimes(3);
            consumer.setConsumeThreadMax(2);
            consumer.setConsumeThreadMin(1);
            consumer.setConsumeTimeout(5);
            consumer.setPullThresholdForQueue(20);
            consumer.setPullThresholdForTopic(20);
            consumer.setInstanceName(topic);
            consumer.start();
            rocketMqHook.consumer(consumer);
            logger.info("MQ：starting recorrect_stock_consumer   is success ! group:{},topic:{},tag:{}", "RecorrectStockConsumer", topic, "*");
        } catch (MQClientException e) {
            logger.error("MQ：starting recorrect_stock_consumer is fail", e.getResponseCode(), e.getErrorMessage());
            throw new RuntimeException(e.getMessage(), e);
        }
    }

    @PreDestroy
    public void stop() {
        if (consumer != null) {
            consumer.shutdown();
            logger.info("MQ：stop recorrect_stock_consumer success! ");
        }
    }


    @Override
    @NewSpan
    public ConsumeConcurrentlyStatus consumeMessage(List<MessageExt> msgs, ConsumeConcurrentlyContext context) {
        Span span = tracerBean.startSpan();
        try {
            for (int index = 0; index < msgs.size(); index++) {
                MessageExt msg = msgs.get(index);
                HDDataInfoDTO hdDataInfoDTO = null;
                try {
                    hdDataInfoDTO = JSONObject.parseObject(new String(msg.getBody(), RemotingHelper.DEFAULT_CHARSET), HDDataInfoDTO.class);
                    logger.info("RecorrectStockConsumer|handleMessage|海典库存价格比对msg:{},sendRequestBodyDTO:{}",msg,hdDataInfoDTO);
                    if (hdDataInfoDTO == null) {
                        logger.warn("RecorrectStockConsumer|handleMessage|海典库存价格比对缺少参数{}", hdDataInfoDTO);
                        return ConsumeConcurrentlyStatus.CONSUME_SUCCESS;
                    }
                    if(this.isBlackStoreId(hdDataInfoDTO.getBusNo())){
                        logger.warn("RecorrectStockConsumer|handleMessage|修正SAP库存数据门店是黑名单,不需要处理[{}]", hdDataInfoDTO);
                        return ConsumeConcurrentlyStatus.CONSUME_SUCCESS;
                    }

                    stockCompareService.sendParam2HD(hdDataInfoDTO.getComId(),
                        hdDataInfoDTO.getBusNo(),hdDataInfoDTO.getCompareDataInfoList());

                } catch (Exception e) {
                    logger.error("RecorrectStockConsumer|handleMessage|矫正库存数据失败:sendRequestBodyDTO:{}",hdDataInfoDTO, e);
                    return ConsumeConcurrentlyStatus.RECONSUME_LATER;
                }
            }
        } catch (Exception e) {
            logger.error("RecorrectStockConsumer|handleMessage|库存价格比对数据失败", e);
            return ConsumeConcurrentlyStatus.RECONSUME_LATER;
        } finally {
            tracerBean.close(span);
        }
        return ConsumeConcurrentlyStatus.CONSUME_SUCCESS;
    }

    /**
     * 验证是否黑名单门店
     * @param busNo
     * @return
     */
    private boolean isBlackStoreId(String busNo){
        if(StringUtils.isBlank(recorrectStockStoreNoBlack)){
            return true;
        }
        if(StringUtils.isNotBlank(busNo) &&
                Arrays.stream(recorrectStockStoreNoBlack.split(",")).anyMatch(t->StringUtils.equals(busNo,t))){
            return true;
        }
        return false;
    }

}
