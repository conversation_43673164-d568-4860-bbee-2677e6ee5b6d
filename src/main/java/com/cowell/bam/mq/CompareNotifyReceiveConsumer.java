package com.cowell.bam.mq;

import com.alibaba.fastjson.JSONObject;
import com.cowell.bam.mq.message.ComparisonModelConfig;
import com.cowell.bam.service.CommonCompareService;
import com.cowell.tools.utils.RocketMqHook;
import org.apache.rocketmq.client.consumer.DefaultMQPushConsumer;
import org.apache.rocketmq.client.consumer.listener.ConsumeConcurrentlyContext;
import org.apache.rocketmq.client.consumer.listener.ConsumeConcurrentlyStatus;
import org.apache.rocketmq.client.consumer.listener.MessageListenerConcurrently;
import org.apache.rocketmq.client.exception.MQClientException;
import org.apache.rocketmq.common.consumer.ConsumeFromWhere;
import org.apache.rocketmq.common.message.MessageExt;
import org.apache.rocketmq.common.protocol.heartbeat.MessageModel;
import org.apache.rocketmq.remoting.common.RemotingHelper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.sleuth.annotation.NewSpan;
import org.springframework.stereotype.Component;

import javax.annotation.PreDestroy;
import java.util.List;

/**
 * 对比价格异常 自产自销 mq
 * <AUTHOR>
 */
@Component
public class CompareNotifyReceiveConsumer implements MessageListenerConcurrently, ConsumerAdapter {


    private static final Logger logger = LoggerFactory.getLogger(CompareNotifyReceiveConsumer.class);

    //测试环境 mq： 10.8.131.14:9876;10.8.131.15:9876
    //topic: comparison_notify_topic_test
    //生产预发 mq:    biz-core-microservice-rocketmq-01_prod.cowelltech.com:9876
    //topic: comparison_notify_topic_stage / prod
    //tag:   配置的id

    @Value("${apache.rocketmq.namesrvAddr}")
    private String namesrvAddr;

    @Value("${apache.rocketmq.comparison_notify.topic}")
    private String topic;
    @Value("${apache.rocketmq.comparison_notify.groupName}")
    private String groupName;

    private final DefaultMQPushConsumer consumer = new DefaultMQPushConsumer();
    private static final String INSTANCE_NAME = "comparison-notify-consumer-instance";

    @Autowired
    private CompareNotifyReceiveConsumer compareNotifyReceiveConsumer;

    @Autowired
    private RocketMqHook rocketMqHook;

    @Autowired
    private CommonCompareService commonCompareService;

    /**
     * 初始化
     *
     * @throws MQClientException
     */
    @Override
    public void start() {
        try {
            logger.info("MQ：启动消费者[CompareNotifyReceiveConsumer]namesrvAddr:"+ namesrvAddr);
            consumer.setNamesrvAddr(namesrvAddr);
            consumer.setConsumerGroup(groupName+topic);
            consumer.setInstanceName(INSTANCE_NAME);
            consumer.setConsumeFromWhere(ConsumeFromWhere.CONSUME_FROM_LAST_OFFSET);
            // 集群消费模式
            consumer.setMessageModel(MessageModel.CLUSTERING);
            // 订阅主题
            consumer.subscribe(topic, "*");
            // 注册消息监听器
            consumer.registerMessageListener(compareNotifyReceiveConsumer);
            //单位分钟，默认15分钟发一版
            consumer.setConsumeTimeout(120);
            consumer.setPullThresholdForQueue(50);
            consumer.setPullThresholdForTopic(50);
            // 启动消费端
            consumer.start();
            rocketMqHook.consumer(consumer);
        } catch (MQClientException e) {
            logger.info("MQ：启动消费者[CompareNotifyReceiveConsumer]失败！异常: {}-{}", e.getResponseCode(), e.getErrorMessage());
            throw new RuntimeException(e.getMessage(), e);
        }

    }

    @Override
    @NewSpan
    public ConsumeConcurrentlyStatus consumeMessage(List<MessageExt> msgs, ConsumeConcurrentlyContext context) {
        logger.info("MQ：消费者[CompareNotifyReceiveConsumer]请求参数 msgs :{} context:{}", msgs,context);
        int index = 0;
        try {
            for (; index < msgs.size(); index++) {
                MessageExt msg = msgs.get(index);
                String messageBody = new String(msg.getBody(), RemotingHelper.DEFAULT_CHARSET);
                logger.info("MQ：消费者[CompareNotifyReceiveConsumer]接受的参数 msg :{} ", messageBody);
                ComparisonModelConfig message = JSONObject.parseObject(messageBody, ComparisonModelConfig.class);
                commonCompareService.compareNotify(message);
                logger.info("MQ：消费者[CompareNotifyReceiveConsumer]消费成功");
            }
        } catch (Exception e) {
            logger.warn("对比事件消息消费失败: ", e);
            return ConsumeConcurrentlyStatus.RECONSUME_LATER;
        }
        return ConsumeConcurrentlyStatus.CONSUME_SUCCESS;
    }



    @PreDestroy
    public void stop() {
        consumer.shutdown();
        logger.warn("MQ：CompareNotifyReceiveConsumer consumer!");
    }

}
