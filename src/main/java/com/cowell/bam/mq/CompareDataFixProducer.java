package com.cowell.bam.mq;

import com.alibaba.fastjson.JSON;
import com.cowell.tools.utils.RocketMqHook;
import com.google.common.collect.Lists;
import org.apache.rocketmq.client.exception.MQClientException;
import org.apache.rocketmq.client.producer.DefaultMQProducer;
import org.apache.rocketmq.client.producer.SendCallback;
import org.apache.rocketmq.client.producer.SendResult;
import org.apache.rocketmq.common.message.Message;
import org.apache.rocketmq.remoting.common.RemotingHelper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.annotation.PreDestroy;
import java.util.ArrayList;
import java.util.List;

/**
 * 比对结果下发
 */
@Component
public class CompareDataFixProducer {

    private static final Logger logger = LoggerFactory.getLogger(CompareDataFixProducer.class);

    @Value("${apache.rocketmq.namesrvAddr}")
    private String namesrvAddr;

    @Value("${apache.rocketmq.compare_result_notify_producer.groupName}")
    private String groupName;


    private final DefaultMQProducer producer = new DefaultMQProducer();

    @Autowired
    private RocketMqHook rocketMqHook;

    /**
     * 初始化
     */
    @PostConstruct
    public void start() {
        try {
            logger.info("MQ：启动生产者[CompareDataFixProducer] namesrvAddr:{}", namesrvAddr);
            producer.setNamesrvAddr(namesrvAddr);
            producer.setProducerGroup(groupName);
            producer.start();
            rocketMqHook.producer(producer);
        } catch (MQClientException e) {
            logger.error("MQ：启动生产者失败[CompareDataFixProducer]：{}-{}", e.getResponseCode(), e.getErrorMessage());
            throw new RuntimeException(e.getMessage(), e);
        }
    }

    /**
     * 发送MQ消息
     * @param topic topic
     * @param tag tag
     * @param message 消息内容
     */
    public void sendMessage(String topic, String tag, String message) {

        try {
            byte[] messageBody = message.getBytes(RemotingHelper.DEFAULT_CHARSET);
            Message mqMsg = new Message(topic, tag, String.valueOf(System.currentTimeMillis()), messageBody);
            producer.send(mqMsg, new SendCallback() {
                @Override
                public void onSuccess(SendResult sendResult) {
                }

                @Override
                public void onException(Throwable throwable) {
                    logger.error("生产者[CompareDataFixProducer|sendMessage]发送订单事件消息失败1 Message:{}", JSON.toJSON(mqMsg));
                    logger.error(throwable.getMessage(), throwable);
                }
            });

        } catch (Exception e) {
            if(message != null){
                logger.error("生产者[CompareDataFixProducer|sendMessage]发送订单事件消息失败2 Message:{}", JSON.toJSON(message));
            }
            logger.error("MQ: 生产者[CompareDataFixProducer|sendMessage]发送消息异常", e);
        }

    }

    /**
     * 发送MQ消息
     * @param topic topic
     * @param tag tag
     * @param messages 消息内容集合
     */
    public void sendBatchMessage(String topic, String tag, List<String> messages) {

        try {
            List<Message> messageList = new ArrayList<>(messages.size());
            for (String message : messages) {
                byte[] messageBody = message.getBytes(RemotingHelper.DEFAULT_CHARSET);
                Message mqMsg = new Message(topic, tag, String.valueOf(System.currentTimeMillis()), messageBody);
                messageList.add(mqMsg);
            }

            Lists.partition(messageList, 100).forEach(list -> {
                try {
                    producer.send(messageList);
                } catch (Exception e) {
                    logger.error("MQ: 生产者[CompareDataFixProducer|sendBatchMessage]批量发送消息异常", e);
                }
            });

        } catch (Exception e) {
            logger.error("MQ: 生产者[CompareDataFixProducer|sendBatchMessage]发送消息异常", e);
        }

    }

    @PreDestroy
    public void stop() {
        if (producer != null) {
            producer.shutdown();
            logger.info("MQ：关闭生产者[CompareDataFixProducer]");
        }
    }


}
