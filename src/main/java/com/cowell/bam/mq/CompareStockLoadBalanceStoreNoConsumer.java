package com.cowell.bam.mq;

import com.alibaba.fastjson.JSONObject;
import com.cowell.bam.handler.AbstractChannelHandler;
import com.cowell.bam.mq.message.CompareStockDTO;
import com.cowell.bam.service.StockCompareService;
import com.cowell.bam.web.rest.BaseController;
import com.cowell.common.utils.SentinelUtils;
import com.cowell.tools.utils.RocketMqHook;
import org.apache.rocketmq.client.consumer.DefaultMQPushConsumer;
import org.apache.rocketmq.client.consumer.listener.ConsumeConcurrentlyContext;
import org.apache.rocketmq.client.consumer.listener.ConsumeConcurrentlyStatus;
import org.apache.rocketmq.client.consumer.listener.MessageListenerConcurrently;
import org.apache.rocketmq.client.exception.MQClientException;
import org.apache.rocketmq.common.consumer.ConsumeFromWhere;
import org.apache.rocketmq.common.message.MessageExt;
import org.apache.rocketmq.common.protocol.heartbeat.MessageModel;
import org.apache.rocketmq.remoting.common.RemotingHelper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.sleuth.annotation.NewSpan;
import org.springframework.stereotype.Component;

import javax.annotation.PreDestroy;
import java.util.List;

/**
 * 对比库存 自产自销 mq
 * <AUTHOR>
 */
@Component
public class CompareStockLoadBalanceStoreNoConsumer implements MessageListenerConcurrently, ConsumerAdapter {


    private static final Logger logger = LoggerFactory.getLogger(CompareStockLoadBalanceStoreNoConsumer.class);

    @Value("${apache.rocketmq.namesrvAddr}")
    private String namesrvAddr;

    @Value("${apache.rocketmq.compare_stock_balance.topic}")
    private String topic;
    @Value("${apache.rocketmq.compare_stock_balance.consumer-group}")
    private String group;

    private final DefaultMQPushConsumer consumer = new DefaultMQPushConsumer();


    @Autowired
    private BaseController baseController;

    @Autowired
    private StockCompareService stockCompareService;

    @Autowired
    private List<AbstractChannelHandler> handlerList;

    @Autowired
    private CompareStockLoadBalanceStoreNoConsumer compareStockLoadBalanceStoreNoConsumer;

    @Autowired
    private RocketMqHook rocketMqHook;

    /**
     * 限流key
     */
    private final static String FLOW_CONTROL_KEY = "MQ-CompareStockLoadBalanceStoreNoConsumer";

    /**
     * 初始化
     *
     * @throws MQClientException
     */
    @Override
    public void start() {
        try {
            logger.info("MQ：启动消费者[CompareStockLoadBalanceStoreNoConsumer]namesrvAddr:"+ namesrvAddr);
            consumer.setNamesrvAddr(namesrvAddr);
            consumer.setConsumerGroup(group+"-storeNo");
            consumer.setInstanceName(topic);
            consumer.setConsumeFromWhere(ConsumeFromWhere.CONSUME_FROM_LAST_OFFSET);
            // 集群消费模式
            consumer.setMessageModel(MessageModel.CLUSTERING);
            // 订阅主题
            consumer.subscribe(topic, "storeNo");
            // 注册消息监听器
            consumer.registerMessageListener(compareStockLoadBalanceStoreNoConsumer);
            //单位分钟，默认15分钟发一版
            consumer.setConsumeTimeout(600);
            consumer.setPullThresholdForQueue(50);
            consumer.setPullThresholdForTopic(50);
            // 启动消费端
            consumer.start();
            rocketMqHook.consumer(consumer);
        } catch (MQClientException e) {
            logger.info("MQ：启动消费者[CompareStockLoadBalanceStoreNoConsumer]失败！异常: {}-{}", e.getResponseCode(), e.getErrorMessage());
            throw new RuntimeException(e.getMessage(), e);
        }

    }

    @Override
    @NewSpan
    public ConsumeConcurrentlyStatus consumeMessage(List<MessageExt> msgs, ConsumeConcurrentlyContext context) {
        if (SentinelUtils.limitResource(FLOW_CONTROL_KEY)) {
            // 被限流了，重新消费
            return ConsumeConcurrentlyStatus.RECONSUME_LATER;
        }
        try {
            for (MessageExt msg : msgs) {
                String messageBody = new String(msg.getBody(), RemotingHelper.DEFAULT_CHARSET);
                logger.info("CompareStockLoadBalanceStoreNoConsumer|consumeMessage:{}", messageBody);
                CompareStockDTO data = JSONObject.parseObject(messageBody, CompareStockDTO.class);
                stockCompareService.compareStock(data);
                logger.info("CompareStockLoadBalanceStoreNoConsumer|consumeMessage|比对完成, data:{}", data);
            }
        } catch (Exception e) {
            logger.warn("CompareStockLoadBalanceStoreNoConsumer|consumeMessage|比对失败", e);
            return ConsumeConcurrentlyStatus.RECONSUME_LATER;
        }
        return ConsumeConcurrentlyStatus.CONSUME_SUCCESS;
    }



    @PreDestroy
    public void stop() {
        consumer.shutdown();
        logger.warn("MQ：CompareStockLoadBalanceStoreNoConsumer consumer!");
    }

    public static void main(String[] args) {
        String ss = "SS";
    }

}
