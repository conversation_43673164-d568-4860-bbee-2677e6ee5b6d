package com.cowell.bam.mq;

import com.alibaba.fastjson.JSONObject;
import com.cowell.bam.enums.SyncTypeEnum;
import com.cowell.bam.handler.AbstractChannelHandler;
import com.cowell.bam.service.dto.CompareTransitMessageDTO;
import com.cowell.bam.web.rest.BaseController;
import com.cowell.tools.utils.RocketMqHook;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.rocketmq.client.consumer.DefaultMQPushConsumer;
import org.apache.rocketmq.client.consumer.listener.ConsumeConcurrentlyContext;
import org.apache.rocketmq.client.consumer.listener.ConsumeConcurrentlyStatus;
import org.apache.rocketmq.client.consumer.listener.MessageListenerConcurrently;
import org.apache.rocketmq.client.exception.MQClientException;
import org.apache.rocketmq.common.consumer.ConsumeFromWhere;
import org.apache.rocketmq.common.message.MessageExt;
import org.apache.rocketmq.common.protocol.heartbeat.MessageModel;
import org.apache.rocketmq.remoting.common.RemotingHelper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.sleuth.annotation.NewSpan;
import org.springframework.stereotype.Component;

import javax.annotation.PreDestroy;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2022/10/30 22:24
 */
@Component
public class CompareTransitStockConsumer implements MessageListenerConcurrently, ConsumerAdapter {

    private static final Logger logger = LoggerFactory.getLogger(CompareTransitStockConsumer.class);

    @Value("${apache.rocketmq.namesrvAddr}")
    private String namesrvAddr;

    @Value("${apache.rocketmq.compare_transit_stock.topic}")
    private String topic;
    @Value("${apache.rocketmq.compare_transit_stock.consumer-group}")
    private String group;
    @Value("${compare.transit.consumeThreadMax:1}")
    private int consumeThreadMax;

    private final DefaultMQPushConsumer consumer = new DefaultMQPushConsumer();


    @Autowired
    private BaseController baseController;

    @Autowired
    private List<AbstractChannelHandler> handlerList;

    @Autowired
    private CompareTransitStockConsumer compareTransitStockConsumer;

    @Autowired
    private RocketMqHook rocketMqHook;

    /**
     * 初始化
     *
     * @throws MQClientException
     */
    @Override
    public void start() {
        try {
            logger.info("MQ：启动消费者[CompareTransitStockConsumer]namesrvAddr:"+ namesrvAddr);
            consumer.setNamesrvAddr(namesrvAddr);
            consumer.setConsumerGroup(group+topic);
            consumer.setInstanceName(topic);
            consumer.setConsumeFromWhere(ConsumeFromWhere.CONSUME_FROM_LAST_OFFSET);
            // 集群消费模式
            consumer.setMessageModel(MessageModel.CLUSTERING);
            // 订阅主题
            consumer.subscribe(topic, "*");
            // 注册消息监听器
            consumer.registerMessageListener(compareTransitStockConsumer);
            //单位分钟，默认15分钟发一版
            consumer.setConsumeTimeout(600);
            consumer.setPullThresholdForQueue(50);
            consumer.setPullThresholdForTopic(50);
            // 启动消费端
            consumer.start();
            rocketMqHook.consumer(consumer);
        } catch (MQClientException e) {
            logger.info("MQ：启动消费者[CompareTransitStockConsumer]失败！异常: {}-{}", e.getResponseCode(), e.getErrorMessage());
            throw new RuntimeException(e.getMessage(), e);
        }

    }

    @Override
    @NewSpan
    public ConsumeConcurrentlyStatus consumeMessage(List<MessageExt> msgs, ConsumeConcurrentlyContext context) {

        try {
            logger.info("CompareTransitStockConsumer|consumeMessage:{}.size:{}..", msgs, CollectionUtils.isNotEmpty(msgs)?msgs.size():0);
            for (int index = 0; index < msgs.size(); index++) {
                MessageExt msg = msgs.get(index);
                String messageBody = new String(msg.getBody(), RemotingHelper.DEFAULT_CHARSET);
                logger.info("CompareTransitStockConsumer|consumeMessage|keys:{}.", msg.getKeys());
                CompareTransitMessageDTO messageDTO = JSONObject.parseObject(messageBody, CompareTransitMessageDTO.class);
                if(Objects.isNull(messageDTO)){
                    logger.warn("messageDTO为空");
                    continue;
                }
                baseController.selectChannelHandler(SyncTypeEnum.STOCK_TRANSIT.getCode(), handlerList).compare(messageDTO);
                logger.info("CompareTransitStockConsumer|consumeMessage|比对完成comId:{}",messageDTO.getComId());
            }
        } catch (Exception e) {
            logger.warn("CompareTransitStockConsumer|consumeMessage|比对失败", e);
            return ConsumeConcurrentlyStatus.RECONSUME_LATER;
        }
        return ConsumeConcurrentlyStatus.CONSUME_SUCCESS;
    }



    @PreDestroy
    public void stop() {
        consumer.shutdown();
        logger.warn("MQ：CompareTransitStockConsumer consumer!");
    }

}
