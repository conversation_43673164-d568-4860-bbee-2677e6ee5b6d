package com.cowell.bam.mq;

import com.cowell.tools.utils.RocketMqHook;
import org.apache.rocketmq.client.exception.MQClientException;
import org.apache.rocketmq.client.producer.DefaultMQProducer;
import org.apache.rocketmq.client.producer.SendCallback;
import org.apache.rocketmq.client.producer.SendResult;
import org.apache.rocketmq.common.message.Message;
import org.apache.rocketmq.remoting.common.RemotingHelper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.ApplicationArguments;
import org.springframework.boot.ApplicationRunner;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

/**
 * 通知库存中心同步前端库存和第三方平台
 *
 * @Author: lss
 * @Date: 2018/8/7
 */
@Component
@Order(1000)
public class NoticeSendStock2ThirdPlatformProducer implements ApplicationRunner {
    private static final Logger log = LoggerFactory.getLogger(NoticeSendStock2ThirdPlatformProducer.class);

    private DefaultMQProducer producer;

    @Value("${apache.rocketmq.namesrvAddr}")
    private String namesrvAddr;

    @Value("${apache.rocketmq.notice_send_stock.topic}")
    private String topic;
    @Value("${apache.rocketmq.notice_send_stock.group}")
    private String groupName;
    @Value("${apache.rocketmq.notice_send_stock.tag}")
    private String tag;

    @Autowired
    private RocketMqHook rocketMqHook;

    @Override
    public void run(ApplicationArguments applicationArguments){
        initProducer();
    }

    /**
     * 初始化producer
     */
   private void initProducer(){
       producer = new DefaultMQProducer(groupName);
       producer.setNamesrvAddr(namesrvAddr);
       producer.setInstanceName(topic);
       try {
           log.info("================================init NoticeSendStock2ThirdPlatformProducer MQ producer================================");
           producer.start();
           rocketMqHook.producer(producer);
           log.info("================================init NoticeSendStock2ThirdPlatformProducer MQ producer success!!!================================");
       } catch (MQClientException e) {
           log.error("MQ：启动生产者失败：{}-{}", e.getResponseCode(), e.getErrorMessage());
           throw new RuntimeException(e.getMessage(), e);
       }
   }

    public void send(String data) {
        log.info("NoticeSendStock2ThirdPlatformProducer|send|生产者发送消息 data:{}", data);
        try {
            byte[] messageBody = data.getBytes(RemotingHelper.DEFAULT_CHARSET);
            Message mqMsg = new Message(topic, tag, String.valueOf(System.currentTimeMillis()), messageBody);
            if(producer == null){
                return;
            }
//            byte[] messageBody = JSON.toJSONString(data).getBytes(RemotingHelper.DEFAULT_CHARSET);
//            Message mqMsg = new Message(topic, tag, String.valueOf(System.currentTimeMillis()), messageBody);
            //"1s 5s 10s 30s 1m 2m 3m 4m 5m 6m 7m 8m 9m 10m 20m 30m 1h 2h"
            mqMsg.setDelayTimeLevel(7);
            producer.send(mqMsg, new SendCallback() {
                @Override
                public void onSuccess(SendResult sendResult) {
                    log.info("NoticeSendStock2ThirdPlatformProducer|send|生产者发送消息成功:{}", sendResult);
                }
                @Override
                public void onException(Throwable throwable) {
                    log.error(throwable.getMessage(), throwable);
                }
            });
        } catch (Exception e) {
            log.error("NoticeSendStock2ThirdPlatformProducer|send|方法发生异常，异常信息为", e);
        }
    }

}
