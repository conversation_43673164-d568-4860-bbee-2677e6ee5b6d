package com.cowell.bam.mq;

import com.alibaba.fastjson.JSON;
import com.cowell.bam.service.dto.MessageDto;
import com.cowell.tools.utils.RocketMqHook;
import org.apache.rocketmq.client.exception.MQClientException;
import org.apache.rocketmq.client.producer.DefaultMQProducer;
import org.apache.rocketmq.client.producer.SendCallback;
import org.apache.rocketmq.client.producer.SendResult;
import org.apache.rocketmq.common.message.Message;
import org.apache.rocketmq.remoting.common.RemotingHelper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.annotation.PreDestroy;

@Component
public class MqProduct {
    private static final Logger LOGGER = LoggerFactory.getLogger(MqProduct.class);

    @Value("${apache.rocketmq.namesrvAddr}")
    private String namesrvAddr;
    @Value("${apache.rocketmq.product.retryTimesWhenSend}")
    private int retryTimesWhenSend;
    @Value("${apache.rocketmq.product.sendMsgTimeout}")
    private int sendMsgTimeout;
    private DefaultMQProducer producer = null;

    private static final String producerGroup = "BAM_LOG_SEND_MESSAGE_GROUP";

    @Autowired
    private RocketMqHook rocketMqHook;

    /**
     * 初始化
     */
    @PostConstruct
    public DefaultMQProducer start() {
        try {
            LOGGER.info("执行方法mqProduct#start：开始启动mqProduct生产者");
            producer = new DefaultMQProducer(producerGroup);
            producer.setRetryTimesWhenSendFailed(retryTimesWhenSend);
            producer.setInstanceName("inventoryMQProducer");
            producer.setSendMsgTimeout(sendMsgTimeout);
            producer.setNamesrvAddr(namesrvAddr);
            producer.start();
            rocketMqHook.producer(producer);
        } catch (MQClientException e) {
            LOGGER.error("MQ：执行mqProduct#start启动生产者失败：{}-{}", e.getResponseCode(), e.getErrorMessage(),e);
            throw new RuntimeException(e.getMessage(), e);
        }catch (Exception e){
            LOGGER.error("MQ 启动失败", e.getMessage(),e);
            throw new RuntimeException(e.getMessage(), e);
        }
        return producer;
    }

    /**
     * 发送消息 异步发送
     *
     * @param messageDto 消息内容
     */
    public Boolean sendMessageAsync(MessageDto messageDto) {
        LOGGER.info("开始执行方法mqProduct#sendMessageAsync，入参为{}", JSON.toJSONString(messageDto));
        boolean flag=true;
        try {
            byte[] messageBody = JSON.toJSONString(messageDto).getBytes(RemotingHelper.DEFAULT_CHARSET);
            Message mqMsg = new Message(messageDto.getTopic(), messageDto.getTag(), messageDto.getId(), messageBody);
            producer.send(mqMsg, new SendCallback() {
                @Override
                public void onSuccess(SendResult sendResult) {
                    LOGGER.info("MQ: mqProduct生产者发送消息 {}", sendResult);
                }
                @Override
                public void onException(Throwable throwable) {
                    LOGGER.error(throwable.getMessage(), throwable);
                }
            });
            LOGGER.info("执行方法mqProduct#sendMessageAsync成功，执行结果为{}",flag);
        } catch (Exception e) {
            LOGGER.error("调用mqProduct#sendMessageAsync方法发生异常，异常信息为", e);
            flag=false;
        }
        return flag;
    }
    /**

     * 发送消息 同步发送
     *
     * @param messageDto 消息内容
     */
    public Boolean sendMessageSync(MessageDto messageDto){
        LOGGER.info("开始执行方法mqProduct#sendMessageSync，入参为{}", JSON.toJSONString(messageDto));
        boolean flag=true;
        SendResult result=new SendResult();
        try{
            byte[] messageBody = JSON.toJSONString(messageDto).getBytes(RemotingHelper.DEFAULT_CHARSET);
            Message mqMsg = new Message(messageDto.getTopic(), messageDto.getTag(), messageDto.getId(), messageBody);
            result=producer.send(mqMsg);
            if(!"SEND_OK".equals(result.getSendStatus().name())){
                flag=false;
            }
            LOGGER.info("执行方法mqProduct#sendMessageSync成功，执行结果为{}",flag);
        }catch (Exception e){
            LOGGER.error("调用mqProduct#sendMessageSync方法发生异常，异常信息为", e);
        }
        return flag;
    }
    /**
     * 发送消息 同步发送
     *
     * @param messageDto 消息内容
     */
    public Boolean sendMessageSyncTimeOut(MessageDto messageDto, long timeout){
        LOGGER.info("开始执行方法mqProduct#sendMessageSyncTimeOut，入参为{},timeout为", JSON.toJSONString(messageDto),timeout);
        boolean flag=true;
        SendResult result;
        try{
            byte[] messageBody = JSON.toJSONString(messageDto).getBytes(RemotingHelper.DEFAULT_CHARSET);
            Message mqMsg = new Message(messageDto.getTopic(), messageDto.getTag(), messageDto.getId(), messageBody);
            result=producer.send(mqMsg, timeout);
            if(!"SEND_OK".equals(result.getSendStatus().name())){
                flag=false;
            }
            LOGGER.info("执行方法mqProduct#sendMessageSyncTimeOut成功，执行结果为{}",flag);
        }catch (Exception e){
            LOGGER.error("调用mqProduct#sendMessageSyncTimeOut方法异常，异常信息为",e);
        }
        return flag;
    }
    @PreDestroy
    public void stop() {
        if (producer != null) {
            producer.shutdown();
            LOGGER.info("MQ：执行方法mqProduct#stop关闭生产者");
        }
    }
}
