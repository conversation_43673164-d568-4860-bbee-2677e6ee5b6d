package com.cowell.bam.mq;

import com.alibaba.fastjson.JSON;
import com.cowell.tools.utils.RocketMqHook;
import org.apache.rocketmq.client.exception.MQClientException;
import org.apache.rocketmq.client.producer.DefaultMQProducer;
import org.apache.rocketmq.client.producer.SendCallback;
import org.apache.rocketmq.client.producer.SendResult;
import org.apache.rocketmq.common.message.Message;
import org.apache.rocketmq.remoting.common.RemotingHelper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.ApplicationArguments;
import org.springframework.boot.ApplicationRunner;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

/**
 * 比对后删除海典不存在的库存-生产者
 *
 * @Author: hulijie
 * @Date: 2018/8/7
 */
@Component
@Order(1000)
public class DeleteCompareStockProducer implements ApplicationRunner {
    private static final Logger log = LoggerFactory.getLogger(DeleteCompareStockProducer.class);

    private DefaultMQProducer producer;

    @Value("${apache.rocketmq.namesrvAddr}")
    private String namesrvAddr;

    @Value("${apache.rocketmq.delete_stock_hdcompare-producer.topic}")
    private String topic;
    @Value("${apache.rocketmq.delete_stock_hdcompare-producer.group}")
    private String group;
    @Value("${apache.rocketmq.delete_stock_hdcompare-producer.tag}")
    private String tag;

    @Autowired
    private RocketMqHook rocketMqHook;

    @Override
    public void run(ApplicationArguments applicationArguments){
        initProducer();
    }

    /**
     * 初始化producer
     */
   private void initProducer(){
       producer = new DefaultMQProducer(group);
       producer.setNamesrvAddr(namesrvAddr);
       producer.setInstanceName(topic);
       try {
           log.info("================================init DeleteCompareStockProducer MQ producer================================");
           producer.start();
           rocketMqHook.producer(producer);
           log.info("================================init DeleteCompareStockProducer MQ producer success!!!================================");
       } catch (MQClientException e) {
           log.error("MQ：启动生产者失败：{}-{}", e.getResponseCode(), e.getErrorMessage());
           throw new RuntimeException(e.getMessage(), e);
       }
   }

    public void send(String data) {
        log.info("DeleteCompareStockProducer|send|生产者发送消息 data:{}", data);
        Message message = null;
        try {
            byte[] messageBody = data.getBytes(RemotingHelper.DEFAULT_CHARSET);
            message = new Message(topic, tag, String.valueOf(System.currentTimeMillis()), messageBody);


            Message finalMessage = message;
            if(finalMessage==null){
                log.error("DeleteCompareStockProducer|send|发送消息体为空{}",finalMessage);
                return;
            }
            //判断producer是否为空
            if(producer == null){
                initProducer();
            }
            //再次判断producer是否初始化成功
            if(producer == null){
                log.error("DeleteCompareStockProducer|send||producer 启动失败:{}",producer);
                return;
            }
            producer.send(finalMessage, new SendCallback() {
                @Override
                public void onSuccess(SendResult sendResult) {
                    log.info("DeleteCompareStockProducer|send|生产者发送消息成功:{}.key:{}.", sendResult, finalMessage.getKeys());
                }

                @Override
                public void onException(Throwable throwable) {
                    log.info("DeleteCompareStockProducer|send|生产者发送消息失败:{},{}", group, JSON.toJSON(finalMessage));
                    log.error(throwable.getMessage(), throwable);
                }
            });
        } catch (Exception e) {
            if (message != null) {
                log.error("DeleteCompareStockProducer|send|生产者发送消息失败|producerGroup:{},Message:{}", group, JSON.toJSON(message));
            }
            log.error(e.getMessage(), e);
        }
    }

}
