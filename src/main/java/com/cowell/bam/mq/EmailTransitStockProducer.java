package com.cowell.bam.mq;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.cowell.bam.service.dto.EmailTransitStockDTO;
import com.cowell.tools.utils.RocketMqHook;
import org.apache.rocketmq.client.exception.MQClientException;
import org.apache.rocketmq.client.producer.DefaultMQProducer;
import org.apache.rocketmq.client.producer.SendCallback;
import org.apache.rocketmq.client.producer.SendResult;
import org.apache.rocketmq.common.message.Message;
import org.apache.rocketmq.remoting.common.RemotingHelper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.annotation.PreDestroy;
import java.util.UUID;

/**
 * <AUTHOR>
 * @date 2022/11/2 13:51
 */
@Component
public class EmailTransitStockProducer {
    private static final Logger logger = LoggerFactory.getLogger(EmailTransitStockProducer.class);

    @Value("${apache.rocketmq.namesrvAddr}")
    private String namesrvAddr;

    @Value("${apache.rocketmq.email_transit_stock.topic}")
    private String topic;
    @Value("${apache.rocketmq.email_transit_stock.producer-group}")
    private String group;

    private static final String instanceName = "email_transit_stock-producer";

    private final DefaultMQProducer producer = new DefaultMQProducer();

    @Autowired
    private RocketMqHook rocketMqHook;

    /**
     * 初始化
     */
    @PostConstruct
    public void start() {
        try {
            logger.info("MQ：启动生产者[EmailTransitStockProducer] namesrvAddr:"+namesrvAddr);
            producer.setInstanceName(instanceName);
            producer.setNamesrvAddr(namesrvAddr);
            producer.setProducerGroup(group + topic);
            producer.start();
            rocketMqHook.producer(producer);
        } catch (MQClientException e) {
            logger.error("MQ：启动生产者失败[EmailTransitStockProducer]：{}-{}", e.getResponseCode(), e.getErrorMessage());
            throw new RuntimeException(e.getMessage(), e);
        }
    }

    public void sendMessage(EmailTransitStockDTO emailTransitStockDTO) {

        try {
            String keys = UUID.randomUUID().toString().replaceAll("-", "");
            String data = JSONObject.toJSONString(emailTransitStockDTO);
            byte[] messageBody = data.getBytes(RemotingHelper.DEFAULT_CHARSET);
            logger.info("EmailTransitStockProducer|sendMessage|发送在途库存邮件事件消息|data:{}.|keys:{}", data, keys);

            Message mqMsg = new Message(topic, "*", keys, messageBody);
            producer.send(mqMsg, new SendCallback() {
                @Override
                public void onSuccess(SendResult sendResult) {
                    logger.info("EmailTransitStockProducer|sendMessage|发送在途库存邮件事件消息结果 {}", sendResult);
                }

                @Override
                public void onException(Throwable throwable) {
                    logger.error("EmailTransitStockProducer|sendMessage|发送在途库存邮件事件消息失败 Message:{}", JSON.toJSON(mqMsg));
                    logger.error(throwable.getMessage(), throwable);
                }
            });

        } catch (Exception e) {
            logger.error("EmailTransitStockProducer|sendMessage|发送在途库存邮件事件消息异常", e);
        }

    }

    @PreDestroy
    public void stop() {
        if (producer != null) {
            producer.shutdown();
            logger.info("MQ：关闭生产者[EmailTransitStockProducer]");
        }
    }

}
