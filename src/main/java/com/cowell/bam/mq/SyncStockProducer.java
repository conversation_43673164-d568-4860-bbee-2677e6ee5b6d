package com.cowell.bam.mq;

import com.cowell.tools.utils.RocketMqHook;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.rocketmq.client.exception.MQClientException;
import org.apache.rocketmq.client.producer.DefaultMQProducer;
import org.apache.rocketmq.client.producer.SendCallback;
import org.apache.rocketmq.client.producer.SendResult;
import org.apache.rocketmq.common.message.Message;
import org.apache.rocketmq.remoting.common.RemotingHelper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.annotation.PreDestroy;
import java.util.Objects;

import static com.cowell.bam.util.CommonUtil.fillProducerInfo;

/**
 * DC库存对比差异后发送原始消息给stockcenter消费
 */
@Slf4j
@Component
public class SyncStockProducer {


    @Value("${apache.rocketmq.saasNamesrvAddr}")
    private String saasNamesrvAddr;

    @Value("${apache.rocketmq.sync_stock_producer.topic}")
    private String topic;

    @Value("${apache.rocketmq.sync_stock_producer.group}")
    private String groupName;

    @Value("${apache.rocketmq.sync_stock_producer.tag}")
    private String tag;


    private DefaultMQProducer producer;

    @Autowired
    private RocketMqHook rocketMqHook;

    /**
     * 初始化
     */
    @PostConstruct
    public void start() {
        try {
            log.info("SyncStockProducer|start|启动同步库存生产者....");
            producer = new DefaultMQProducer(groupName);
            producer.setNamesrvAddr(saasNamesrvAddr);
            fillProducerInfo(producer);
            producer.start();
            rocketMqHook.producer(producer);
        } catch (MQClientException e) {
            log.error("SyncStockProducer|start|启动同步库存生产者失败", e);
            throw new RuntimeException(e.getMessage(), e);
        }
    }

    public void sendMessage(String msg) {
        if (StringUtils.isBlank(msg)) {
            return;
        }

        try {
            byte[] messageBody = msg.getBytes(RemotingHelper.DEFAULT_CHARSET);


            Message mqMsg = new Message(topic, tag, String.valueOf(System.currentTimeMillis()), messageBody);
            log.info("SyncStockProducer|sendMessage|keys={}", mqMsg.getKeys());

            producer.send(mqMsg, new SendCallback() {
                @Override
                public void onSuccess(SendResult sendResult) {
                    log.info("SyncStockProducer|sendMessage|发送消息成功|sendResult={},keys={}", sendResult, mqMsg.getKeys());
                }

                @Override
                public void onException(Throwable throwable) {
                    log.error("SyncStockProducer|sendMessage|发送消息失败|keys={},data={}", mqMsg.getKeys(), msg, throwable);
                }
            });

        } catch (Exception e) {
            log.error("SyncStockProducer|sendMessage|生产者发送失败data:{}", msg, e);
        }

    }

    @PreDestroy
    public void stop() {
        if (Objects.nonNull(producer)) {
            producer.shutdown();
            log.info("SyncStockProducer|sendMessage|关闭生产者....");
        }
    }


}
