package com.cowell.bam.mq;

import com.alibaba.fastjson.JSON;
import com.cowell.bam.entity.ItemBaseChangeDTO;
import com.cowell.bam.service.IStoreSyncFeignService;
import com.cowell.bam.service.PriceCenterCompareService;
import com.cowell.bam.service.StockCompareService;
import com.cowell.bam.service.dto.CompareDataInfo;
import com.cowell.bam.service.dto.MdmDataTransformDTO;
import com.cowell.common.utils.SentinelUtils;
import com.cowell.tools.utils.RocketMqHook;
import com.google.common.collect.Lists;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.rocketmq.client.consumer.DefaultMQPushConsumer;
import org.apache.rocketmq.client.consumer.listener.ConsumeConcurrentlyContext;
import org.apache.rocketmq.client.consumer.listener.ConsumeConcurrentlyStatus;
import org.apache.rocketmq.client.consumer.listener.MessageListenerConcurrently;
import org.apache.rocketmq.client.exception.MQClientException;
import org.apache.rocketmq.common.consumer.ConsumeFromWhere;
import org.apache.rocketmq.common.message.MessageExt;
import org.apache.rocketmq.common.protocol.heartbeat.MessageModel;
import org.apache.rocketmq.remoting.common.RemotingHelper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.sleuth.annotation.NewSpan;
import org.springframework.stereotype.Component;

import javax.annotation.PreDestroy;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Objects;

/**
 * 商品创建同步库存价格
 * <AUTHOR>
 * @date 2023/9/21 11:27
 */
@Component
public class ItemBaseCreateSyncStockAndPriceConsumer implements MessageListenerConcurrently, ConsumerAdapter {
    private static final Logger logger = LoggerFactory.getLogger(ItemBaseCreateSyncStockAndPriceConsumer.class);

    private static final String LIMITER = "ItemBaseCreateSyncStockAndPriceConsumer";

    @Value("${apache.rocketmq.namesrvAddr04}")
    private String namesrvAddr;

    @Value("${apache.rocketmq.item_base_create_sync_stock_price.consumer-topic}")
    private String topic;
    @Value("${apache.rocketmq.item_base_create_sync_stock_price.consumer-group}")
    private String group;
    @Value("${apache.rocketmq.item_base_create_sync_stock_price.consumer-tag}")
    private String tag;

    private final DefaultMQPushConsumer consumer = new DefaultMQPushConsumer();

    @Autowired
    private ItemBaseCreateSyncStockAndPriceConsumer itemBaseCreateSyncStockAndPriceConsumer;

    @Autowired
    private RocketMqHook rocketMqHook;

    @Autowired
    private IStoreSyncFeignService storeFeignService;

    @Autowired
    private PriceCenterCompareService priceCenterCompareService;

    @Autowired
    private StockCompareService stockCompareService;

    /**
     * 初始化
     */
    @Override
    public void start() {
        try {
            logger.info("MQ：启动消费者[ItemBaseCreateSyncStockAndPriceConsumer]namesrvAddr:"+ namesrvAddr);
            consumer.setNamesrvAddr(namesrvAddr);
            consumer.setConsumerGroup(group+topic);
            consumer.setInstanceName(topic);
            consumer.setConsumeFromWhere(ConsumeFromWhere.CONSUME_FROM_LAST_OFFSET);
            // 集群消费模式
            consumer.setMessageModel(MessageModel.CLUSTERING);
            // 订阅主题
            consumer.subscribe(topic, tag);
            // 注册消息监听器
            consumer.registerMessageListener(itemBaseCreateSyncStockAndPriceConsumer);
            //单位分钟，默认15分钟发一版
            consumer.setConsumeTimeout(600);
            consumer.setPullThresholdForQueue(50);
            consumer.setPullThresholdForTopic(50);
            // 启动消费端
            consumer.start();
            rocketMqHook.consumer(consumer);
        } catch (MQClientException e) {
            logger.info("MQ：启动消费者[ItemBaseCreateSyncStockAndPriceConsumer]失败！异常: {}-{}", e.getResponseCode(), e.getErrorMessage());
            throw new RuntimeException(e.getMessage(), e);
        }

    }

    @Override
    @NewSpan
    public ConsumeConcurrentlyStatus consumeMessage(List<MessageExt> msgs, ConsumeConcurrentlyContext context) {

        try {
            logger.info("ItemBaseCreateSyncStockAndPriceConsumer|size:{}..", CollectionUtils.isNotEmpty(msgs)?msgs.size():0);
            for (int index = 0; index < msgs.size(); index++) {
                MessageExt msg = msgs.get(index);
                if (tag.equalsIgnoreCase(msg.getTags())) {
                    boolean limit = SentinelUtils.limitResource(LIMITER);
                    if (limit){
                        logger.warn("ItemBaseCreateSyncStockAndPriceConsumer|consumeMessage|限流,稍后重试!{}",msg.getKeys());
                        return ConsumeConcurrentlyStatus.RECONSUME_LATER;
                    }
                    String messageBody = new String(msg.getBody(), RemotingHelper.DEFAULT_CHARSET);
                    logger.info("ItemBaseCreateSyncStockAndPriceConsumer|consumeMessage|keys:{}.", msg.getKeys());
                    ItemBaseChangeDTO itemBaseChangeDTO = JSON.parseObject(messageBody, ItemBaseChangeDTO.class);

                    ItemBaseChangeDTO.ColumnsList columnsList = itemBaseChangeDTO.getAfterColumnsList();
                    if (Objects.isNull(columnsList)) {
                        columnsList = itemBaseChangeDTO.getBeforeColumnsList();
                    }
                    if (Objects.isNull(columnsList)) {
                        continue;
                    }

                    MdmDataTransformDTO mdmDataTransformDTO = new MdmDataTransformDTO();
                    mdmDataTransformDTO.setTransFormType(1);
                    mdmDataTransformDTO.setBusinessId(String.valueOf(columnsList.getBusiness_id()));
                    mdmDataTransformDTO.setStoreIds(Lists.newArrayList(columnsList.getStore_id()));
                    mdmDataTransformDTO.setDataType(2);
                    MdmDataTransformDTO mdmStoreInfo = storeFeignService.transformMdmData(mdmDataTransformDTO);
                    List<String> storeNos = mdmStoreInfo.getStoreNos();
                    if (CollectionUtils.isEmpty(storeNos)){
                        logger.warn("ItemBaseCreateSyncStockAndPriceConsumer|未能获取到busNo");
                        continue;
                    }
                    String busNo = mdmStoreInfo.getStoreNos().get(0);
                    String comId = mdmStoreInfo.getComId();
                    String goodsNo = columnsList.getItem_union_id().toString();

                    // 比对价格
                    try {
                        priceCenterCompareService.priceCenterCompareFromGoods(comId, busNo, Collections.singletonList(goodsNo));
                    } catch (Exception e) {
                        logger.error("比对价格失败，busNo:{}, comId:{}, goodsNo:{}", busNo, comId, goodsNo);
                    }

                    try {
                        List<CompareDataInfo> compareDataInfoList = new ArrayList<>();
                        CompareDataInfo compareDataInfo = new CompareDataInfo();
                        compareDataInfo.setRowNo(0);
                        compareDataInfo.setGoodsNo(goodsNo);
                        compareDataInfoList.add(compareDataInfo);

                        // 同步库存
                        stockCompareService.sendParam2HD(comId, busNo, compareDataInfoList);
                    } catch (Exception e) {
                        logger.error("比对库存失败，busNo:{}, comId:{}, goodsNo:{}", busNo, comId, goodsNo);
                    }
                }


            }
        } catch (Exception e) {
            logger.warn("ItemBaseCreateSyncStockAndPriceConsumer|consumeMessage|比对失败", e);
            return ConsumeConcurrentlyStatus.RECONSUME_LATER;
        }
        return ConsumeConcurrentlyStatus.CONSUME_SUCCESS;
    }



    @PreDestroy
    public void stop() {
        consumer.shutdown();
        logger.warn("MQ：ItemBaseCreateSyncStockAndPriceConsumer consumer!");
    }
}
