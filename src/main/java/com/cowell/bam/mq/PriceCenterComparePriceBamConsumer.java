package com.cowell.bam.mq;

import com.alibaba.fastjson.JSON;
import com.cowell.bam.enums.PriceComparisonEnum;
import com.cowell.bam.mq.message.CompareOneselfMessage;
import com.cowell.bam.service.PriceCenterCompareService;
import com.cowell.common.sentinel.utils.SentinelUtil;
import com.cowell.tools.utils.RocketMqHook;
import org.apache.commons.lang.StringUtils;
import org.apache.rocketmq.client.consumer.DefaultMQPushConsumer;
import org.apache.rocketmq.client.consumer.listener.ConsumeConcurrentlyContext;
import org.apache.rocketmq.client.consumer.listener.ConsumeConcurrentlyStatus;
import org.apache.rocketmq.client.consumer.listener.MessageListenerConcurrently;
import org.apache.rocketmq.client.exception.MQClientException;
import org.apache.rocketmq.common.consumer.ConsumeFromWhere;
import org.apache.rocketmq.common.message.MessageExt;
import org.apache.rocketmq.common.protocol.heartbeat.MessageModel;
import org.apache.rocketmq.remoting.common.RemotingHelper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.sleuth.annotation.NewSpan;
import org.springframework.core.task.AsyncTaskExecutor;
import org.springframework.stereotype.Component;

import javax.annotation.PreDestroy;
import java.util.List;
import java.util.Objects;

/**
 * 类说明
 *
 * @Author: liw
 * @Date: 2020-06-01 12:14
 */
@Component
public class PriceCenterComparePriceBamConsumer implements MessageListenerConcurrently, ConsumerAdapter {

    private static final Logger logger = LoggerFactory.getLogger(PriceCenterComparePriceBamConsumer.class);

    @Value("${apache.rocketmq.namesrvAddr}")
    private String namesrvAddr;

    @Value("${apache.rocketmq.compare_price_self.topic}")
    private String topic;
    @Value("${apache.rocketmq.compare_price_self.group}")
    private String group;
    @Value("${apache.rocketmq.compare_price_self.tag}")
    private String tag;

    @Value("${apache.rocketmq.compare_price_self.consumeThreadMax:10}")
    private int consumeThreadMax;

    @Value("${apache.rocketmq.compare_price_self.consumeThreadMin:10}")
    private int consumeThreadMin;

    @Autowired
    private PriceCenterComparePriceBamConsumer priceCenterComparePriceConsumer;
    @Autowired
    private PriceCenterCompareService priceCenterCompareService;

    private final DefaultMQPushConsumer consumer = new DefaultMQPushConsumer();

    private static final String instanceName = "PriceCenterComparePriceBamConsumer";

    @Autowired
    private RocketMqHook rocketMqHook;

    @Autowired
    @Qualifier("businessComparePriceExecutorTrace")
    private AsyncTaskExecutor businessComparePriceExecutor;

    /**
     * 初始化
     *
     * @throws MQClientException
     */
    @Override
    public void start() {
        try {
            logger.info("MQ：start  对比价格后任务拆解!");
            consumer.setConsumerGroup(group + "_TASK_SPLIT");
            consumer.setNamesrvAddr(namesrvAddr);
            consumer.setInstanceName(instanceName);
            consumer.setConsumeThreadMax(consumeThreadMax);
            consumer.setConsumeThreadMin(consumeThreadMin);
            consumer.setConsumeFromWhere(ConsumeFromWhere.CONSUME_FROM_LAST_OFFSET);
            // 集群消费模式
            consumer.setMessageModel(MessageModel.CLUSTERING);
            // 订阅主题
            consumer.subscribe(topic, tag + "_TASK_SPLIT");
            consumer.setConsumeTimeout(120);
            consumer.setPullThresholdForQueue(30);
            consumer.setPullThresholdForTopic(30);
            // 注册消息监听器
            consumer.registerMessageListener(priceCenterComparePriceConsumer);
            // 启动消费端
            consumer.start();
            rocketMqHook.consumer(consumer);
        } catch (MQClientException e) {
            logger.error("MQ：start  对比价格后任务拆解 is fail! error ：{}-{}", e.getResponseCode(), e.getErrorMessage());
            throw new RuntimeException(e.getMessage(), e);
        }
    }

    @Override
    @NewSpan
    public ConsumeConcurrentlyStatus consumeMessage(List<MessageExt> msgs, ConsumeConcurrentlyContext context) {
        logger.info(" MQ  对比价格后任务拆解 list: {}", msgs);

        try {

            if (SentinelUtil.limitResource(getLimitKey())) {
                return ConsumeConcurrentlyStatus.RECONSUME_LATER;
            }

            MessageExt msg = msgs.get(0);
            String messageBody = new String(msg.getBody(), RemotingHelper.DEFAULT_CHARSET);
            logger.info("对比价格后任务拆解 MQ msg: {}", messageBody);
            CompareOneselfMessage compareOneselfMessage = JSON.parseObject(messageBody, CompareOneselfMessage.class);
            logger.info("对比价格后任务拆解 CompareOneselfMessage: {}", compareOneselfMessage);
            if (null == compareOneselfMessage) {
                return ConsumeConcurrentlyStatus.CONSUME_SUCCESS;
            }

            PriceComparisonEnum priceComparisonEnum = PriceComparisonEnum.getByCode(compareOneselfMessage.getIsPriceComparison());
            switch (priceComparisonEnum) {
                case PUSH_PRICE_CENTER:
                    // 不对比价格直接推价格中台
                    priceCenterCompareService.priceCenterFromBusNo(compareOneselfMessage.getComId(), compareOneselfMessage.getBusNo());
                    break;
                case COMPARE_POS_PRICE:
                    if (StringUtils.isBlank(compareOneselfMessage.getBusNo()) || Objects.isNull(compareOneselfMessage.getStoreId())) {
                        businessComparePriceExecutor.execute(() -> priceCenterCompareService.exportByComId(compareOneselfMessage.getComId()));
                    } else {
                        priceCenterCompareService.posFromBusNo(compareOneselfMessage);
                    }
                    break;
                case BAM_COMPARE_PRICE_SIGN:
                    priceCenterCompareService.comparePriceSpecialFlag(compareOneselfMessage.getComId(), compareOneselfMessage.getBusNo());
                    break;
                case BAM_COMPARE_STORE_PRICE:
                    priceCenterCompareService.doCompareStorePrice(compareOneselfMessage.getMdmStoreBaseDTO(), compareOneselfMessage.isPriceRepair());
                    break;
                default:
                    priceCenterCompareService.priceCenterCompareFromBusNo(compareOneselfMessage.getComId(), compareOneselfMessage.getBusNo());
                    break;
            }
//            if (compareOneselfMessage.getIsPriceComparison().equals(PriceComparisonEnum.PUSH_PRICE_CENTER.getCode())) {
//                priceCenterCompareService.priceCenterFromBusNo(compareOneselfMessage.getComId(), compareOneselfMessage.getBusNo());
//            } else if (compareOneselfMessage.getIsPriceComparison().equals(PriceComparisonEnum.COMPARE_POS_PRICE.getCode())) {
//                if (StringUtils.isBlank(compareOneselfMessage.getBusNo()) || Objects.isNull(compareOneselfMessage.getStoreId())) {
//                    businessComparePriceExecutor.execute(() -> priceCenterCompareService.exportByComId(compareOneselfMessage.getComId()));
//                } else {
//                    priceCenterCompareService.posFromBusNo(compareOneselfMessage);
//                }
//            } else if (compareOneselfMessage.getIsPriceComparison().equals(PriceComparisonEnum.BAM_COMPARE_PRICE_SIGN.getCode())) {
//                priceCenterCompareService.comparePriceSpecialFlag(compareOneselfMessage.getComId(), compareOneselfMessage.getBusNo());
//            } else {
//                priceCenterCompareService.priceCenterCompareFromBusNo(compareOneselfMessage.getComId(), compareOneselfMessage.getBusNo());
//            }

        } catch (Exception e) {
            logger.error("对比价格后任务拆解 消费失败:", e);
        }

        return ConsumeConcurrentlyStatus.CONSUME_SUCCESS;

    }

    private String getLimitKey() {
        return topic + "/" + group;
    }

    @PreDestroy
    public void stop() {
        consumer.shutdown();
        logger.error("MQ：stop  对比价格后任务拆解 !");
    }


}
