package com.cowell.bam.mq;

import com.alibaba.fastjson.JSONObject;
import com.cowell.bam.common.TracerBean;
import com.cowell.bam.domain.BamLogDO;
import com.cowell.bam.service.BamBusinessBillLogService;
import com.cowell.bam.service.EsBdpDataService;
import com.cowell.bam.service.dto.MessageDto;
import com.cowell.tools.utils.RocketMqHook;
import com.google.common.collect.Lists;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.rocketmq.client.consumer.DefaultMQPushConsumer;
import org.apache.rocketmq.client.consumer.listener.ConsumeConcurrentlyContext;
import org.apache.rocketmq.client.consumer.listener.ConsumeConcurrentlyStatus;
import org.apache.rocketmq.client.consumer.listener.MessageListenerConcurrently;
import org.apache.rocketmq.client.exception.MQClientException;
import org.apache.rocketmq.common.consumer.ConsumeFromWhere;
import org.apache.rocketmq.common.message.MessageExt;
import org.apache.rocketmq.common.protocol.heartbeat.MessageModel;
import org.apache.rocketmq.remoting.common.RemotingHelper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.sleuth.Span;
import org.springframework.cloud.sleuth.annotation.NewSpan;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.PreDestroy;
import javax.annotation.Resource;
import java.io.UnsupportedEncodingException;
import java.util.List;
@Component
public class MqConsumer implements MessageListenerConcurrently, ConsumerAdapter {

    private static final Logger logger = LoggerFactory.getLogger(MqConsumer.class);

    @Resource
    private TracerBean tracerBean;

    private DefaultMQPushConsumer consumer = null;

    private static final String BAM_LOG_GROUP = "bam_log_group";

    @Value("${apache.rocketmq.namesrvAddr}")
    private String namesrvAddr;

    @Value("${apache.rocketmq.bam.topic}")
    private String bamLogTopic;

    @Value("${data_source_env}")
    private String dataSourceEnv;
    @Autowired
    private EsBdpDataService esBdpDataService;
    @Autowired
    private BamBusinessBillLogService bamBusinessBillLogService;

    @Autowired
    private RocketMqHook rocketMqHook;

    @Override
    public void start() {
        try {
            String groupName = BAM_LOG_GROUP;
            if ("STAGE".equalsIgnoreCase(dataSourceEnv)) {
                groupName = "bam_stage_log_group";
            }
            consumer = new DefaultMQPushConsumer(groupName);

            logger.info("MQ：starting bam_log ......");
            consumer.setNamesrvAddr(namesrvAddr);
            consumer.setConsumeFromWhere(ConsumeFromWhere.CONSUME_FROM_LAST_OFFSET);
            consumer.setMessageModel(MessageModel.CLUSTERING);
            consumer.subscribe(bamLogTopic, "*");
            consumer.registerMessageListener(this);
            consumer.setMaxReconsumeTimes(3);
            consumer.setConsumeThreadMax(2);
            consumer.setConsumeThreadMin(1);
            consumer.setConsumeTimeout(5);
            consumer.setPullThresholdForQueue(20);
            consumer.setPullThresholdForTopic(20);
            consumer.setInstanceName("bam_log_consumer");
            consumer.start();
            rocketMqHook.consumer(consumer);
            logger.info("MQ：starting bam_log   is success ! group:{},topic:{},tag:{}", BAM_LOG_GROUP, bamLogTopic, "*");
        } catch (MQClientException e) {
            logger.error("MQ：starting bam_log is fail", e.getResponseCode(), e.getErrorMessage());
            throw new RuntimeException(e.getMessage(), e);
        }
    }

    @PreDestroy
    public void stop() {
        if (consumer != null) {
            consumer.shutdown();
            logger.info("MQ：stop bam_log success! ");
        }
    }


    @Override
    @NewSpan
    public ConsumeConcurrentlyStatus consumeMessage(List<MessageExt> msgs, ConsumeConcurrentlyContext context) {
        Span span = tracerBean.startSpan();
        try {
            for (int index = 0; index < msgs.size(); index++) {
                String messageBody = "";
                MessageExt msg = msgs.get(index);
                try {
                    messageBody = new String(msg.getBody(), RemotingHelper.DEFAULT_CHARSET);
                } catch (UnsupportedEncodingException e) {
                    logger.warn("bam消息错误", e);
                }
                if (StringUtils.isBlank(messageBody)) {
                    continue;
                }
                //logger.info("MQ：bam_log consumer msg is : body is:{}", messageBody);
                MessageDto messageDto = JSONObject.parseObject(messageBody, MessageDto.class);
                List<BamLogDO> bamLogDOList = null;
                if (messageDto != null) {
                    bamLogDOList = JSONObject.parseArray(messageDto.getObject().toString(), BamLogDO.class);
                }

                if (CollectionUtils.isNotEmpty(bamLogDOList)) {
                    logger.info("MQ：bam_log consumer msg size:{}",bamLogDOList.size());
                    setHanaBdpData(bamLogDOList);
                }
                logger.info("MQ：  bam_log consumer msg success:{}", messageBody);
            }
        } catch (Exception e) {
            logger.error("bam_log consumer msg error,", e);
            return ConsumeConcurrentlyStatus.RECONSUME_LATER;
        } finally {
            tracerBean.close(span);
        }
        return ConsumeConcurrentlyStatus.CONSUME_SUCCESS;
    }

    @Transactional(rollbackFor = Exception.class)
    public void setHanaBdpData(List<BamLogDO> bamLogDOList) {
        List<List<BamLogDO>> sublist = Lists.partition(bamLogDOList, 200);
        for (List<BamLogDO> sub : sublist) {
            //保存数据库
            bamBusinessBillLogService.batchInsert(sub);
            //保存ES数据
            esBdpDataService.batchSave(sub);
        }
    }
}
