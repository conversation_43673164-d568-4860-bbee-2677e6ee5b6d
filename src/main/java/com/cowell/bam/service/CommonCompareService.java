package com.cowell.bam.service;

import com.alibaba.fastjson.JSON;
import com.cowell.bam.mq.CompareDataFixProducer;
import com.cowell.bam.mq.message.ComparisonModelConfig;
import com.cowell.bam.service.dto.CompareResultNotifyDTO;
import com.cowell.bam.service.dto.base.AmisListResponse;
import com.ctrip.framework.apollo.spring.annotation.ApolloJsonValue;
import org.apache.commons.collections4.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 比对平台
 * <AUTHOR>
 * @date 2022/12/19 10:45
 */
@Service
public class CommonCompareService {

    private static final Logger log = LoggerFactory.getLogger(CommonCompareService.class);

    @Autowired
    @Qualifier("vanillaRestTemplate")
    private RestTemplate restTemplate;

    @Autowired
    private INyuwaFeign nyuwaFeign;

    @ApolloJsonValue("${compareResultNotifyMqTopicConfig:{}}")
    private Map<String, String> compareResultNotifyMqTopicConfig;

    @Autowired
    private CompareDataFixProducer compareDataFixProducer;

    @Value("${compareDataFixMqTopicTemp:}")
    private String compareDataFixMqTopicTemp;

    @Value("${compareDataFixMqTagTemp:}")
    private String compareDataFixMqTagTemp;

    @Value("${queryCompareResultPageSize:100}")
    private int queryCompareResultPageSize;

    /**
     * 处理比对通知
     * @param config ComparisonModelConfig
     */
    public void compareNotify(ComparisonModelConfig config) {
        log.info("CommonCompareService|compareNotify,config:{}", JSON.toJSONString(config));
        String appId = config.getApp_id().toString();
        String configId = config.getId().toString();
        String modelName = config.getComparison_result_name();
        String mqConfig = compareResultNotifyMqTopicConfig.getOrDefault(configId, configId);
        if (mqConfig.equals(configId)) {
            return;
        }
        String topic = String.format(compareDataFixMqTopicTemp, mqConfig);
        String tag = String.format(compareDataFixMqTagTemp, mqConfig);

        int page = 1, pageSize = queryCompareResultPageSize;
        List<Object> rows;
        do {
            AmisListResponse<Object> compareResult = queryCompareResult(config, appId, modelName, page, pageSize, 3);
            if (Objects.isNull(compareResult)) {
                break;
            }
            rows = compareResult.getRows();
            if (CollectionUtils.isEmpty(rows)) {
                break;
            }
            List<String> stringList = rows.stream().map(d -> {
                CompareResultNotifyDTO dto = new CompareResultNotifyDTO();
                dto.setAppId(appId);
                dto.setComparisonModelConfigurationId(configId);
                dto.setMsg(JSON.toJSONString(d));
                return JSON.toJSONString(dto);
            }).collect(Collectors.toList());
            compareDataFixProducer.sendBatchMessage(topic, tag, stringList);
            log.info("CommonCompareService|compareNotify,sendBatchMessage success, page:{}", page);
            page++;
        } while (CollectionUtils.isNotEmpty(rows));
    }

    private AmisListResponse<Object> queryCompareResult(ComparisonModelConfig config, String appId, String modelName, int page, int pageSize, int retryTimes) {
        AmisListResponse<Object> compareResult;
        if (retryTimes < 0) {
            return null;
        }
        try {
            compareResult = nyuwaFeign.queryCompareResult(appId, modelName, page, pageSize, config.getBatchNo(), 2).getData();
        } catch (Exception e) {
            log.error("CommonCompareService|queryCompareResult error, page:{},config:{}", page,  JSON.toJSONString(config));
            retryTimes--;
            compareResult = queryCompareResult(config, appId, modelName, page, pageSize, retryTimes - 1);
        }
        return compareResult;
    }

//    private AmisListResponse<Object> getCompareResult(String appId, String modelName, String queryParam) {
//        String url = listUrlTemp.replace("{appId}", appId).replace("{modelName}", modelName);
//        if (StringUtils.isNotBlank(queryParam)) {
//            url = url + "?" + queryParam;
//        }
//        ParameterizedTypeReference<AmisCommonResponse<AmisListResponse<Object>>> typeRef = new ParameterizedTypeReference<AmisCommonResponse<AmisListResponse<Object>>>() {
//        };
//
//        HttpHeaders headers = new HttpHeaders();
//        headers.add("Content-Type", "application/json;charset=UTF-8");
//        HttpEntity<Object> requestEntity = new HttpEntity<>(headers);
//        ResponseEntity<AmisCommonResponse<AmisListResponse<Object>>> responseEntity = null;
//
//        try {
//            responseEntity = restTemplate.exchange(url, HttpMethod.GET, requestEntity, typeRef);
//        } catch (RestClientException e) {
//            log.error("获取比对结果失败，URL:{}, e:{}", url, e);
//        }
//
//        if (Objects.isNull(responseEntity)) {
//            return null;
//        }
//
//        return responseEntity.getBody().getData();
//    }
}
