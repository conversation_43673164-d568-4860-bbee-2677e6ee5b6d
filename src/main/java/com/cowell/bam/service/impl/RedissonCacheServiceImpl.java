package com.cowell.bam.service.impl;

import com.alibaba.fastjson.JSON;
import com.cowell.bam.service.IRedissonCacheService;
import com.cowell.bam.web.rest.util.ConstantPool;
import com.cowell.bam.web.rest.util.RedisKeysConstant;
import org.joda.time.LocalDate;
import org.redisson.api.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.TimeUnit;

/**
 * redis 缓存服务接口
 */
@Component
public class RedissonCacheServiceImpl implements IRedissonCacheService {

    private static final Logger LOG = LoggerFactory.getLogger(RedissonCacheServiceImpl.class);

    @Autowired
    private RedissonClient redissonClient;

    /**
     * set操作
     *
     * @param key
     * @param obj
     * @return
     */
    @Override
    public boolean set(String key, Object obj) {
        try {
            RBucket<Object> bucket = redissonClient.getBucket(key);
            bucket.set(JSON.toJSONString(obj));
            return true;
        } catch (Exception e) {
            LOG.error("<=======set redis Exception,key:{}", key, e);
        }
        return false;
    }

    @Override
    public boolean setString(String key, String value) {
        try {
            RBucket<Object> bucket = redissonClient.getBucket(key);
            bucket.set(value);
            return true;
        } catch (Exception e) {
            LOG.error("<=======set redis Exception,key:{}", key, e);
        }
        return false;
    }

    @Override
    public boolean setStringExpire(String key, String value, Long seconds) {
        try {
            RBucket<Object> bucket = redissonClient.getBucket(key);
            bucket.set(value, seconds, TimeUnit.SECONDS);
            return true;
        } catch (Exception e) {
            LOG.error("<=======set redis Exception,key:{}", key, e);
        }
        return false;
    }

    /**
     * set操作 有效时间
     *
     * @param key
     * @param obj
     * @return
     */
    @Override
    public boolean setNx(String key, Object obj, long expire) {
        try {
            RBucket<Object> bucket = redissonClient.getBucket(key);
            boolean result = bucket.trySet(JSON.toJSONString(obj));
            if (result) {
                bucket.expire(expire, TimeUnit.SECONDS);
            }
            return result;
        } catch (Exception e) {
            LOG.error("<=======setNx redis Exception,key:{}", key, e);
        }
        return false;
    }

    /**
     * 根据key获取对象实体
     *
     * @param key
     * @return
     */
    @Override
    public String get(String key) {
        try {
            RBucket<String> bucket = redissonClient.getBucket(key);
            String strObj = bucket.get();
            return strObj;
        } catch (Exception e) {
            LOG.error("<=======get redis Exception,key:{}", key, e);
        }
        return null;
    }

    @Override
    public boolean setNxString(String key, Object obj, long expire) {
        try {
            RBucket<Object> bucket = redissonClient.getBucket(key);
            boolean result = bucket.trySet(obj);
            if (result) {
                bucket.expire(expire, TimeUnit.SECONDS);
            }
            return result;
        } catch (Exception e) {
            LOG.error("<=======setNx redis Exception,key:{}", key, e);
        }
        return false;
    }


    /**
     * 根据key获取对象实体
     *
     * @param key
     * @return
     */
    @Override
    public <T> Map<String, T> getMap(String key, Class<T> t) {
        try {
            RMap<String, T> map = redissonClient.getMap(key);
            return map;
        } catch (Exception e) {
            LOG.error("=====>[reids] error", e);
            //redis发生错误时，删除此key
            this.deleteByKey(key);
        }
        return null;
    }

    /**
     * 根据key获取对象实体
     *
     * @param key
     * @return
     */
    @Override
    public <T> void setMap(String key, Map<String, T> paramMap) {
        try {
            try {
                this.deleteByKey(key);
            } catch (Exception e) {
                LOG.error("=====>[reids] setMap error", e);
                //redis发生错误时，删除此key
                this.deleteByKey(key);
            }
            RMap<String, T> map = redissonClient.getMap(key);
            map.putAll(paramMap);
        } catch (Exception e) {
            LOG.error("=====>[reids] setMap error", e);
            //redis发生错误时，删除此key
            this.deleteByKey(key);
        }
    }

    /**
     * 根据key获取对象实体
     *
     * @param key
     * @return
     */
    @Override
    public <T> List<T> getList(String key, Class<T> t) {
        try {
            RList<T> list = redissonClient.getList(key);
            return list;
        } catch (Exception e) {
            LOG.error("=====>[reids] getList error", e);
            //redis发生错误时，删除此key
            this.deleteByKey(key);
        }
        return null;
    }

    @Override
    public List<String> getListString(String key) {
        RList<String> list = redissonClient.getList(key);
        return list.readAll();
    }

    @Override
    public <T> boolean setList(String key, List<T> list) {
        try {
            try {
                this.deleteByKey(key);
            } catch (Exception e) {
                LOG.error("=====>[reids] deleteByKey error", e);
                //redis发生错误时，删除此key
                this.deleteByKey(key);
            }
            RList<T> setList = redissonClient.getList(key);
            setList.addAll(list);
        } catch (Exception e) {
            LOG.error("=====>[reids] setList error", e);
            //redis发生错误时，删除此key
            this.deleteByKey(key);
            return false;
        }
        return true;
    }

    /**
     * 根据key删除
     *
     * @param key
     * @return
     */
    @Override
    public boolean deleteByKey(String... key) {
        try {
            long num = redissonClient.getKeys().delete(key);
            if (num > 0) {
                return true;
            }
        } catch (Exception e) {
            LOG.error("=====>[reids] deleteByKey error", e);
        }
        return false;
    }

    /**
     * 根据正则key删除
     *
     * @param pattern
     * @return
     */
    @Override
    public boolean deleteByPattern(String pattern) {
        try {
            long num = redissonClient.getKeys().deleteByPattern(pattern);
            if (num > 0) {
                return true;
            }
        } catch (Exception e) {
            LOG.error("=====>[reids] deleteByPattern error", e);
        }
        return false;
    }


    //超时时间 2分钟
    public static final long TIMEOUT_MILLIS = 120000L;

    //最大重试次数
    public static final int RETRY_TIMES = Integer.MAX_VALUE;

    //默认重试次数
    public static final int DEFAULT_RETRY_TIMES = 5;

    //重试间隔时间
    public static final long SLEEP_MILLIS = 500L;


    /**
     * 根据key枷锁
     *
     * @param key
     * @return
     */
    @Override
    public RLock getLock(String key) {
        return getLock(key, TIMEOUT_MILLIS, RETRY_TIMES, SLEEP_MILLIS);
    }

    /**
     * @param key
     * @param retryTimes 重试次数
     * @return
     */
    @Override
    public RLock getLock(String key, int retryTimes) {
        return getLock(key, TIMEOUT_MILLIS, retryTimes, SLEEP_MILLIS);
    }

    /**
     * @param key
     * @param retryTimes  重试次数
     * @param sleepMillis 时间间隔
     * @return
     */
    @Override
    public RLock getLock(String key, int retryTimes, long sleepMillis) {
        return getLock(key, TIMEOUT_MILLIS, retryTimes, sleepMillis);
    }

    /**
     * @param key
     * @param expire 到期时间
     * @return
     */
    @Override
    public RLock getLock(String key, long expire) {
        return getLock(key, expire, RETRY_TIMES, SLEEP_MILLIS);
    }

    /**
     * @param key
     * @param expire     到期时间
     * @param retryTimes 重试次数
     * @return
     */
    @Override
    public RLock getLock(String key, long expire, int retryTimes) {
        return getLock(key, expire, retryTimes, SLEEP_MILLIS);
    }


    /**
     * 自增key后获取
     *
     * @param key
     * @return
     */
    @Override
    public Long incr(String key) {
        RAtomicLong v = redissonClient.getAtomicLong(key);
        return v.incrementAndGet();
    }

    /**
     * 通过类型获取key的value
     *
     * @param key
     * @param t
     * @param <T>
     * @return
     */
    @Override
    public <T> T typeGet(String key, Class<T> t) {
        RBucket<T> bucket = redissonClient.getBucket(key);
        return bucket.get();
    }

    /**
     * 获取后自增
     *
     * @param key
     * @return
     */
    @Override
    public Long getAndIncr(String key) {
        RAtomicLong v = redissonClient.getAtomicLong(key);
        return v.getAndIncrement();
    }

    /**
     * 向set里新增
     *
     * @param key
     * @param value
     * @return
     */
    public boolean sadd(String key, String value) {
        RSet<String> set = redissonClient.getSet(key);
        return set.add(value);
    }

    /**
     * 获取set
     *
     * @param key
     * @return
     */
    @Override
    public Set<String> getSet(String key) {
        RSet<String> set = redissonClient.getSet(key);
        return set.readAll();
    }

    @Override
    public String spop(String key) {
        RSet<String> set = redissonClient.getSet(key);
        return set.removeRandom();
    }

    @Override
    public int scard(String key) {
        RSet<String> set = redissonClient.getSet(key);
        return set.size();
    }

    @Override
    public void lpush(String key, String value) {
        RDeque<String> deque = redissonClient.getDeque(key);
        deque.addFirst(value);
    }

    @Override
    public String rpop(String key) {
        RDeque<String> deque = redissonClient.getDeque(key);
        return deque.getLast();
    }

    @Override
    public Long incrAndExpire(String key, long time) {
        RAtomicLong v = redissonClient.getAtomicLong(key);
        v.expire(time, TimeUnit.MILLISECONDS);
        return v.incrementAndGet();
    }

    /**
     * @param key
     * @param expire      持锁时间,单位毫秒
     * @param retryTimes  重试次数
     * @param sleepMillis 重试的间隔时间
     * @return
     */
    @Override
    public RLock getLock(String key, long expire, int retryTimes, long sleepMillis) {
        boolean result = false;
        RLock lock = null;
        try {
            lock = redissonClient.getLock(key);
            result = lock.tryLock(TIMEOUT_MILLIS, TimeUnit.MILLISECONDS);
            if (!result) {
                lock = null;
            }
        } catch (Exception e) {
            LOG.error("<=======getLock redis Exception,key:{}", key, e);
        }
        return lock;
    }


    /**
     * 解锁
     *
     * @param lock
     */
    @Override
    public void unLock(RLock lock) {
        if (null != lock && lock.isHeldByCurrentThread()) {
            lock.unlock();
            LOG.info("<== == =====release lock success ");
        }
    }

    @Override
    public Integer versionIncr(Integer dataType, String date){
        StringBuilder key = new StringBuilder();
        key.append(RedisKeysConstant.COMPARE_VERSION_KEY);
        key.append(dataType);
        key.append("-");
        key.append(date);
        LOG.info("versionIncr|key:{}.", key);
        RAtomicLong rAtomicLong = redissonClient.getAtomicLong(key.toString());
        long maxNo = rAtomicLong.incrementAndGet();
        rAtomicLong.expire(1, TimeUnit.DAYS);
        LOG.info("versionIncr|maxNo:{}.", maxNo);
        String maxNoStr = String.format("%01d", maxNo);
        LOG.info("versionIncr|maxNoStr:{}.", maxNoStr);
        return Integer.parseInt(maxNoStr);
    }
}
