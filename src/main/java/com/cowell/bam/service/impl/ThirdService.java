package com.cowell.bam.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.cowell.bam.enums.OperationTypeEnum;
import com.cowell.bam.service.*;
import com.cowell.bam.service.dto.*;
import com.cowell.bam.web.rest.util.ConstantPool;
import com.cowell.bam.web.rest.util.DateUtil;
import com.cowell.bam.web.rest.util.RedisKeysConstant;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import lombok.Getter;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.joda.time.LocalDate;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.task.AsyncTaskExecutor;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;

@Component
@Getter
public class ThirdService {
    private final Logger log = LoggerFactory.getLogger(ThirdService.class);

    @Autowired
    private IStoreSyncFeignService storeSyncFeignService;

    @Autowired
    private IStoreFeignService storeFeignService;

    @Autowired
    private IRedissonCacheService redissonCacheService;


    @Value("${dbMapConfig}")
    private String dbMapConfigStr;

    @Autowired
    @Qualifier("taskExecutor")
    private AsyncTaskExecutor executor;

    @Autowired
    private INyuwaErpFeign nyuwaErpFeign;

    private static final String STORE_APPLY_DATE_COLUMNS = "store_code,store_status,apply_date";
    private static final String STORE_APPLY_DATE_STORE_STATUS = "营业";

    public Long getBussiness(String compId) {
        log.info("ThirdService.getBussiness compId:【{}】", compId);
        String businessIdKey = ConstantPool.STOCK_COMPID_MDM_KEY + compId;
        String result = redissonCacheService.get(businessIdKey);
        //String result = null;
        Long businessId = null;
        if (StringUtils.isEmpty(result)) {
            businessId = transferMdmComIdToBusinessId(compId);
            if (businessId != null) {
                final String id = businessId.toString();
                executor.execute(() -> redissonCacheService.setNxString(businessIdKey, id, 60 * 60 * 24L));
            }
        } else {
            businessId = Long.valueOf(result);
        }
        log.info("ThirdService.getBussiness-结果-businessId:【{}】", businessId);
        return businessId;
    }

    /**
     * 根据mdm企业编码获取连锁id
     *
     * @param comId
     * @return
     */
    public Long transferMdmComIdToBusinessId(String comId) {
        MdmDataTransformDTO dataTransformDTO = new MdmDataTransformDTO();
        dataTransformDTO.setComId(comId);
        dataTransformDTO.setDataType(1);
        dataTransformDTO.setTransFormType(2);
        log.info("调用store comId 转换 连锁id，传入参数=={}", dataTransformDTO);
        MdmDataTransformDTO mdmDataTransformDTO = storeSyncFeignService.transformMdmData(dataTransformDTO);
        log.info("调用store comId 转换 连锁id，返回参数=={}", mdmDataTransformDTO);
        if (mdmDataTransformDTO == null || Objects.isNull(mdmDataTransformDTO.getBusinessId())) {
            return null;
        }
        return Long.valueOf(mdmDataTransformDTO.getBusinessId());
    }
    public Long getStore(String retailCode, String compId) {
        String retailCodeKey = ConstantPool.STOCK_STORE_MDM_KEY + compId + "_" + retailCode;
        Long storeId = null;
        String result = redissonCacheService.get(retailCodeKey);
        if (StringUtils.isEmpty(result)) {
            List<String> storeNos = new ArrayList<>();
            storeNos.add(retailCode);
            storeId = queryMdmStoreId(compId, storeNos);
            if (storeId != null) {
                String finalStoreId = String.valueOf(storeId);
                executor.execute(() -> redissonCacheService.setNxString(retailCodeKey, finalStoreId, 60 * 60 * 24L));
            }
        } else {
            storeId = Long.valueOf(result);
        }
        return storeId;
    }

    public Long queryMdmStoreId(String compId, List<String> storeNos) {
        log.debug("ThirdService.queryMdmStoreId start:{},{}", compId, storeNos);
        Long storeId = null;
        MdmDataTransformDTO mdmDataTransformDTO = new MdmDataTransformDTO();
        mdmDataTransformDTO.setComId(compId);
        mdmDataTransformDTO.setDataType(2);
        mdmDataTransformDTO.setTransFormType(2);
        mdmDataTransformDTO.setStoreNos(storeNos);
        if (mdmDataTransformDTO != null) {
            MdmDataTransformDTO dto = storeSyncFeignService.transformMdmData(mdmDataTransformDTO);
            if (dto != null && CollectionUtils.isNotEmpty(dto.getStoreIds())) {
                storeId = dto.getStoreIds().get(0);
            }
        }
        log.debug("ThirdService.queryMdmStoreId end:{}", mdmDataTransformDTO);
        return storeId;
    }

    /**
     * 查询门店和连锁一起的
     * @param compId
     * @param busNo
     * @return
     */
    public String getBusinessIdAndStoreId(String compId,String busNo) {
        String retailCodeKey = ConstantPool.STOCK_STORE_MDM_KEY + compId + "_" + busNo + "_BusinessIdAndStoreId";
        String businessIdAndStoreId = "";
        String result = redissonCacheService.get(retailCodeKey);
        if (StringUtils.isEmpty(result)) {
            List<String> storeNos = new ArrayList<>();
            storeNos.add(busNo);
            businessIdAndStoreId = queryMdmBusinessIdAndStoreId(compId, storeNos);
            if (businessIdAndStoreId != null) {
                String finalBusinessIdAndStoreId = businessIdAndStoreId;
                executor.execute(() -> redissonCacheService.setNxString(retailCodeKey, finalBusinessIdAndStoreId, 60 * 60));
            }
        } else {
            businessIdAndStoreId = result;
        }
        return businessIdAndStoreId;
    }

    public String queryMdmBusinessIdAndStoreId(String compId, List<String> storeNos) {
        log.debug("ThirdService.queryMdmStoreIdAndBusinessId start:{},{}", compId, storeNos);
        Long storeId = null;
        String businessId = "";
        MdmDataTransformDTO mdmDataTransformDTO = new MdmDataTransformDTO();
        mdmDataTransformDTO.setComId(compId);
        mdmDataTransformDTO.setDataType(2);
        mdmDataTransformDTO.setTransFormType(2);
        mdmDataTransformDTO.setStoreNos(storeNos);
        MdmDataTransformDTO dto = storeSyncFeignService.transformMdmData(mdmDataTransformDTO);
        if (dto != null && CollectionUtils.isNotEmpty(dto.getStoreIds())) {
            businessId = dto.getBusinessId();
            storeId = dto.getStoreIds().get(0);
        }
        log.debug("ThirdService.queryMdmStoreIdAndBusinessId end:{}", mdmDataTransformDTO);
        return businessId+":"+storeId;
    }


    /**
     * 根据连锁id查询门店ids
     *
     * @param businessId
     * @return mdm
     */
    public List<Long> getStoreIdsByBusinessId(Long businessId) {
        log.debug("ThirdService.getStoreIdsByBusinessId:{}", businessId);
        List<Long> storeIds = null;

        ResponseEntity<List<Long>> responseEntity = storeSyncFeignService.getStoreIdsByBusinessId(businessId);
        if (responseEntity != null && responseEntity.getStatusCode().equals(HttpStatus.OK)) {
            storeIds = responseEntity.getBody();
        }
        log.debug("ThirdService|getStoreIdsByBusinessId|response:{}", storeIds);
        return storeIds;
    }

    /**
     * 根据连锁id查询mdm门店映射关系
     *
     * @param businessId
     * @return mdm
     */
    public List<MdmStoreBaseDTO> findAllStoreByBusinessId(Long businessId) {
        log.debug("ThirdService.getStoreIdsByBusinessId:{}", businessId);
        List<Long> storeIds = null;

        ResponseEntity<List<MdmStoreBaseDTO>> responseEntity = storeSyncFeignService.findAllStoreByBusinessId(businessId);
        List<MdmStoreBaseDTO> mdmStoreBaseDTOS = null;
        if (responseEntity != null && responseEntity.getStatusCode().equals(HttpStatus.OK)) {
            mdmStoreBaseDTOS = responseEntity.getBody();
        }
        log.debug("ThirdService|getStoreIdsByBusinessId|response:{}", storeIds);
        return mdmStoreBaseDTOS;
    }


    /**
     * 通过id查门店信息
     *
     * @param storeId
     * @return
     */
    public CrmStoreDTO getCrmStoreByStoreId(Long storeId) {
        if (Objects.isNull(storeId)) {
            return null;
        }
        String retailCodeKey = RedisKeysConstant.CRM_STORE_KEY + storeId;
        String result = redissonCacheService.get(retailCodeKey);
        if (StringUtils.isNotEmpty(result)) {
            return JSONObject.parseObject(result, CrmStoreDTO.class);
        }
        CrmStoreDTO crmStore = null;
        ResponseEntity<CrmStoreDTO> responseEntity = storeSyncFeignService.getCrmStore(storeId);
        log.info("ThirdService|getCrmStoreByStoreId|responseEntity:【{}】", responseEntity);
        if (responseEntity != null && responseEntity.getBody() != null) {
            crmStore = responseEntity.getBody();
            if (Objects.nonNull(crmStore)) {
                CrmStoreDTO finalCrmStoreDTO = crmStore;
                executor.execute(() -> redissonCacheService.setNxString(retailCodeKey, JSON.toJSONString(finalCrmStoreDTO), 60 * 60 * 24L));
            }
        }
        return crmStore;
    }


    public Boolean isB2cWmsSendStock(Long storeId) {
        CrmStoreDTO crmStoreDTO = getCrmStoreByStoreId(storeId);
        log.info("ThirdService|isB2cWmsSendStock|crmStoreDTO:【{}】", crmStoreDTO);
        if (Objects.isNull(crmStoreDTO)) {
            return false;
        }
        String operationModel = crmStoreDTO.getOperationModel();
        if (StringUtils.isEmpty(operationModel)) {
            return false;
        }
        log.info("ThirdService|isB2cWmsSendStock|crmStoreDTO:【{}】", operationModel);
        if(checkBitValue(Integer.parseInt(operationModel), OperationTypeEnum.B2C_WAREHOUSE_SEND_GOODS.getBit())){
            log.info("ThirdService|isB2cWmsSendStock|b2c大仓配送库存:【{}】", operationModel);
            return true;
        }else {
            return false;
        }
    }

    public Boolean isWarehouse(Long storeId) {
        CrmStoreDTO crmStoreDTO = getCrmStoreByStoreId(storeId);
        log.info("ThirdService|isB2cWmsSendStock|crmStoreDTO:【{}】", crmStoreDTO);
        if (Objects.isNull(crmStoreDTO)) {
            return false;
        }
        String operationModel = crmStoreDTO.getOperationModel();
        if (StringUtils.isEmpty(operationModel)) {
            return false;
        }
        log.info("ThirdService|isWareHouse|crmStoreDTO:【{}】", operationModel);
        if(checkBitValue(Integer.parseInt(operationModel), OperationTypeEnum.WAREHOUSE.getBit())){
            log.info("ThirdService|isWareHouse|是否大仓:【{}】", operationModel);
            return true;
        }else {
            return false;
        }
    }

    private boolean checkBitValue(int source, int pos) {
        log.info("ThirdService|checkBitValue|source:{},pos:{}", source,pos);
        String str = ((source&(1<<(pos-1)))>>(pos-1))==0?"0":"1";
        return str.equals("1")?true:false;
    }

    public String getComId(Long businessId) {
        MdmDataTransformDTO dataTransformDTO = new MdmDataTransformDTO();
        dataTransformDTO.setBusinessId(businessId+"");
        dataTransformDTO.setDataType(1);
        dataTransformDTO.setTransFormType(1);
        log.info("ThirdService|getMdmByBusinessId|调用转换连锁id，传入参数=={}", dataTransformDTO);
        MdmDataTransformDTO dto = storeSyncFeignService.transformMdmData(dataTransformDTO);
        log.info("调用store查询连锁mdm：【{}】", dto);
        if (dto == null) {
            return null;
        }
        return dto.getComId();
    }

    public String transferBusinessIdAndStoreIdToMdmStoreNo(Long businessId, Long storeId) {
        MdmDataTransformDTO mdmDataTransformDTO = getMdmDataTransformDTO(businessId, storeId);
        if (mdmDataTransformDTO == null) {
            return null;
        }
        List<String> storeNos = mdmDataTransformDTO.getStoreNos();
        if (CollectionUtils.isNotEmpty(storeNos)) {
            return storeNos.get(0);
        }
        return null;
    }

    public MdmDataTransformDTO getMdmDataTransformDTO(Long businessId, Long storeId) {
        MdmDataTransformDTO dataTransformDTO = new MdmDataTransformDTO();
        dataTransformDTO.setBusinessId(businessId+"");
        List<Long> storeIdList = new ArrayList<>();
        storeIdList.add(storeId);
        dataTransformDTO.setStoreIds(storeIdList);
        dataTransformDTO.setDataType(2);
        dataTransformDTO.setTransFormType(1);
        log.info("调用store comId 转换 连锁id，传入参数=={}", dataTransformDTO);
        return storeSyncFeignService.transformMdmData(dataTransformDTO);
    }

    public List<MdmStoreBaseDTO> findMdmStoreByComId(String comId) {
        log.info("ThirdService|findMdmStoreByComId传入参数=={}", comId);
        ResponseEntity<List<MdmStoreBaseDTO>> responseEntity = storeSyncFeignService.findMdmStoreByComId(comId);
        if (responseEntity != null && responseEntity.getStatusCode().equals(HttpStatus.OK)) {
            List<MdmStoreBaseDTO> storeBaseDTOList = responseEntity.getBody();
            return storeBaseDTOList.stream().filter(d -> ConstantPool.HD_SYSTEM.equalsIgnoreCase(d.getSystemType())).collect(Collectors.toList());
        }
        return null;

    }



    /**
     * 判断是否是邦建 连锁
     * @param comId 连锁编码
     * @return
     */
    public Boolean isBangJianComId(String comId,String channel) {
        Map<String, String> dbMapConfig = JSONObject.parseObject(dbMapConfigStr, Map.class);
        log.info("|PriceCompareServiceImpl|isBangJianComId|获取连锁Map|dbMapConfig:{} 请求参数:{}", dbMapConfig,comId);

        String comIds = dbMapConfig.get(channel);

        if (StringUtils.isBlank(comIds)){
            return false;
        }
        String[] idsStr = comIds.split(",");
        if(idsStr.length <= 0){
            return false;
        }
        return Arrays.asList(idsStr).contains(comId);
    }

    /**
     * 判断是否是邦建 连锁
     * @param comId 连锁编码
     * @return
     */
    public Boolean isBangJianComId(String comId) {
        return isBangJianComId(comId,"bj");
    }

    /**
     * 返回连锁详细信息
     * @return
     */
    public List<BusinessInfoDTO> getAllBusinessInfoByIds(List<Long> ids) {
        ResponseEntity<List<BusinessInfoDTO>> responseEntity = storeSyncFeignService.getAllBusinessInfoByIds(ids);
        List<BusinessInfoDTO> businessInfoDTOList = null;
        if (responseEntity != null && responseEntity.getStatusCode().equals(HttpStatus.OK)) {
            businessInfoDTOList = responseEntity.getBody();
        }
        return businessInfoDTOList;
    }

    /**
     * 根据连锁id查询门店ids
     *
     * @param businessId
     * @return crm
     */
    public List<CrmStoreDTO> getCrmStoreInfoByBusinessId(Long businessId) {
        log.debug("ThirdService.getCrmStoreIdsByBusinessId:{}", businessId);
        List<CrmStoreDTO> crmStoreDTOS = null;
        ResponseEntity<List<CrmStoreDTO>> responseEntity = storeSyncFeignService.getCrmStoreByBusinessId(businessId);
        if (responseEntity != null && responseEntity.getStatusCode().equals(HttpStatus.OK)) {
            crmStoreDTOS = responseEntity.getBody();
        }
        log.debug("ThirdService|getStoreIdsByBusinessId response:{}", crmStoreDTOS);
        return crmStoreDTOS;
    }

    public MdmStoreBaseDTO getMdmStoreByStoreNo(String storeNo) {

        log.info("ThirdService|getMdmStoreByStoreNo|storeNo={}", storeNo);
        if (StringUtils.isBlank(storeNo)) {
            return null;
        }
        MdmStoreBaseDTO mdmStore = null;
        try {
            String storeNoKey = ConstantPool.STOCK_MDM_STORE_KEY + storeNo;
            String mdmStoreInfo = redissonCacheService.get(storeNoKey);

            if (StringUtils.isNotBlank(mdmStoreInfo)) {
                return JSONObject.parseObject(mdmStoreInfo, MdmStoreBaseDTO.class);
            }

            mdmStore = storeSyncFeignService.findByStoreNo(storeNo).getBody();
            if (Objects.isNull(mdmStore)) {
                return null;
            }
            redissonCacheService.setNxString(storeNoKey, JSON.toJSONString(mdmStore), 60 * 60 * 1L);
        } catch (Exception e) {
            log.error("ThirdService|getMdmStoreByStoreNo|根据门店编码获取门店异常|storeNo={}", storeNo);
        }

        return mdmStore;
    }

    public MdmStoreBaseDTO getMdmStoreByStoreId(Long storeId) {

        log.info("ThirdService|getMdmStoreByStoreId|storeId={}", storeId);
        if (Objects.isNull(storeId)) {
            return null;
        }
        MdmStoreBaseDTO mdmStore = null;
        try {
            String storeNoKey = ConstantPool.STOCK_MDM_STORE_KEY + storeId;
            String mdmStoreInfo = redissonCacheService.get(storeNoKey);

            if (StringUtils.isNotBlank(mdmStoreInfo)) {
                return JSONObject.parseObject(mdmStoreInfo, MdmStoreBaseDTO.class);
            }

            mdmStore = storeSyncFeignService.findByStoreId(storeId).getBody();
            if (Objects.isNull(mdmStore)) {
                return null;
            }
            redissonCacheService.setNxString(storeNoKey, JSON.toJSONString(mdmStore), 60 * 60 * 1L);
        } catch (Exception e) {
            log.error("ThirdService|getMdmStoreByStoreId|根据门店编码获取门店异常|storeId={}", storeId);
        }

        return mdmStore;
    }

    /**
     * 根据连锁ID获取连锁信息
     *
     * @param businessId 连锁ID
     * @return {@link MdmBusinessBaseDTO }
     */
    public MdmBusinessBaseDTO getMdmBusinessByBusinessId(Long businessId) {
        try {
            String key = ConstantPool.MDM_BUSINESS_KEY + businessId;
            String cacheValue = redissonCacheService.get(key);
            if (StringUtils.isNotBlank(cacheValue)){
                return JSONObject.parseObject(cacheValue, MdmBusinessBaseDTO.class);
            }
            List<MdmBusinessBaseDTO> businessList = storeFeignService.findMdmBusinessBaseByBusinessIds(Lists.newArrayList(businessId),null,null);
            if (CollectionUtils.isEmpty(businessList)){
                log.error("ThirdService|getMdmBusinessByBusinessId|根据业务id获取连锁信息为空|businessId={}", businessId);
                return null;
            }
            MdmBusinessBaseDTO mdmBusiness = businessList.get(0);
            redissonCacheService.setNxString(key, JSON.toJSONString(mdmBusiness), 60 * 60 * 1L);
            return mdmBusiness;
        } catch (Exception e) {
            log.error("ThirdService|getMdmBusinessByBusinessId|根据业务id获取连锁信息异常|businessId={}", businessId,e);
            return null;
        }
    }

    public HashSet<String> queryStoreApplyDateSet(String date) {
        try {
            if (StringUtils.isBlank(date)){
                return Sets.newHashSet();
            }
            String key = ConstantPool.STOCK_APPLY_DATE_CACHE_KEY + date;
            String dateAmes = DateUtil.dateToStr(DateUtil.getDate(date, ConstantPool.YYYYMMDD), DateUtil.DATE_FORMAT);
            String cacheValue = redissonCacheService.get(key);
            if (StringUtils.isNotBlank(cacheValue)){
                List<String> list = JSON.parseArray(cacheValue, String.class);
                if (CollectionUtils.isNotEmpty(list)){
                    log.info("获取门店请货日数据成功|date={} size={}", date, list.size());
                    return new HashSet<>(list);
                }
            }
            HashSet<String> storeNoSet = new HashSet<>();
            List<StoreApplyDateDTO> storeApplyDateDTOList = nyuwaErpFeign.queryStoreApplyDateResult(dateAmes, STORE_APPLY_DATE_COLUMNS).getData().getRows();
            if (CollectionUtils.isNotEmpty(storeApplyDateDTOList)) {
                storeNoSet = (HashSet<String>) storeApplyDateDTOList.stream().filter(v -> STORE_APPLY_DATE_STORE_STATUS.equals(v.getStore_status())).map(StoreApplyDateDTO::getStore_code).collect(Collectors.toSet());
                if (CollectionUtils.isNotEmpty(storeNoSet)) {
                    redissonCacheService.setNxString(key, JSON.toJSONString(storeNoSet), 60 * 60 * 20L);
                    return storeNoSet;
                }
            }
        } catch (Exception e) {
            log.error("获取门店请货日期失败|失败date :{}", date, e);
            throw new RuntimeException("获取门店请货日期失败");
        }
        return Sets.newHashSet();
    }

    public HashSet<String> queryStoreApplyDate(String date) {
        try {
            if (StringUtils.isBlank(date)){
                return Sets.newHashSet();
            }
            String key = ConstantPool.STOCK_AND_PRICE_APPLY_DATE_CACHE_KEY + date;
            String dateAmes = DateUtil.dateToStr(DateUtil.getDate(date, ConstantPool.YYYYMMDD), DateUtil.DATE_FORMAT);
            String cacheValue = redissonCacheService.get(key);
            if (StringUtils.isNotBlank(cacheValue)){
                List<String> list = JSON.parseArray(cacheValue, String.class);
                if (CollectionUtils.isNotEmpty(list)){
                    log.info("获取门店请货日数据成功|date={} size={}", date, list.size());
                    return new HashSet<>(list);
                }
            }
            HashSet<String> storeNoSet = new HashSet<>();
            List<StoreApplyDateDTO> storeApplyDateDTOList = nyuwaErpFeign.queryStoreApplyDateResult(dateAmes, STORE_APPLY_DATE_COLUMNS).getData().getRows();
            if (CollectionUtils.isNotEmpty(storeApplyDateDTOList)) {
                storeNoSet = (HashSet<String>) storeApplyDateDTOList.stream().filter(v -> STORE_APPLY_DATE_STORE_STATUS.equals(v.getStore_status())).map(StoreApplyDateDTO::getStore_code).collect(Collectors.toSet());
                if (CollectionUtils.isNotEmpty(storeNoSet)) {
                    redissonCacheService.setNxString(key, JSON.toJSONString(storeNoSet), 60 * 60 * 20L);
                    return storeNoSet;
                }
            }
        } catch (Exception e) {
            log.error("获取门店请货日期失败|失败date :{}", date, e);
            throw new RuntimeException("获取门店请货日期失败");
        }
        return Sets.newHashSet();
    }
}
