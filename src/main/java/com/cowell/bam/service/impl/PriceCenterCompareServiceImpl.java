package com.cowell.bam.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.cowell.bam.config.Constants;
import com.cowell.bam.domain.DiffDataCompareInfoExample;
import com.cowell.bam.domain.DiffDataCompareInfoWithBLOBs;
import com.cowell.bam.entity.ItemPrice;
import com.cowell.bam.entity.PriceCompareResultSendEmail;
import com.cowell.bam.entity.PriceCompareStatistic;
import com.cowell.bam.enums.PriceTypeEnum;
import com.cowell.bam.enums.PriceComparisonEnum;
import com.cowell.bam.enums.SyncTypeEnum;
import com.cowell.bam.mq.PriceCenterComparePriceProducer;
import com.cowell.bam.mq.message.CompareOneselfMessage;
import com.cowell.bam.repository.mybatis.dao.DiffDataCompareInfoMapper;
import com.cowell.bam.service.*;
import com.cowell.bam.service.dto.*;
import com.cowell.bam.service.dto.base.CommonResponse;
import com.cowell.bam.service.utils.ExcelUtil;
import com.cowell.bam.web.rest.errors.BusinessErrorException;
import com.cowell.bam.web.rest.util.*;
import com.ctrip.framework.apollo.spring.annotation.ApolloJsonValue;
import com.google.common.collect.Lists;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.redisson.api.RBucket;
import org.redisson.api.RedissonClient;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.task.AsyncTaskExecutor;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;

import java.io.ByteArrayOutputStream;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Collectors;

/**
 * 类说明 价格中心对比 接口
 *
 * @Author: liw
 * @Date: 2020-05-31 00:54
 */
@Service
public class PriceCenterCompareServiceImpl implements PriceCenterCompareService {

    private final Logger logger = LoggerFactory.getLogger(PriceCenterCompareServiceImpl.class);

    @Autowired
    private ThirdService thirdService;
    @Autowired
    private HdDataService hdDataService;
    @Autowired
    private IPriceCenterFeignService priceCenterFeignService;
    @Autowired
    private DiffDataCompareInfoMapper diffDataCompareInfoMapper;
    @Autowired
    private PriceCenterComparePriceProducer priceCenterComparePriceProducer;
    @Autowired
    private IOmsFeignService omsFeignService;
    @Autowired
    @Qualifier("taskExecutorTrace")
    private AsyncTaskExecutor asyncTaskExecutor;
    @Autowired
    @Qualifier("comparePriceByGoodsExecutor")
    private AsyncTaskExecutor comparePriceByGoodsExecutor;
    @Autowired
    @Qualifier("businessComparePriceExecutorTrace")
    private AsyncTaskExecutor businessComparePriceExecutor;
    @Autowired
    @Qualifier("taskExecutorTrace2")
    private AsyncTaskExecutor asyncTaskExecutor2;
    @Autowired
    private RedissonClient redissonClient;
    @Autowired
    private IStoreSyncFeignService storeFeignService;

    private static final String openBusinessList = "OPEN_OMS_CONFIG_BUSINESS_LIST";

    // 调价单2.0版本推送POS方的渠道ID
    @Value("${push.pos.channelId:0}")
    private int pushPosChannelId;

    /**
     * 价格比对结果发送邮件接收人
     */
    @Value("${priceCompareResultSendEmail:<EMAIL>}")
    private String priceCompareResultSendEmail;

    /**
     * 价格比对结果发送邮件接收人业务方配置
     */
    @ApolloJsonValue("${priceCompareResultSendEmail.bizConfig:{}}")
    private Map<String, List<PriceCompareResultSendEmail>> priceCompareResultSendEmailMap;

    /**
     * 价格比对结果发送邮件类型 business-连锁，store-门店
     */
    @Value("${priceCompareResultSendEmailType:business}")
    private String priceCompareResultSendEmailType;

    @Autowired
    private PriceCompareService priceCompareService;

    @Value("${compareAndSendGoodsPriceData:false}")
    private boolean compareAndSendGoodsPriceData;

    @Value("${priceComparePosStoreBlackList:}")
    private String priceComparePosStoreBlackList;

    @Autowired
    private IRedissonCacheService redissonCacheService;

    @Value("${comparePriceDistribute:false}")
    private boolean comparePriceDistribute;


    @Override
    public void priceCenterCompareFromGoods(Long businessId, Long storeId, List<String> goodsNos) {

        MdmDataTransformDTO mdmDataTransformDTO = new MdmDataTransformDTO();
        mdmDataTransformDTO.setTransFormType(1);
        mdmDataTransformDTO.setBusinessId(String.valueOf(businessId));
        mdmDataTransformDTO.setStoreIds(Lists.newArrayList(storeId));
        mdmDataTransformDTO.setDataType(2);
        MdmDataTransformDTO mdmStoreInfo = storeFeignService.transformMdmData(mdmDataTransformDTO);

        String busNoCur = mdmStoreInfo.getStoreNos().get(0);
        String comIdCur = mdmStoreInfo.getComId();
        this.priceCenterCompareFromGoods(comIdCur, busNoCur, goodsNos);
    }


    @Override
    public void priceCenterCompareFromGoods(String comId, String busNo, List<String> goodsNos) {

        Assert.isTrue(StringUtils.isNotEmpty(comId), "连锁编码不可以为空");
        Assert.isTrue(StringUtils.isNotEmpty(busNo), "门店编码不可以为空");
        Assert.isTrue(CollectionUtils.isNotEmpty(goodsNos), "商品编码不可以为空");
        //查询门店对应关系
        String businessIdAndStoreIdStr = thirdService.getBusinessIdAndStoreId(comId, busNo);
        String[] businessIdAndStoreIdStrArr = businessIdAndStoreIdStr.split(":");
        if (StringUtils.isBlank(businessIdAndStoreIdStrArr[0]) || StringUtils.isBlank(businessIdAndStoreIdStrArr[1])) {
            logger.error("根据连锁门店编码、商品编码未查询到连锁ID、门店ID 连锁编码:{} 门店编码:{}", comId, busNo);
            throw new BusinessErrorException("根据连锁门店编码、商品编码未查询到连锁ID、门店ID");
        }

        Long businessId = Long.parseLong(businessIdAndStoreIdStrArr[0]);
        Long storeId = Long.parseLong(businessIdAndStoreIdStrArr[1]);

        //查询海典/邦建 价格
        List<ItemPrice> itemPriceList = hdDataService.queryPriceByComIdAndBusNoAndGoods(comId, busNo, goodsNos);

        if (CollectionUtils.isEmpty(itemPriceList)) {
            logger.info("根据门店编码、连锁编码、商品编码 查询第三方价格为空, 不做对比操作 :{} :{} :{}", comId, busNo, goodsNos);
            return;
        }

        //查询价格中台 价格
        List<ItemPriceResponse> itemPriceResponseList = this.getPriceCenterGoodsPrice(businessId, storeId, goodsNos);

        //进行价格对比以及补发更新
        this.doComparePrice(itemPriceList, itemPriceResponseList, businessId, storeId);

    }


    @Override
    public void priceCenterCompareFromBusNo(String comId, String busNo) {

        //校验参数
        this.priceCenterCompareFromBusNoCommonCheck(comId, busNo);

        if (comparePriceDistribute) {
            MdmStoreBaseDTO mdmStoreByStoreNo = thirdService.getMdmStoreByStoreNo(busNo);
            doCompareStorePrice(mdmStoreByStoreNo, true);
            return;
        }

        //查询海典/邦建 价格
        List<ItemPrice> itemPriceList = hdDataService.queryPriceByComIdAndBusNo(comId, busNo);

        if (CollectionUtils.isEmpty(itemPriceList)) {
            logger.info("根据门店编码、连锁编码 查询第三方价格为空, 不做对比操作 :{} :{}", comId, busNo);
            return;
        }
        this.priceCenterCompareFromBusNoCommon(itemPriceList, comId, busNo);
    }

    @Override
    public void priceCenterCompareFromBusNoByDate(String comId, String busNo, String queryDate) {

        //校验参数
        this.priceCenterCompareFromBusNoCommonCheck(comId, busNo);

        //查询海典/邦建 价格
        List<ItemPrice> itemPriceList = hdDataService.queryPriceByComIdAndBusNoAndDate(comId, busNo, queryDate);

        if (CollectionUtils.isEmpty(itemPriceList)) {
            logger.info("根据门店编码、连锁编码 查询第三方价格为空, 不做对比操作 :{} :{}", comId, busNo);
            return;
        }

        this.priceCenterCompareFromBusNoCommon(itemPriceList, comId, busNo);
    }

    @Override
    public void priceCenterCompareFromComId(String comId) {
        Assert.isTrue(StringUtils.isNotEmpty(comId), "连锁编码不可以为空");

        if (comparePriceDistribute) {
            exportByComIdDistribute(comId, true);
            return;
        }

        List<MdmStoreBaseDTO> mdmStoreBaseDTOList = thirdService.findMdmStoreByComId(comId);
        logger.info("根据连锁编码 获取连锁门店信息, 连锁编码 :{},门店数量 :{}", comId, mdmStoreBaseDTOList == null ? 0 : mdmStoreBaseDTOList.size());
        if (CollectionUtils.isEmpty(mdmStoreBaseDTOList)) {
            logger.error("根据连锁编码 未查询到连锁ID 连锁编码:{} ", comId);
            throw new BusinessErrorException("根据连锁编码 未查询到连锁ID");
        }

        List<String> storeNos = mdmStoreBaseDTOList.stream().map(MdmStoreBaseDTO::getStoreNo).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(storeNos)) {
            storeNos.forEach(storeNo -> {
                //发送mq异步处理
                CompareOneselfMessage message = new CompareOneselfMessage();
                message.setComId(comId);
                message.setBusNo(storeNo);
                priceCenterComparePriceProducer.sendMessageToCompareTaskSplit(message);
                logger.info("根据连锁编码 获取连锁门店信息,发送MQ成功 连锁编码 :{},门店编码 :{}", comId, storeNo);
            });

        }

    }

    @Override
    public void priceCenterComparePosFromComId(String comId) {
        Assert.isTrue(StringUtils.isNotEmpty(comId), "连锁编码不可以为空");

        if (comparePriceDistribute) {
            exportByComIdDistribute(comId, false);
            return;
        }

        if ("business".equals(priceCompareResultSendEmailType)) {
            //发送mq异步处理
            Long businessId = thirdService.transferMdmComIdToBusinessId(comId);
            CompareOneselfMessage message = new CompareOneselfMessage();
            message.setComId(comId);
            message.setBusinessId(businessId);
            message.setIsPriceComparison(PriceComparisonEnum.COMPARE_POS_PRICE.getCode());
            priceCenterComparePriceProducer.sendMessageToCompareTaskSplit(message);
            logger.info("根据连锁编码 获取连锁门店信息,发送MQ成功 连锁编码 :{}, businessId:{}", comId, businessId);
        }

        if ("store".equals(priceCompareResultSendEmailType)) {
            List<MdmStoreBaseDTO> mdmStoreBaseDTOList = thirdService.findMdmStoreByComId(comId);
            if (CollectionUtils.isEmpty(mdmStoreBaseDTOList)) {
                logger.error("根据连锁编码 未查询到连锁ID 连锁编码:{} ", comId);
                throw new BusinessErrorException("根据连锁编码 未查询到连锁ID");
            }

            List<String> blackList = getPriceComparePosBlackList();
            if (CollectionUtils.isNotEmpty(mdmStoreBaseDTOList)) {
                mdmStoreBaseDTOList.forEach(storeBaseDTO -> {
                    Long storeId = storeBaseDTO.getStoreId();
                    if (!blackList.contains(storeId.toString())) {
                        String storeNo = storeBaseDTO.getStoreNo();
                        Long businessId = storeBaseDTO.getBusinessId();
                        //发送mq异步处理
                        CompareOneselfMessage message = new CompareOneselfMessage();
                        message.setComId(comId);
                        message.setBusNo(storeNo);
                        message.setBusinessId(businessId);
                        message.setStoreId(storeId);
                        message.setIsPriceComparison(PriceComparisonEnum.COMPARE_POS_PRICE.getCode());
                        priceCenterComparePriceProducer.sendMessageToCompareTaskSplit(message);
                        logger.info("根据连锁编码 获取连锁门店信息,发送MQ成功 连锁编码 :{},门店编码 :{}, businessId:{}, storeId:{}",
                            comId, storeNo, businessId, storeId);
                    }
                });
            }
        }
    }

    private List<String> getPriceComparePosBlackList() {
        List<String> list = new ArrayList<>();
        if (StringUtils.isNotBlank(priceComparePosStoreBlackList)) {
            String[] split = priceComparePosStoreBlackList.split(",");
            Collections.addAll(list, split);
        }
        return list;
    }

    @Override
    public void doComparePrice(List<ItemPrice> itemPriceList, List<ItemPriceResponse> itemList, Long businessId, Long storeId) {
        doComparePrice(itemPriceList, itemList, businessId, storeId, false);
    }

    public void doComparePrice(List<ItemPrice> itemPriceList, List<ItemPriceResponse> itemList, Long businessId, Long storeId,
                               boolean pos){
        doComparePrice(itemPriceList, itemList, businessId, storeId, pos, new ArrayList<>());
    }

    @Override
    public void doComparePrice(List<ItemPrice> itemPriceList, List<ItemPriceResponse> itemList, Long businessId, Long storeId,
                               boolean pos, List<PricePushDTO> needRetweetPosItemList) {

        Map<String, ItemPriceResponse> goodsNoToPriceMap = itemList.stream().collect(Collectors.toMap(ItemPriceResponse::getGoodsNo, item -> item, (v1, v2) -> v2));

        //需要重推的额商品价格
        List<ItemPrice> needRetweetItemList = Lists.newArrayList();
        //需要插入的对比记录
        List<DiffDataCompareInfoWithBLOBs> diffDataCompareInfos = new ArrayList<>();

        itemPriceList.forEach(itemPrice -> {
            //需要对比的价格类型 1 零售价 2 会员价
            String compareType = itemPrice.getType();
            PriceTypeEnum priceTypeEnum = PriceTypeEnum.getByCode(Integer.parseInt(itemPrice.getType()));
            if (Objects.isNull(priceTypeEnum)) {
                return;
            }
            String thirdPriceStr = StringUtils.isBlank(itemPrice.getPrice()) ? "0" : itemPrice.getPrice();
            BigDecimal thirdPriceBigDecimal = new BigDecimal(thirdPriceStr).setScale(Constants.PRICE_SCALE, RoundingMode.FLOOR);
            //价格中台的价格
            ItemPriceResponse itemPriceResponse = goodsNoToPriceMap.get(itemPrice.getWarecode());
//            logger.info("价格中台的价格信息:{}", itemPriceResponse);
            if (itemPriceResponse == null) {
                if ("0".equals(thirdPriceStr)) {
                    logger.warn("线下{}为:{} compareType:{} 门店:{} 价格为零不做对比和插入", priceTypeEnum.getPriceTypeName(),
                        thirdPriceBigDecimal, compareType, businessId + "-" + storeId + "-" + itemPrice.getWarecode());
                    return;
                }
                if (!pos) {
                    needRetweetItemList.add(itemPrice);
                    diffDataCompareInfos.add(this.toDiffDataCompareInfo(businessId, storeId, "未推送价格到中台", itemPrice, itemPriceResponse));
                } else {
                    //需要重推的价格数据
                    needRetweetPosItemList.add(buildPushDTO(businessId, storeId, null, priceTypeEnum, itemPrice.getWarecode(), thirdPriceBigDecimal, "未推送价格到中台"));
                }
                //需要插入的对比记录
                return;
            }

            //价格类型
            String goodsNo = itemPriceResponse.getGoodsNo();
            String offlinePriceStr = null, onlinePriceStr = null;
            Integer onlineIsHasPrice = null;
            switch (priceTypeEnum) {
                case LSJ:
                    offlinePriceStr = itemPrice.getPrice();
                    onlinePriceStr = itemPriceResponse.getPrice();
                    onlineIsHasPrice = itemPriceResponse.getIsHasPrice();
                    break;
                case HYJ:
                    offlinePriceStr = itemPrice.getPrice();
                    onlinePriceStr = itemPriceResponse.getMemberPrice();
                    onlineIsHasPrice = itemPriceResponse.getIsHasMemberPrice();
                    break;
                case CLJ:
                    offlinePriceStr = itemPrice.getPrice();
                    onlinePriceStr = itemPriceResponse.getPiecePrice();
                    onlineIsHasPrice = itemPriceResponse.getIsHasPiecePrice();
                    break;
                case CHYJ:
                    offlinePriceStr = itemPrice.getPrice();
                    onlinePriceStr = itemPriceResponse.getMemberPiecePrice();
                    onlineIsHasPrice = itemPriceResponse.getIsHasMemberPiecePrice();
                    break;
                default:
                    break;
            }

            String reason = priceCompare(offlinePriceStr, onlinePriceStr, priceTypeEnum, goodsNo, onlineIsHasPrice, pos);
            logger.debug("priceCompare返回|offlinePriceStr:{}, onlinePriceStr:{}, priceTypeEnum:{}, goodsNo:{}, onlineHasPrice:{}, pos:{}, reason:{}",
                offlinePriceStr, onlinePriceStr, priceTypeEnum, goodsNo, onlineIsHasPrice, pos, reason);
            if (StringUtils.isNotBlank(reason)) {
                //需要重推的价格数据
                if (pos) {
                    BigDecimal onlinePrice = new BigDecimal(onlinePriceStr).setScale(Constants.PRICE_SCALE, RoundingMode.FLOOR);
                    needRetweetPosItemList.add(buildPushDTO(businessId, storeId, onlinePrice, priceTypeEnum, goodsNo, thirdPriceBigDecimal, reason));
                } else {
                    needRetweetItemList.add(itemPrice);
                }
                //需要插入的对比记录
                diffDataCompareInfos.add(this.toDiffDataCompareInfo(businessId, storeId, reason, itemPrice, itemPriceResponse));
            }

        });

        if (CollectionUtils.isNotEmpty(diffDataCompareInfos)) {
            //进行对比记录的 insert
            diffDataCompareInfos.forEach(itemD -> diffDataCompareInfoMapper.insertSelective(itemD));
            logger.info("价格对比完成 存在不一致的:{}", storeId);
        }

        //发送价格不一致的消息到 价格价格中台
        if (CollectionUtils.isNotEmpty(needRetweetItemList)) {
            this.noticePriceCenter(needRetweetItemList, null, pos);
            logger.info("价格对比完成 发送不一致的商品到价格中台更新:{}", storeId);
        }

        if (diffDataCompareInfos.size() > 0) {
            logger.info("价格对比完成 存在不一致的数量:{}", diffDataCompareInfos.size());
        }
    }

    /**
     * 比对价格
     * @param offlinePriceStr 线下价格
     * @param onlinePriceStr 线上价格（价格中台）
     * @param priceTypeEnum 价格类型
     * @param goodsNo 商品编码
     * @param onlineHasPrice 线上是否有价格 0：否，1：是
     * @param pos 是否推送POS
     * @return String
     */
    private String priceCompare(String offlinePriceStr, String onlinePriceStr, PriceTypeEnum priceTypeEnum, String goodsNo, Integer onlineHasPrice, boolean pos) {
        logger.debug("priceCompare入参|offlinePriceStr:{}, onlinePriceStr:{}, priceTypeEnum:{}, goodsNo:{}, onlineHasPrice:{}, pos:{}",
            offlinePriceStr, onlinePriceStr, priceTypeEnum, goodsNo, onlineHasPrice, pos);
        if (StringUtils.isBlank(offlinePriceStr) || StringUtils.isBlank(onlinePriceStr)) {
            return null;
        }
        BigDecimal onlinePrice = new BigDecimal(onlinePriceStr).setScale(Constants.PRICE_SCALE, RoundingMode.FLOOR);
        BigDecimal offlinePrice = new BigDecimal(offlinePriceStr).setScale(Constants.PRICE_SCALE, RoundingMode.FLOOR);
        if (onlinePrice.compareTo(offlinePrice) == 0) {
            return null;
        }
        String reason;
        logger.info("{}对比不一致:{} :{} :{}", priceTypeEnum.getPriceTypeName(), offlinePriceStr, onlinePriceStr, goodsNo);
        if (Objects.nonNull(onlineHasPrice) && onlineHasPrice == 0) {
            reason = pos ? "未推送" + priceTypeEnum.getPriceTypeName() + "数据到POS" : "未推送" + priceTypeEnum.getPriceTypeName() + "数据到中台";
        } else {
            reason = pos ? "POS中" + priceTypeEnum.getPriceTypeName() + "和中台不一致" : "推送" + priceTypeEnum.getPriceTypeName() + "和中台不一致";
        }
        return reason;
    }

    private PricePushDTO buildPushDTO(Long businessId, Long storeId, BigDecimal price, PriceTypeEnum priceTypeEnum, String goodsNo, BigDecimal thirdPriceBigDecimal, String reason) {
        PricePushDTO pricePushDTO = new PricePushDTO();
        pricePushDTO.setBusinessId(businessId);
        pricePushDTO.setStoreId(storeId);
        pricePushDTO.setGoodsNo(goodsNo);
        pricePushDTO.setPrice(price);
        pricePushDTO.setPriceTypeCode(priceTypeEnum.getPriceTypeCode());
        pricePushDTO.setChannelId(pushPosChannelId);
        pricePushDTO.setPosPrice(thirdPriceBigDecimal);
        pricePushDTO.setReason(reason);
        return pricePushDTO;
    }

    @Override
    public void noticePriceCenter(List<ItemPrice> storeItemPriceList, List<PricePushDTO> needRetweetPosItemList, boolean pos) {

        CompareOneselfMessage message = new CompareOneselfMessage();
        if (pos) {
            return;
        } else {
            if (CollectionUtils.isEmpty(storeItemPriceList)) {
                return;
            }
            message.setItemPriceList(storeItemPriceList);
        }
        priceCenterComparePriceProducer.sendMessage(message);
        logger.info("价格对比存在不一致, 通知价格中台修改价格, 发送MQ成功");
    }

    @Override
    public List<String> openConfigBusinessIdList() {

        List<String> comIds = Lists.newArrayList();
        try {
            ResponseEntity<List<Long>> responseEntity = omsFeignService.openConfigBusinessIdList();
            logger.info("获取Oms开通配置的连锁ID:{}", responseEntity);

            if (responseEntity == null || CollectionUtils.isEmpty(responseEntity.getBody())) {
                return comIds;
            }
            responseEntity.getBody().forEach(item -> {
                String comId = thirdService.getComId(item);
                if (StringUtils.isNotBlank(comId)) {
                    comIds.add(comId);
                }
            });
        } catch (Exception e) {
            logger.warn("调用OMS获取开通的连锁列表异常:", e);
            return comIds;
        }
        if (CollectionUtils.isNotEmpty(comIds)) {
            RBucket<String> bucket = redissonClient.getBucket(openBusinessList);
            bucket.set(JSONObject.toJSONString(comIds), 12, TimeUnit.HOURS);
        }

        return comIds;
    }

    /**
     * @param businessId
     * @param storeId
     * @param goodsNos
     */
    private List<ItemPriceResponse> getPriceCenterGoodsPrice(Long businessId, Long storeId, List<String> goodsNos) {
        return getPriceCenterGoodsPrice(businessId, storeId, goodsNos, null);
    }

    private List<ItemPriceResponse> getPriceCenterGoodsPrice(Long businessId, Long storeId, List<String> goodsNos, Integer channelId) {
        return getPriceCenterGoodsPrice(businessId, storeId, goodsNos, channelId, 0);
    }

    private List<ItemPriceResponse> getPriceCenterGoodsPrice(Long businessId, Long storeId, List<String> goodsNos, Integer channelId, int retry) {
        //价格中心的
        List<ItemPriceResponse> itemPriceResponseList = Lists.newArrayList();

        //查询价格中台价格
        PriceQueryParam priceQueryParam = new PriceQueryParam();
        priceQueryParam.setBusinessId(businessId);
        priceQueryParam.setStoreId(storeId);
        priceQueryParam.setGoodsNoList(goodsNos);
        if (Objects.nonNull(channelId)) {
            priceQueryParam.setChannelId(channelId);
        }
        List<ItemPriceResponse> itemListPriceCenter = null;
        try {
            CommonResponse<List<PriceQueryGoodsNoDTO>> commonResponse = priceCenterFeignService.getPriceAndMemberPrice(priceQueryParam);

            if (commonResponse.getCode() == 0 && CollectionUtils.isNotEmpty(commonResponse.getResult())) {
                List<PriceQueryGoodsNoDTO> priceQueryGoodsNoDTOList = commonResponse.getResult();
//                logger.info("根据连锁Id、门店id和商品编码,查询价格中台的商品:{},{},{}", businessId, storeId, priceQueryGoodsNoDTOList.toString());
                itemListPriceCenter = priceQueryGoodsNoDTOList.stream().map(item -> toItemListPriceCenter(item, businessId, storeId)).collect(Collectors.toList());
                itemPriceResponseList.addAll(itemListPriceCenter);
            }
        } catch (Exception e) {
            logger.warn("根据连锁Id、门店id和商品编码,查询价格中台超时或者为空 :{} :{}, retry:{}", businessId, storeId, retry);
            if (retry > 0) {
                return getPriceCenterGoodsPrice(businessId, storeId, goodsNos, channelId, retry - 1);
            }
        }

        return itemPriceResponseList;
    }

    /**
     * @param businessId
     * @param storeId
     * @param page
     * @param pageSize
     */
    private List<ItemPriceResponse> getAllPOSPricePage(Long businessId, Long storeId, Integer page, Integer pageSize) {
        //价格中心的
        List<ItemPriceResponse> itemPriceResponseList = Lists.newArrayList();

        //查询价格中台价格
        PriceQueryParam priceQueryParam = new PriceQueryParam();
        priceQueryParam.setBusinessId(businessId);
        priceQueryParam.setStoreId(storeId);
        priceQueryParam.setPage(page);
        priceQueryParam.setPageSize(pageSize);
        List<ItemPriceResponse> itemListPriceCenter = null;
        try {
            CommonResponse<List<PriceQueryGoodsNoDTO>> commonResponse = priceCenterFeignService.getAllPOSPricePage(priceQueryParam);

            if (commonResponse.getCode() == 0 && CollectionUtils.isNotEmpty(commonResponse.getResult())) {
                List<PriceQueryGoodsNoDTO> list = commonResponse.getResult();
                logger.info("根据连锁Id、门店id和商品编码,查询价格中台的商品:{},{},{}", businessId, storeId, list.toString());
                itemListPriceCenter = list.stream().map(item -> toItemListPriceCenter(item, businessId, storeId)).collect(Collectors.toList());
                itemPriceResponseList.addAll(itemListPriceCenter);
            }
        } catch (Exception e) {
            logger.warn("根据连锁Id、门店id和商品编码,查询价格中台超时或者为空 :{} :{}", businessId, storeId);
        }

        return itemPriceResponseList;
    }

    /**
     * 通用检查以及 获取连锁ID和门店ID
     *
     * @param comId
     * @param busNo
     * @return
     */
    private void priceCenterCompareFromBusNoCommonCheck(String comId, String busNo) {

        Assert.isTrue(StringUtils.isNotEmpty(comId), "连锁编码不可以为空");
        Assert.isTrue(StringUtils.isNotEmpty(busNo), "门店编码不可以为空");
    }

    /**
     * 通用检查以及 获取连锁ID和门店ID
     *
     * @param comId
     * @param busNo
     * @return
     */
    private String[] getBusinessIdAndStoreIdCommon(String comId, String busNo) {

        //查询门店对应关系
        String businessIdAndStoreIdStr = thirdService.getBusinessIdAndStoreId(comId, busNo);
        String[] businessIdAndStoreIdStrArr = businessIdAndStoreIdStr.split(":");
        if (StringUtils.isBlank(businessIdAndStoreIdStrArr[0]) || StringUtils.isBlank(businessIdAndStoreIdStrArr[1])) {
            logger.error("根据连锁/门店编码 未查询到连锁ID、门店ID 连锁编码:{} 门店编码:{}", comId, busNo);
            throw new BusinessErrorException("根据连锁/门店编码 未查询到连锁ID、门店ID");
        }

        return businessIdAndStoreIdStrArr;
    }

    private void priceCenterCompareFromBusNoCommon(List<ItemPrice> itemPriceList, String comId, String busNo) {
        priceCenterCompareFromBusNoCommon(itemPriceList, comId, busNo, false);
    }

    /**
     * 进行商品价格对比
     *
     * @param itemPriceList
     * @param comId
     * @param busNo
     */
    private void priceCenterCompareFromBusNoCommon(List<ItemPrice> itemPriceList, String comId, String busNo, boolean pos) {


        String[] businessIdAndStoreIdStrArr = this.getBusinessIdAndStoreIdCommon(comId, busNo);

        Long businessId = Long.parseLong(businessIdAndStoreIdStrArr[0]);
        Long storeId = Long.parseLong(businessIdAndStoreIdStrArr[1]);

        //将列表进行 5000 分组
        List<List<ItemPrice>> itemPriceLists = Lists.partition(itemPriceList, 5000);

        if (!pos) {
            itemPriceLists.forEach(itemPriceList1 -> asyncTaskExecutor.execute(() -> {
                //将列表进行 500 分组
                List<List<ItemPrice>> itemPriceLists1 = Lists.partition(itemPriceList1, 50);

                itemPriceLists1.forEach(itemPriceList2 -> {
//                    logger.info("单个门店进行价格比对, 任务拆解:{}", busNo);
                    //需要查询的 商品价格
                    List<String> goodsNos = itemPriceList2.stream().distinct().map(ItemPrice::getWarecode).distinct().collect(Collectors.toList());

                    //查询价格中台 价格
                    List<ItemPriceResponse> itemPriceResponseList = this.getPriceCenterGoodsPrice(businessId, storeId, goodsNos, pushPosChannelId, 3);

                    //进行价格对比以及补发更新
                    this.doComparePrice(itemPriceList2, itemPriceResponseList, businessId, storeId);
                });
            }));
            return;
        }

        List<PricePushDTO> diffList = new ArrayList<>();
        itemPriceLists.forEach(itemPriceList1 -> {
            //将列表进行 500 分组
            List<List<ItemPrice>> itemPriceLists1 = Lists.partition(itemPriceList1, 50);

            itemPriceLists1.forEach(itemPriceList2 -> {
                logger.info("单个门店进行价格比对, 任务拆解:{}", busNo);
                // 需要查询的价格的商品
                List<String> goodsNos = itemPriceList2.stream().distinct().map(ItemPrice::getWarecode).distinct().collect(Collectors.toList());
                // 查询价格中台价格
                List<ItemPriceResponse> itemPriceResponseList = this.getPriceCenterGoodsPrice(businessId, storeId, goodsNos, pushPosChannelId, 3);
                // 进行价格对比
                this.doComparePrice(itemPriceList2, itemPriceResponseList, businessId, storeId, pos, diffList);
            });
        });
        // 发送邮件
        if (CollectionUtils.isNotEmpty(diffList) && StringUtils.isNotBlank(priceCompareResultSendEmail)) {
            MdmStoreBaseDTO mdmStoreBaseDTO = thirdService.getMdmStoreByStoreNo(busNo);
            String storeName = mdmStoreBaseDTO.getStoreName();
            String date = DateUtil.dateToStr(new Date(), DateUtil.DATE_FORMAT);

            String subject = "【价格中台】" + storeName + "线上线下价格对比_" + date;
            StringBuilder content = new StringBuilder("<html><head></head><body><h3>"+ subject +"</h3>");
            content.append("<table border=\"5\" style=\"border:solid 1px #E8F2F9;font-size=14px;;font-size:18px;width:700px;text-align:center\">");
            content.append("<tr><td>连锁id</td><td>连锁编码</td><td>门店ID</td><td>门店编码</td><td>门店名称</td><td>差异数</td></tr>");

            content.append("<tr>");
            content.append("<td>").append(businessId).append("</td>"); //第一列
            content.append("<td>").append(comId).append("</td>"); //第一列
            content.append("<td>").append(storeId).append("</td>"); //第二列
            content.append("<td>").append(busNo).append("</td>"); //第二列
            content.append("<td>").append(storeName).append("</td>"); //第三列
            content.append("<td>").append(diffList.size()).append("</td>"); //第四列
            content.append("</tr>");
            content.append("</table>");
            content.append("<h3>差异明细见附件</h3>");
            content.append("</body></html>");

            String[] to = getEmailRecipient(businessId, storeId);
            try {
                ByteArrayOutputStream bos = new ByteArrayOutputStream();
                ExcelUtil.listToExcel(diffList, getExcelColumnTitle(), "sheet1",0,  bos);
                String attachName = businessId + "_" + storeId + "_" + date + "_detail.xls";
                EmailUtils.sendMail(to, subject, content.toString(), bos, attachName);
            } catch (Exception e) {
                logger.error("价格比对邮件任务执行失败, 收件人：{}", to, e);
            }
        }
    }

    private String[] getEmailRecipient(Long businessId, Long storeId) {
        String[] defaultEmails = priceCompareResultSendEmail.split(",");
        if (Objects.isNull(businessId) && Objects.isNull(storeId)) {
            return defaultEmails;
        }

        List<String> emailList = new ArrayList<>();
        CollectionUtils.addAll(emailList, defaultEmails);

        List<PriceCompareResultSendEmail> businessConfig = priceCompareResultSendEmailMap.getOrDefault("business", null);
        filterAndAddEmailRecipient(businessId, emailList, businessConfig);

        List<PriceCompareResultSendEmail> storeConfig = priceCompareResultSendEmailMap.getOrDefault("store", null);
        filterAndAddEmailRecipient(storeId, emailList, storeConfig);

        return emailList.toArray(new String[0]);
    }

    private void filterAndAddEmailRecipient(Long id, List<String> emailList, List<PriceCompareResultSendEmail> config) {
        if (Objects.nonNull(id) && CollectionUtils.isNotEmpty(config)) {
            Optional<PriceCompareResultSendEmail> first = config.stream()
                .filter(biz -> biz.getId().equals(id))
                .findFirst();
            if (first.isPresent()) {
                PriceCompareResultSendEmail priceCompareResultSendEmail = first.get();
                String emailRecipient = priceCompareResultSendEmail.getEmailRecipient();
                String[] emailRecipientArray = emailRecipient.split(",");
                CollectionUtils.addAll(emailList, emailRecipientArray);
            }
        }
    }

    private LinkedHashMap<String, String> getExcelColumnTitle() {
        LinkedHashMap<String, String> fieldMap = new LinkedHashMap<>();
        fieldMap.put("businessId", "连锁id");
        fieldMap.put("storeId", "门店Id");
        fieldMap.put("storeNo", "门店编码");
        fieldMap.put("goodsNo", "商品编码");
        fieldMap.put("posPrice", "线下POS价格");
        fieldMap.put("price", "中台价格");
        fieldMap.put("priceTypeCode", "价格类型");
        fieldMap.put("channelId", "价格渠道");
        fieldMap.put("reason", "原因");
        return fieldMap;
    }

    private ItemPriceResponse toItemListPriceCenter(PriceQueryGoodsNoDTO dto, Long businessId, Long storeId) {
        ItemPriceResponse itemPriceResponse = new ItemPriceResponse();

        BeanUtils.copyProperties(dto, itemPriceResponse);

        itemPriceResponse.setBusinessId(businessId);
        itemPriceResponse.setStoreId(storeId);
        itemPriceResponse.setPrice(BigDecimalUtils.convertYuanByFen(dto.getPrice()));
        itemPriceResponse.setMemberPrice(BigDecimalUtils.convertYuanByFen(dto.getMemberPrice()));
        itemPriceResponse.setPiecePrice(BigDecimalUtils.convertYuanByFen(dto.getPiecePrice()));
        itemPriceResponse.setMemberPiecePrice(BigDecimalUtils.convertYuanByFen(dto.getMemberPiecePrice()));

        return itemPriceResponse;
    }


    private DiffDataCompareInfoWithBLOBs toDiffDataCompareInfo(Long businessId, Long storeId, String reason,
                                                               ItemPrice itemPrice, ItemPriceResponse itemPriceResponse) {


        logger.info("价格对比完成 进行记录插入 :{} :{} :{}", storeId, reason, itemPrice);



        DiffDataCompareInfoWithBLOBs diffDataCompareInfo = new DiffDataCompareInfoWithBLOBs();
        diffDataCompareInfo.setBusinessId(businessId);
        diffDataCompareInfo.setStoreId(storeId);
        diffDataCompareInfo.setReason(reason);
        diffDataCompareInfo.setVersion(61);
        diffDataCompareInfo.setDataType(SyncTypeEnum.PRICE.getCode());
        diffDataCompareInfo.setCreatedBy("-1");
        diffDataCompareInfo.setGmtCreate(new Date());
        diffDataCompareInfo.setGmtUpdate(new Date());
        diffDataCompareInfo.setStatus(0);
        diffDataCompareInfo.setUpdatedBy("-1");
        PriceTypeEnum priceTypeEnum = PriceTypeEnum.getByCode(Integer.parseInt(itemPrice.getType()));

        if (Objects.isNull(priceTypeEnum)) {
            return diffDataCompareInfo;
        }

        //设置 价格中台的参数
        if (itemPriceResponse != null) {
            PriceDataInfoDTO priceDataInfoDTO = new PriceDataInfoDTO();
            BeanUtils.copyProperties(itemPriceResponse, diffDataCompareInfo);
            diffDataCompareInfo.setGoodsNo(itemPriceResponse.getGoodsNo().toString());
            BeanUtils.copyProperties(itemPriceResponse, priceDataInfoDTO);
            switch (priceTypeEnum) {
                case LSJ:
                    priceDataInfoDTO.setPrice(itemPriceResponse.getPrice());
                    break;
                case HYJ:
                    priceDataInfoDTO.setPrice(itemPriceResponse.getMemberPrice());
                    break;
                case CLJ:
                    priceDataInfoDTO.setPrice(itemPriceResponse.getPiecePrice());
                    break;
                case CHYJ:
                    priceDataInfoDTO.setPrice(itemPriceResponse.getMemberPiecePrice());
                    break;
                default:
                    break;
            }

            priceDataInfoDTO.setPriceTypeCode(priceTypeEnum.getPriceTypeCode());
            priceDataInfoDTO.setSkuMerchantCode(itemPriceResponse.getGoodsNo());
            diffDataCompareInfo.setOurData(JSON.toJSONString(priceDataInfoDTO));
        }

        if (itemPrice != null) {
            ThridPriceInfoDTO thridPriceInfoDTO = new ThridPriceInfoDTO();
            BeanUtils.copyProperties(itemPrice, thridPriceInfoDTO);
            diffDataCompareInfo.setGoodsNo(itemPrice.getWarecode());
            thridPriceInfoDTO.setPrice(itemPrice.getPrice());
            thridPriceInfoDTO.setType(itemPrice.getType());
            thridPriceInfoDTO.setSkuMerchantCode(itemPrice.getWarecode());
            diffDataCompareInfo.setThirdData(JSON.toJSONString(thridPriceInfoDTO));
        }

        return diffDataCompareInfo;
    }

    @Override
    public void priceCenterFromComId(String comId,String busNo) {
        Assert.isTrue(StringUtils.isNotEmpty(comId), "连锁编码不可以为空");

        List<MdmStoreBaseDTO> mdmStoreBaseDTOList = thirdService.findMdmStoreByComId(comId);
        logger.info("根据连锁编码 获取连锁门店信息, 连锁编码 :{},门店数量 :{}", comId, mdmStoreBaseDTOList == null ? 0 : mdmStoreBaseDTOList.size());
        if (CollectionUtils.isEmpty(mdmStoreBaseDTOList)) {
            logger.error("根据连锁编码 未查询到连锁ID 连锁编码:{} ", comId);
            throw new BusinessErrorException("根据连锁编码 未查询到连锁ID");
        }

        if (StringUtils.isNotBlank(busNo)){
            //发送mq异步处理
            CompareOneselfMessage message = new CompareOneselfMessage();
            message.setComId(comId);
            message.setBusNo(busNo);
            message.setIsPriceComparison(PriceComparisonEnum.PUSH_PRICE_CENTER.getCode());
            message.setPriceRepair(true);
            priceCenterComparePriceProducer.sendMessageToCompareTaskSplit(message);
            logger.info("根据连锁编码 获取连锁门店信息,发送MQ成功-单个门店 连锁编码 :{},门店编码 :{}", comId, busNo);
        }else{
            List<String> storeNos = mdmStoreBaseDTOList.stream().map(MdmStoreBaseDTO::getStoreNo).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(storeNos)) {
                storeNos.forEach(storeNo -> {
                    //发送mq异步处理
                    CompareOneselfMessage message = new CompareOneselfMessage();
                    message.setComId(comId);
                    message.setBusNo(storeNo);
                    message.setPriceRepair(true);
                    message.setIsPriceComparison(PriceComparisonEnum.PUSH_PRICE_CENTER.getCode());
                    priceCenterComparePriceProducer.sendMessageToCompareTaskSplit(message);
                    logger.info("根据连锁编码 获取连锁门店信息,发送MQ成功 连锁编码 :{},门店编码 :{}", comId, storeNo);
                });

            }
        }
    }

    @Override
    public void priceCenterFromBusNo(String comId, String busNo) {
        //校验参数
        this.priceCenterCompareFromBusNoCommonCheck(comId, busNo);

        //查询海典/邦建 价格
        List<ItemPrice> itemPriceList = hdDataService.queryPriceByComIdAndBusNo(comId, busNo);

        if (CollectionUtils.isEmpty(itemPriceList)) {
            logger.info("根据门店编码、连锁编码 查询第三方价格为空, 不做对比操作 :{} :{}", comId, busNo);
            return;
        }
        this.priceCenterFromBusNoCommon(itemPriceList);
    }

    private void priceCenterFromBusNoCommon(List<ItemPrice> itemPriceList) {
        //将列表进行 5000 分组
        List<List<ItemPrice>> itemPriceLists = Lists.partition(itemPriceList, 5000);

        itemPriceLists.forEach(itemPriceList1 -> {

            asyncTaskExecutor.execute(() -> {

                //将列表进行 500 分组
                List<List<ItemPrice>> itemPriceLists1 = Lists.partition(itemPriceList1, 50);
                itemPriceLists1.forEach(this::noticePriceCenterTwo);
            });
        });
    }

    @Override
    public void posFromBusNo(CompareOneselfMessage message) {
        String comId = message.getComId();
        String busNo = message.getBusNo();
        Long storeId = message.getStoreId();
        Long businessId = message.getBusinessId();
        //校验参数
        Assert.isTrue(StringUtils.isNotEmpty(comId), "连锁编码不可以为空");
        Assert.isTrue(StringUtils.isNotEmpty(busNo), "门店编码不可以为空");
        Assert.isTrue(Objects.nonNull(businessId), "businessId不可以为空");
        Assert.isTrue(Objects.nonNull(storeId), "storeId不可以为空");

        MdmStoreBaseDTO mdmStoreBaseDTO = thirdService.getMdmStoreByStoreNo(busNo);
        doCompareStorePrice(mdmStoreBaseDTO, false);
    }

    @Override
    public void noticePriceCenterTwo(List<ItemPrice> storeItemPriceList) {
        if (CollectionUtils.isEmpty(storeItemPriceList)) {
            return;
        }

        CompareOneselfMessage message = new CompareOneselfMessage();
        message.setItemPriceList(storeItemPriceList);
        message.setIsPriceComparison(PriceComparisonEnum.PUSH_PRICE_CENTER.getCode());
        priceCenterComparePriceProducer.sendMessage(message);
        logger.info("三方价格不对比, 通知价格中台, 发送MQ成功");
    }

    @Override
    public void comparePriceByBusinessIdAndStoreIdAndGoodsNo(Long businessId, Long storeId, String goodsNos) {
        Assert.isTrue(businessId != null, "连锁ID不可以为空");
        //通过 连锁进行价格对比
        if (storeId == null){
            String comId = thirdService.getComId(businessId);
            priceCenterCompareFromComId(comId);
            return;
        }
        boolean isHasGoods = org.apache.commons.lang3.StringUtils.isNotEmpty(goodsNos);
        if (isHasGoods){
            List<String> goodsList = Arrays.asList(goodsNos.split(","));
            priceCenterCompareFromGoods(businessId,storeId,goodsList);
        }else{
            MdmDataTransformDTO mdmDataTransformDTO = new MdmDataTransformDTO();
            mdmDataTransformDTO.setTransFormType(1);
            mdmDataTransformDTO.setBusinessId(String.valueOf(businessId));
            mdmDataTransformDTO.setStoreIds(Lists.newArrayList(storeId));
            mdmDataTransformDTO.setDataType(2);
            MdmDataTransformDTO mdmStoreInfo = storeFeignService.transformMdmData(mdmDataTransformDTO);
            priceCenterCompareFromBusNo(mdmStoreInfo.getComId(),mdmStoreInfo.getStoreNos().get(0));
        }
    }

    @Override
    public void exportByComId(String comId) {
        Assert.isTrue(StringUtils.isNotEmpty(comId), "连锁编码不可以为空");

        if (comparePriceDistribute) {
            exportByComIdDistribute(comId, false);
            return;
        }

        List<MdmStoreBaseDTO> mdmStoreBaseDTOList = thirdService.findMdmStoreByComId(comId);
        logger.info("根据连锁编码 获取连锁门店信息, 连锁编码 :{},门店数量 :{}", comId, mdmStoreBaseDTOList == null ? 0 : mdmStoreBaseDTOList.size());
        if (CollectionUtils.isEmpty(mdmStoreBaseDTOList)) {
            logger.error("根据连锁编码 未查询到连锁ID 连锁编码:{} ", comId);
            throw new BusinessErrorException("根据连锁编码 未查询到连锁ID");
        }


        if (CollectionUtils.isNotEmpty(mdmStoreBaseDTOList)) {
            AtomicReference<Long> businessIdRef = new AtomicReference<>();
            List<PricePushDTO> allDiffList = Collections.synchronizedList(new ArrayList<>());
            List<PriceCompareStatistic> statisticList = Collections.synchronizedList(new ArrayList<>());
            List<String> priceComparePosBlackList = getPriceComparePosBlackList();
            CountDownLatch countDownLatch = new CountDownLatch(mdmStoreBaseDTOList.size());
            for (MdmStoreBaseDTO storeBaseDTO : mdmStoreBaseDTOList) {
                String busNo = storeBaseDTO.getStoreNo();
                Long storeId = storeBaseDTO.getStoreId();
                if (priceComparePosBlackList.contains(storeId.toString())) {
                    countDownLatch.countDown();
                    continue;
                }

                businessIdRef.set(storeBaseDTO.getBusinessId());
                businessComparePriceExecutor.execute(() -> {
                    try {
                        doCompareStorePrice(comId, businessIdRef.get(), allDiffList, statisticList, storeBaseDTO, busNo, storeId);
                    } finally {
                        countDownLatch.countDown();
                    }
                });
            }

            Long businessId = businessIdRef.get();
            try {
                countDownLatch.await();
            } catch (Exception e) {
                logger.error("exportByComId 执行异常, businessId:{},Exception:{}", businessId, e);
            }
            logger.debug("exportByComId 比对, comId:{}", comId);

            sendEmail(comId, allDiffList, statisticList, businessId);
        }
    }

    private void sendEmail(String comId, List<PricePushDTO> allDiffList, List<PriceCompareStatistic> statisticList, Long businessId) {
        // 发送邮件
        String date = DateUtil.dateToStr(new Date(), DateUtil.DATE_FORMAT);
        String subject = "【价格中台】" + businessId + "-" + comId + "_线上线下价格对比_" + date;
        if (CollectionUtils.isNotEmpty(allDiffList) && StringUtils.isNotBlank(priceCompareResultSendEmail)) {

            StringBuilder content = new StringBuilder("<html><head></head><body><h3>"+ subject +"</h3>");
            content.append("<table border=\"5\" style=\"border:solid 1px #E8F2F9;font-size=14px;;font-size:18px;width:700px;text-align:center\">");
            content.append("<tr><td>连锁id</td><td>连锁编码</td><td>门店ID</td><td>门店编码</td><td>门店名称</td><td>差异数</td></tr>");

            statisticList.forEach(statistic -> {
                content.append("<tr>");
                content.append("<td>").append(businessId).append("</td>");
                content.append("<td>").append(statistic.getComId()).append("</td>");
                content.append("<td>").append(statistic.getStoreId()).append("</td>");
                content.append("<td>").append(statistic.getStoreNo()).append("</td>");
                content.append("<td>").append(statistic.getStoreName()).append("</td>");
                content.append("<td>").append(statistic.getDiffCount()).append("</td>");
                content.append("</tr>");
            });
            content.append("</table>");
            content.append("<h3>差异明细见附件</h3>");
            content.append("</body></html>");

            String[] to = getEmailRecipient(businessId, null);
            try {
                ByteArrayOutputStream bos = new ByteArrayOutputStream();
                ExcelUtil.listToExcel(allDiffList, getExcelColumnTitle(), "sheet1",0,  bos);
                String attachName = businessId + "_" + comId + "_" + date + "_detail.xls";
                EmailUtils.sendMail(to, subject, content.toString(), bos, attachName);
            } catch (Exception e) {
                logger.error("价格比对邮件任务执行失败, 收件人：{}", to, e);
            }
        } else if (CollectionUtils.isEmpty(allDiffList) && StringUtils.isNotBlank(priceCompareResultSendEmail)) {
            String[] to = getEmailRecipient(businessId, null);
            try {
                //EmailUtils.sendMail(to, subject, "当前连锁无差异");
            } catch (Exception e) {
                logger.error("价格比对邮件任务执行失败, 收件人：{}", to, e);
            }
        }
    }

    @Override
    public void exportByComIdDistribute(String comId, boolean priceRepair) {
        Assert.isTrue(StringUtils.isNotEmpty(comId), "连锁编码不可以为空");

        List<MdmStoreBaseDTO> mdmStoreBaseDTOList = thirdService.findMdmStoreByComId(comId);
        logger.info("根据连锁编码 获取连锁门店信息, 连锁编码 :{},门店数量 :{}", comId, mdmStoreBaseDTOList == null ? 0 : mdmStoreBaseDTOList.size());
        if (CollectionUtils.isEmpty(mdmStoreBaseDTOList)) {
            logger.error("根据连锁编码 未查询到连锁ID 连锁编码:{} ", comId);
            throw new BusinessErrorException("根据连锁编码 未查询到连锁ID");
        }
        Integer version = generateCompareVersion();
        mdmStoreBaseDTOList.forEach(storeBaseDTO -> {
            storeBaseDTO.set_version(version);

            CompareOneselfMessage message = new CompareOneselfMessage();
            message.setMdmStoreBaseDTO(storeBaseDTO);
            message.setIsPriceComparison(PriceComparisonEnum.BAM_COMPARE_STORE_PRICE.getCode());
            message.setPriceRepair(priceRepair);
            priceCenterComparePriceProducer.sendMessageToCompareTaskSplit(message);
        });

    }

    private int generateCompareVersion() {
        return Integer.parseInt(DateUtil.dateToStr(new Date(), "yyMMddHHmm").substring(0, 9));
    }

    @Override
    public void doCompareStorePrice(MdmStoreBaseDTO storeBaseDTO, boolean priceRepair) {
        if (Objects.isNull(storeBaseDTO)) {
            return;
        }
        String comId = storeBaseDTO.getComId(), busNo = storeBaseDTO.getStoreNo();
        Long businessId = storeBaseDTO.getBusinessId(), storeId = storeBaseDTO.getStoreId();
        logger.info("doCompareStorePrice,storeId:{}", storeId);
        Integer version = storeBaseDTO.get_version();
        boolean compareAll = true;
        if (Objects.isNull(version)) {
            compareAll = false;
            version = generateCompareVersion();
        }
        String key = RedisKeysConstant.PRICE_COMPARE_COMPLETED_STORE_CACHE_KEY + comId + "_" + version;

        // 黑名单门店过滤
        List<String> priceComparePosBlackList = getPriceComparePosBlackList();
        if (priceComparePosBlackList.contains(storeId.toString())) {
            putStoreNo(compareAll, key, busNo);
            return;
        }

        //查询海典/邦建 价格
        List<ItemPrice> goodsNoList = hdDataService.queryPriceGoodsNoByComIdAndBusNo(comId, busNo);
        logger.info("doCompareStorePrice,storeId:{},查询海典视图结束", storeId);

        if (CollectionUtils.isEmpty(goodsNoList)) {
            logger.info("根据门店编码、连锁编码 查询第三方价格为空, 不做对比操作 :{} :{}", comId, busNo);
            putStoreNo(compareAll, key, busNo);
            return;
        }
        List<String> goodsNos = goodsNoList.stream().map(ItemPrice::getWarecode).collect(Collectors.toList());
        List<List<String>> goodsNosPartition = Lists.partition(goodsNos, 50);
        CountDownLatch countDownLatch = new CountDownLatch(goodsNosPartition.size());
        Integer finalVersion = version;
        goodsNosPartition.forEach(list -> comparePriceByGoodsExecutor.execute(() -> {
            logger.info("doCompareStorePrice,storeId:{}，任务拆分", storeId);
            try {
                // 查询POS价格
                List<ItemPrice> posPriceList = hdDataService.queryPriceByComIdAndBusNoAndGoods(comId, busNo, list);
                // 查询价格中台价格
                List<ItemPriceResponse> priceCenterPriceList = this.getPriceCenterGoodsPrice(businessId, storeId, list, pushPosChannelId, 3);
                // 进行价格对比
                this.doComparePrice(posPriceList, priceCenterPriceList, businessId, storeId, finalVersion, priceRepair);
            } catch (Exception e) {
                logger.error("doCompareStorePrice 任务拆分执行异常, storeId:" + storeId, e);
            } finally {
                countDownLatch.countDown();
            }
        }));
        try {
            countDownLatch.await();
        } catch (Exception e) {
            logger.error("doCompareStorePrice countDownLatch 执行异常, businessId:{}, storeId:{}", businessId, storeId, e);
        }

        putStoreNo(compareAll, key, busNo);
        logger.info("doCompareStorePrice,storeId:{},比对完成", storeId);
        // 导出
        if (compareAll && isComplete(key, comId)) {
            exportCompareDataByVersion(comId, businessId, version, SyncTypeEnum.PRICE);
        }
    }

    public void exportCompareDataByVersion(String comId, Long businessId, Integer version, SyncTypeEnum syncTypeEnum) {
        List<DiffDataCompareInfoWithBLOBs> diffDataCompareInfos = new ArrayList<>();
        Long maxId = 0L;
        while (true) {
            DiffDataCompareInfoExample example = new DiffDataCompareInfoExample();
            example.createCriteria().andBusinessIdEqualTo(businessId)
                .andDataTypeEqualTo(syncTypeEnum.getCode())
                .andVersionEqualTo(version)
                .andIdGreaterThan(maxId)
                .andGmtCreateGreaterThan(DateUtil.parse(DateUtil.dateToStr(new Date(), "yyyy-MM-dd") + " 00:00:00", DateUtil.TIME_FORMAT));
            example.setOrderByClause("id");
            example.setLimitStart(1000);
            List<DiffDataCompareInfoWithBLOBs> list = diffDataCompareInfoMapper.selectByExampleWithBLOBs(example);
            if (CollectionUtils.isEmpty(list)) {
                break;
            }
            maxId = list.get(list.size() - 1).getId();
            diffDataCompareInfos.addAll(list);
        }
        if (CollectionUtils.isEmpty(diffDataCompareInfos)) {
            logger.info("doCompareStorePrice,比对完成:{}，开始发送无差异邮件", comId);
            //sendEmail(comId, null, null, businessId);
        } else {
            logger.info("doCompareStorePrice,比对完成:{}，开始发送差异邮件,差异总数:{}", comId, diffDataCompareInfos.size());
            transDataAndSendEmail(comId, businessId, version, diffDataCompareInfos);
        }
    }

    private void putStoreNo(boolean all, String key, String busNo) {
        if (!all) {
            return;
        }
        redissonCacheService.sadd(key, busNo);
        redissonClient.getSet(key).expire(24, TimeUnit.HOURS);
    }

    private void transDataAndSendEmail(String comId, Long businessId, Integer version, List<DiffDataCompareInfoWithBLOBs> diffDataCompareInfos) {
        List<PriceCompareStatistic> statisticList = new ArrayList<>();
        DiffDataRequestDTO query = new DiffDataRequestDTO();
        query.setBusinessId(businessId);
        query.setDataType(SyncTypeEnum.PRICE.getCode());
        query.setVersion(version);
        query.setGmtCreateStart(DateUtil.parse(DateUtil.dateToStr(new Date(), "yyyy-MM-dd") + " 00:00:00", DateUtil.TIME_FORMAT));
        List<DiffCollectDataResponseDTO> diffCollect = diffDataCompareInfoMapper.selectDiffData(query);
        List<MdmStoreBaseDTO> mdmStoreBaseDTOList = thirdService.findMdmStoreByComId(comId);
        Map<Long, MdmStoreBaseDTO> collect = mdmStoreBaseDTOList.stream().collect(Collectors.toMap(MdmStoreBaseDTO::getStoreId, v -> v));
        diffCollect.forEach(d -> {
            PriceCompareStatistic statistic = new PriceCompareStatistic();
            BeanUtils.copyProperties(d, statistic);
            statistic.setDiffCount(d.getCount().intValue());
            MdmStoreBaseDTO mdmStoreBaseDTO = collect.get(d.getStoreId());
            if (Objects.isNull(mdmStoreBaseDTO)) {
                mdmStoreBaseDTO = thirdService.getMdmStoreByStoreId(d.getStoreId());
            }
            if (Objects.nonNull(mdmStoreBaseDTO)) {
                statistic.setComId(mdmStoreBaseDTO.getComId());
                statistic.setStoreNo(mdmStoreBaseDTO.getStoreNo());
                statistic.setStoreName(mdmStoreBaseDTO.getStoreName());
            } else {
                logger.info("transDataAndSendEmail获取连锁门店信息,连锁编码:{},门店ID:{},未查询到数据", comId, d.getStoreId());
            }
            statisticList.add(statistic);
        });

        List<PricePushDTO> allDiffList = diffDataCompareInfos.stream().map(d -> {
            PricePushDTO pricePushDTO = new PricePushDTO();
            pricePushDTO.setBusinessId(d.getBusinessId());
            pricePushDTO.setStoreId(d.getStoreId());
            pricePushDTO.setReason(d.getReason());
            pricePushDTO.setGoodsNo(d.getGoodsNo());
            pricePushDTO.setChannelId(0);

            String price = null, posPrice = null, priceTypeCode = null;
            String ourData = d.getOurData();
            if (StringUtils.isNotBlank(ourData)) {
                PriceDataInfoDTO priceDataInfoDTO = JSON.parseObject(ourData, PriceDataInfoDTO.class);
                price = priceDataInfoDTO.getPrice();
                priceTypeCode = priceDataInfoDTO.getPriceTypeCode();
            }
            String thirdData = d.getThirdData();
            if (StringUtils.isNotBlank(thirdData)) {
                ThridPriceInfoDTO thridPriceInfoDTO = JSON.parseObject(thirdData, ThridPriceInfoDTO.class);
                posPrice = thridPriceInfoDTO.getPrice();
                if (StringUtils.isBlank(priceTypeCode)) {
                    PriceTypeEnum priceTypeEnum = PriceTypeEnum.getByCode(Integer.parseInt(thridPriceInfoDTO.getType()));
                    if (Objects.nonNull(priceTypeEnum)) {
                        priceTypeCode = priceTypeEnum.getPriceTypeCode();
                    }
                }
            }

            pricePushDTO.setPrice(StringUtils.isBlank(price) ? null : new BigDecimal(price));
            pricePushDTO.setPosPrice(StringUtils.isBlank(posPrice) ? null : new BigDecimal(posPrice));
            pricePushDTO.setPriceTypeCode(priceTypeCode);
            String storeNo = collect.containsKey(d.getStoreId()) && StringUtils.isNotEmpty(collect.get(d.getStoreId()).getStoreNo())?collect.get(d.getStoreId()).getStoreNo() : "";
            pricePushDTO.setStoreNo(storeNo);
            return pricePushDTO;
        }).collect(Collectors.toList());
        sendEmail(comId, allDiffList, statisticList, businessId);
    }

    private boolean isComplete(String key, String comId) {
        List<MdmStoreBaseDTO> mdmStoreBaseDTOList = thirdService.findMdmStoreByComId(comId);
        logger.info("根据连锁编码 获取连锁门店信息, 连锁编码 :{},门店数量 :{}", comId, mdmStoreBaseDTOList == null ? 0 : mdmStoreBaseDTOList.size());
        if (CollectionUtils.isEmpty(mdmStoreBaseDTOList)) {
            return false;
        }
        Set<String> storeIds = redissonCacheService.getSet(key);
        for (MdmStoreBaseDTO storeBase : mdmStoreBaseDTOList) {
            String storeNo = storeBase.getStoreNo();
            if (!storeIds.contains(storeNo)) {
                logger.info("isComplete key:{}, storeNo:{}", key, storeNo);
                return false;
            }
        }
        logger.info("isComplete key:{}, 完成", key);
        return true;
    }

    private void doComparePrice(List<ItemPrice> itemPriceList, List<ItemPriceResponse> itemList, Long businessId, Long storeId, Integer version, boolean priceRepair) {

        Map<String, ItemPriceResponse> goodsNoToPriceMap = itemList.stream().collect(Collectors.toMap(ItemPriceResponse::getGoodsNo, item -> item, (v1, v2) -> v2));

        // 需要插入的对比记录
        List<DiffDataCompareInfoWithBLOBs> diffDataCompareInfos = new ArrayList<>();

        // 需要重推的商品价格
        List<ItemPrice> needRetweetItemList = Lists.newArrayList();

        itemPriceList.forEach(itemPrice -> {
            //需要对比的价格类型 1 零售价 2 会员价
//            String compareType = itemPrice.getType();
            PriceTypeEnum priceTypeEnum = PriceTypeEnum.getByCode(Integer.parseInt(itemPrice.getType()));
            if (Objects.isNull(priceTypeEnum)) {
                return;
            }
            String thirdPriceStr = StringUtils.isBlank(itemPrice.getPrice()) ? "0" : itemPrice.getPrice();
//            BigDecimal thirdPriceBigDecimal = new BigDecimal(thirdPriceStr).setScale(Constants.PRICE_SCALE, RoundingMode.FLOOR);
            //价格中台的价格
            ItemPriceResponse itemPriceResponse = goodsNoToPriceMap.get(itemPrice.getWarecode());
//            logger.info("价格中台的价格信息:{}", itemPriceResponse);
            if (itemPriceResponse == null) {
                if ("0".equals(thirdPriceStr)) {
//                    logger.warn("线下{}为:{} compareType:{} 门店:{} 价格为零不做对比和插入", priceTypeEnum.getPriceTypeName(),
//                        thirdPriceBigDecimal, compareType, businessId + "-" + storeId + "-" + itemPrice.getWarecode());
                    return;
                }
                if (priceRepair) {
                    needRetweetItemList.add(itemPrice);
                }
                diffDataCompareInfos.add(this.toDiffDataCompareInfo(businessId, storeId, "未推送价格到中台", itemPrice, itemPriceResponse));
                return;
            }

            //价格类型
            String goodsNo = itemPriceResponse.getGoodsNo();
            String offlinePriceStr = null, onlinePriceStr = null;
            Integer onlineIsHasPrice = null;
            switch (priceTypeEnum) {
                case LSJ:
                    offlinePriceStr = itemPrice.getPrice();
                    onlinePriceStr = itemPriceResponse.getPrice();
                    onlineIsHasPrice = itemPriceResponse.getIsHasPrice();
                    break;
                case HYJ:
                    offlinePriceStr = itemPrice.getPrice();
                    onlinePriceStr = itemPriceResponse.getMemberPrice();
                    onlineIsHasPrice = itemPriceResponse.getIsHasMemberPrice();
                    break;
                case CLJ:
                    offlinePriceStr = itemPrice.getPrice();
                    onlinePriceStr = itemPriceResponse.getPiecePrice();
                    onlineIsHasPrice = itemPriceResponse.getIsHasPiecePrice();
                    break;
                case CHYJ:
                    offlinePriceStr = itemPrice.getPrice();
                    onlinePriceStr = itemPriceResponse.getMemberPiecePrice();
                    onlineIsHasPrice = itemPriceResponse.getIsHasMemberPiecePrice();
                    break;
                default:
                    break;
            }

            String reason = priceCompare(offlinePriceStr, onlinePriceStr, priceTypeEnum, goodsNo, onlineIsHasPrice, true);
            if (StringUtils.isNotBlank(reason)) {
                logger.debug("priceCompare返回|offlinePriceStr:{}, onlinePriceStr:{}, priceTypeEnum:{}, goodsNo:{}, onlineHasPrice:{}, reason:{}", offlinePriceStr, onlinePriceStr, priceTypeEnum, goodsNo, onlineIsHasPrice, reason);
                if (priceRepair) {
                    needRetweetItemList.add(itemPrice);
                }
                //需要插入的对比记录
                diffDataCompareInfos.add(this.toDiffDataCompareInfo(businessId, storeId, reason, itemPrice, itemPriceResponse));
            }

        });

        if (CollectionUtils.isNotEmpty(diffDataCompareInfos)) {
            //进行对比记录的 insert
            diffDataCompareInfos.forEach(itemD -> {
                itemD.setVersion(version);
                diffDataCompareInfoMapper.insertSelective(itemD);
            });
            logger.info("价格对比完成 存在不一致的:{}", storeId);
        }

        //发送价格不一致的消息到 价格价格中台
        if (priceRepair && CollectionUtils.isNotEmpty(needRetweetItemList)) {
            this.noticePriceCenter(needRetweetItemList, null, false);
            logger.info("价格对比完成 发送不一致的商品到价格中台更新:{}", storeId);
        }

        if (diffDataCompareInfos.size() > 0) {
            logger.info("价格对比完成 存在不一致的数量:{}", diffDataCompareInfos.size());
        }
    }

    private void doCompareStorePrice(String comId, Long businessId, List<PricePushDTO> allDiffList, List<PriceCompareStatistic> statisticList, MdmStoreBaseDTO storeBaseDTO, String busNo, Long storeId) {
        logger.info("doCompareStorePrice,storeId:{}", storeId);
        //查询海典/邦建 价格
        List<ItemPrice> itemPriceList = hdDataService.queryPriceByComIdAndBusNo(comId, busNo);
        logger.info("doCompareStorePrice,storeId:{},查询海典视图结束", storeId);

        if (CollectionUtils.isEmpty(itemPriceList)) {
            logger.info("根据门店编码、连锁编码 查询第三方价格为空, 不做对比操作 :{} :{}", comId, busNo);
            return;
        }
        //将列表进行 5000 分组
        List<List<ItemPrice>> itemPriceLists = Lists.partition(itemPriceList, 5000);
        List<PricePushDTO> diffList = Collections.synchronizedList(new ArrayList<>());
        CountDownLatch countDownLatch = new CountDownLatch(itemPriceLists.size());
        itemPriceLists.forEach(itemPriceList1 -> asyncTaskExecutor.execute(() -> {
            logger.info("doCompareStorePrice,storeId:{}，任务拆分", storeId);
            // 将列表进行 500 分组
            List<List<ItemPrice>> itemPriceLists1 = Lists.partition(itemPriceList1, 50);
            itemPriceLists1.forEach(itemPriceList2 -> {
                // 需要查询的 商品价格
                List<String> goodsNos = itemPriceList2.stream().distinct().map(ItemPrice::getWarecode).distinct().collect(Collectors.toList());
                // 查询价格中台 价格
                List<ItemPriceResponse> itemPriceResponseList = this.getPriceCenterGoodsPrice(businessId, storeId, goodsNos, pushPosChannelId);
                // 进行价格对比
                this.doComparePrice(itemPriceList2, itemPriceResponseList, businessId, storeId, true, diffList);

            });
            countDownLatch.countDown();
        }));
        try {
            countDownLatch.await();
        } catch (Exception e) {
            logger.error("priceCenterCompareFromBusNoCommon 执行异常, businessId:{}, storeId:{}, Exception:{}", businessId, storeId, e);
        }
        if (CollectionUtils.isNotEmpty(diffList)) {
            PriceCompareStatistic statistic = PriceCompareStatistic.builder()
                .businessId(businessId)
                .comId(storeBaseDTO.getComId())
                .storeId(storeId)
                .storeNo(busNo)
                .storeName(storeBaseDTO.getStoreName())
                .diffCount(diffList.size())
                .build();
            statisticList.add(statistic);
            allDiffList.addAll(diffList);
        }
    }

    @Override
    public void comparePriceSpecialFlag(String comId) {
        Assert.isTrue(StringUtils.isNotEmpty(comId), "连锁编码不可以为空");

        List<MdmStoreBaseDTO> mdmStoreBaseDTOList = thirdService.findMdmStoreByComId(comId);
        logger.info("比对特价标签|根据连锁编码 获取连锁门店信息, 连锁编码 :{},门店数量 :{}", comId, mdmStoreBaseDTOList == null ? 0 : mdmStoreBaseDTOList.size());
        if (CollectionUtils.isEmpty(mdmStoreBaseDTOList)) {
            logger.error("比对特价标签|根据连锁编码 未查询到连锁ID 连锁编码:{} ", comId);
            throw new BusinessErrorException("根据连锁编码 未查询到连锁ID");
        }

        List<String> storeNos = mdmStoreBaseDTOList.stream().map(MdmStoreBaseDTO::getStoreNo).collect(Collectors.toList());


        if (CollectionUtils.isNotEmpty(storeNos)) {
            storeNos.forEach(storeNo -> {
                //发送mq异步处理
                CompareOneselfMessage message = new CompareOneselfMessage();
                message.setComId(comId);
                message.setBusNo(storeNo);
                message.setIsPriceComparison(PriceComparisonEnum.BAM_COMPARE_PRICE_SIGN.getCode());
                priceCenterComparePriceProducer.sendMessageToCompareTaskSplit(message);
                logger.info("比对特价标签|根据连锁编码 获取连锁门店信息,发送MQ成功 连锁编码 :{},门店编码 :{}", comId, storeNo);
            });

        }
    }

    @Override
    public void comparePriceSpecialFlag(String comId, String busNo) {
        // 校验参数
        this.priceCenterCompareFromBusNoCommonCheck(comId, busNo);

        // 查询海典/邦建 价格
        List<ItemPrice> itemPriceList = hdDataService.queryPriceByComIdAndBusNo(comId, busNo);

        if (CollectionUtils.isEmpty(itemPriceList)) {
            logger.info("根据门店编码、连锁编码 查询第三方价格为空, 不做对比操作 :{} :{}", comId, busNo);
            return;
        }
        this.comparePriceSpecialFlag(itemPriceList, comId, busNo);
    }

    @Override
    public void comparePriceSpecialFlag(String comId, String busNo, List<String> goodsNos) {
        // 校验参数
        this.priceCenterCompareFromBusNoCommonCheck(comId, busNo);

        // 查询海典/邦建 价格
        List<ItemPrice> posPriceList = hdDataService.queryPriceByComIdAndBusNoAndGoods(comId, busNo, goodsNos);

        if (CollectionUtils.isEmpty(posPriceList)) {
            logger.info("根据门店编码、连锁编码 查询第三方价格为空, 不做对比操作 :{} :{}", comId, busNo);
            return;
        }
        String[] businessIdAndStoreIdStrArr = this.getBusinessIdAndStoreIdCommon(comId, busNo);
        Long businessId = Long.parseLong(businessIdAndStoreIdStrArr[0]);
        Long storeId = Long.parseLong(businessIdAndStoreIdStrArr[1]);
        // 查询价格中台价格
        List<ItemPriceResponse> itemPriceResponseList = this.getPriceCenterGoodsPrice(businessId, storeId, goodsNos, pushPosChannelId, 3);
        this.doComparePriceSpecialFlag(posPriceList, itemPriceResponseList, businessId, storeId);
    }

    private void comparePriceSpecialFlag(List<ItemPrice> posPriceList, String comId, String busNo) {
        String[] businessIdAndStoreIdStrArr = this.getBusinessIdAndStoreIdCommon(comId, busNo);

        Long businessId = Long.parseLong(businessIdAndStoreIdStrArr[0]);
        Long storeId = Long.parseLong(businessIdAndStoreIdStrArr[1]);

        List<List<ItemPrice>> itemPriceList = Lists.partition(posPriceList, 5000);

        itemPriceList.forEach(splitList -> {
            List<List<ItemPrice>> splitPosPriceList = Lists.partition(splitList, 50);
            splitPosPriceList.forEach(itemPrices -> {
//                logger.info("单个门店进行价格比对, 任务拆解:{}", busNo);
                // 需要查询的 商品价格
                List<String> goodsNos = itemPrices.stream().distinct().map(ItemPrice::getWarecode).distinct().collect(Collectors.toList());

                // 查询价格中台 价格
                List<ItemPriceResponse> itemPriceResponseList = this.getPriceCenterGoodsPrice(businessId, storeId, goodsNos, pushPosChannelId, 3);

                // 进行价格对比以及补发更新
                this.doComparePriceSpecialFlag(itemPrices, itemPriceResponseList, businessId, storeId);
            });
        });
    }

    private void doComparePriceSpecialFlag(List<ItemPrice> posPriceList, List<ItemPriceResponse> itemList, Long businessId, Long storeId) {
        Map<String, ItemPriceResponse> goodsNoToPriceMap = itemList.stream().collect(Collectors.toMap(ItemPriceResponse::getGoodsNo, item -> item, (v1, v2) -> v2));

        // 需要重推的额商品价格
        List<PricePushDTO> pricePushDTOList = new ArrayList<>();

        posPriceList.forEach(itemPrice -> {
            ItemPriceResponse itemPriceResponse = goodsNoToPriceMap.get(itemPrice.getWarecode());
            doComparePriceSpecialFlag(businessId, storeId, itemPriceResponse, pricePushDTOList, itemPrice);
        });

        // 发送价格不一致的消息到 价格价格中台
        if (CollectionUtils.isNotEmpty(pricePushDTOList)) {
            this.noticePriceCenter(pricePushDTOList);
            logger.info("价格特价标识对比完成 发送不一致的商品到价格中台更新:{}", storeId);
        }
        if (pricePushDTOList.size() > 0) {
            logger.info("价格特价标识对比完成 存在不一致的数量:{}", pricePushDTOList.size());
        }
    }

    private void doComparePriceSpecialFlag(Long businessId, Long storeId, ItemPriceResponse itemPriceResponse,
                                           List<PricePushDTO> pricePushDTOList, ItemPrice itemPrice) {
        Integer posPriceIsSpecial = itemPrice.getIsSpecial();

        if (Objects.isNull(itemPriceResponse)) {
            return;
        }
        if (itemPriceResponse.getIsHasPrice() == 1 && !posPriceIsSpecial.equals(itemPriceResponse.getIsSpecial())) {
            pricePushDTOList.add(buildPushDTO(businessId, storeId, PriceTypeEnum.LSJ, itemPrice.getWarecode(), "线下POS特价标识与中台零售价特价标识不一致", posPriceIsSpecial));
        }
        if (itemPriceResponse.getIsHasMemberPrice() == 1 && !posPriceIsSpecial.equals(itemPriceResponse.getMemberPriceIsSpecial())) {
            pricePushDTOList.add(buildPushDTO(businessId, storeId, PriceTypeEnum.HYJ, itemPrice.getWarecode(), "线下POS特价标识与中台会员价特价标识不一致", posPriceIsSpecial));
        }
    }

    private PricePushDTO buildPushDTO(Long businessId, Long storeId, PriceTypeEnum priceTypeEnum, String goodsNo, String reason, Integer isSpecial) {
        PricePushDTO pricePushDTO = buildPushDTO(businessId, storeId, null, priceTypeEnum, goodsNo, null, reason);
        pricePushDTO.setIsSpecial(isSpecial);
        return pricePushDTO;
    }

    public void noticePriceCenter(List<PricePushDTO> pricePushDTOList) {

        CompareOneselfMessage message = new CompareOneselfMessage();
        if (CollectionUtils.isEmpty(pricePushDTOList)) {
            return;
        }
        List<PricePushDTO> list = pricePushDTOList.stream()
            .collect(Collectors.collectingAndThen(Collectors.toCollection(() -> new TreeSet<>(Comparator.comparing(PricePushDTO::getGoodsNo))), ArrayList::new));
        message.setIsPriceComparison(PriceComparisonEnum.COMPARE_PRICE_SIGN.getCode());
        message.setItemList(list);
        priceCenterComparePriceProducer.sendMessage(message);
        logger.info("价格特价标识对比存在不一致, 通知价格中台修改价格, 发送MQ成功，门店：{}, 商品编码：{}", pricePushDTOList.get(0).getStoreId(),
            pricePushDTOList.stream()
            .map(PricePushDTO::getGoodsNo).collect(Collectors.toList()));
    }


}
