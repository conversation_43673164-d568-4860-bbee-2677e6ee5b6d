
package com.cowell.bam.service.impl;

import com.cowell.bam.domain.*;
import com.cowell.bam.entity.SysAction;
import com.cowell.bam.entity.SysActionExample;
import com.cowell.bam.entity.SysDescription;
import com.cowell.bam.entity.SysDescriptionExample;
import com.cowell.bam.repository.mybatis.dao.BamBusinessBillLogDOMapper;
import com.cowell.bam.repository.mybatis.dao.SysActionMapper;
import com.cowell.bam.repository.mybatis.dao.SysDescriptionMapper;
import com.cowell.bam.service.BamBusinessBillLogService;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.sleuth.annotation.NewSpan;
import org.springframework.scheduling.annotation.AsyncResult;
import org.springframework.stereotype.Service;

import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.Future;


/**
 * <AUTHOR>
 * @date: 2018/12/29 11:32
 * @description:
 */

@Service
public class BamBusinessBillLogServiceImpl implements BamBusinessBillLogService {
    @Autowired
    private BamBusinessBillLogDOMapper bamBusinessBillLogDOMapper;
    @Autowired
    private SysDescriptionMapper sysDescriptionMapper;
    @Autowired
    private SysActionMapper sysActionMapper;

    private final Logger log = LoggerFactory.getLogger(BamBusinessBillLogServiceImpl.class);

    /**
     * 保存单据日志
     *
     * @param bamLogDO
     * @return
     */

    @Override
    public int save(BamLogDO bamLogDO) {
        BamBusinessBillLogDO bamBusinessBillLogDO = this.transBillLogDo(bamLogDO);
        int insert = bamBusinessBillLogDOMapper.insert(bamBusinessBillLogDO);
        return insert;
    }

    @Override
    @NewSpan("bam.BamBusinessBillLogServiceImpl.batchInsert")
    public Future<Boolean> batchInsert(List<BamLogDO> bamLogDOList) {
        List<BamBusinessBillLogDO> record = new ArrayList<>();
        for (BamLogDO bamLogDO : bamLogDOList) {
            BamBusinessBillLogDO bamBusinessBillLogDO = this.transBillLogDo(bamLogDO);
            record.add(bamBusinessBillLogDO);
        }
        int result = bamBusinessBillLogDOMapper.batchInsert(record);
        return new AsyncResult<>(result > 0);
    }

    @Override
    public List<BamReturnDO> selectByCompanyAndStoreId(BamQueryDO bamQueryDO) throws Exception {

        List<BamReturnDO> returnDOList = new ArrayList<>();
        //先根据companyId和storeId，businessDate倒序,取出billId去重后取出前五条
        //在根据billId查询各个列表的展示项
        List<String> billIdList = bamBusinessBillLogDOMapper.selectTop5ByCompanyAndStoreId(bamQueryDO);
        log.info("top5 billId:{}", billIdList);
        if (CollectionUtils.isNotEmpty(billIdList)) {
            Map<String, String> desMap = getSysDescMap();
            Map<String, String> actMap = getSysActionMap();
            BamReturnDO returnDO;
            List<BamTransDO> transDOList;
            for (String billId : billIdList) {
                List<BamBusinessBillLogDO> businessBillLogDOList = bamBusinessBillLogDOMapper.selectByBillId(billId);
                returnDO = new BamReturnDO();
                returnDO.setBillId(billId);
                transDOList = transDO(businessBillLogDOList, desMap, actMap);
                returnDO.setBamTransDOList(transDOList);
                returnDOList.add(returnDO);
            }
        }
        log.info("selectByCompanyAndStoreId处理结果:{}", returnDOList);
        return returnDOList;
    }

    private List<BamTransDO> transDO(List<BamBusinessBillLogDO> logDOList, Map<String, String> desMap, Map<String, String> actMap) throws Exception {
        List<BamTransDO> doList = new ArrayList<>();
        BamTransDO transDO;
        for (BamBusinessBillLogDO logDO : logDOList) {
            transDO = new BamTransDO();
            transDO.setBusinessDate(transBusinessDate(logDO.getBusinessDate() + ""));
            transDO.setSendSysName(desMap.get(logDO.getSendSysId() + ""));
            transDO.setReceiveSysName(desMap.get(logDO.getReceiveSysId() + ""));
            transDO.setActionName(actMap.get(logDO.getStepId().toString()));
            transDO.setStatus(logDO.getStatus() == 1 ? "成功" : "失败");
            doList.add(transDO);
        }
        return doList;
    }

    private Map<String, String> getSysDescMap() {
        Map<String, String> descMap = new HashMap<>();
        List<SysDescription> descriptionList = sysDescriptionMapper.selectByExample(new SysDescriptionExample());
        if (CollectionUtils.isNotEmpty(descriptionList)) {
            descriptionList.forEach(desDO -> descMap.put(desDO.getSysId(), desDO.getSysDesc()));
        }
        return descMap;
    }

    private Map<String, String> getSysActionMap() {
        Map<String, String> actionMap = new HashMap<>();
        List<SysAction> actionList = sysActionMapper.selectByExample(new SysActionExample());
        if (CollectionUtils.isNotEmpty(actionList)) {
            actionList.forEach(actDO -> actionMap.put(actDO.getZstepId(), actDO.getZstepAct()));
        }
        return actionMap;
    }

    private String transBusinessDate(String businessDate) throws Exception {
        SimpleDateFormat sdf1 = new SimpleDateFormat("yyyyMMddhhmmss");
        Date date = sdf1.parse(businessDate);
        SimpleDateFormat sdf2 = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        return sdf2.format(date);
    }

    /**
     * 转换实体
     *
     * @param bamLogDO
     * @return
     */

    private BamBusinessBillLogDO transBillLogDo(BamLogDO bamLogDO) {
        BamBusinessBillLogDO bamBusinessBillLogDO = new BamBusinessBillLogDO();
        try {
            bamBusinessBillLogDO.setGuId(bamLogDO.getZGUID());
            bamBusinessBillLogDO.setBillId(StringUtils.isEmpty(bamLogDO.getZTRACEID()) ? "" : bamLogDO.getZTRACEID());
            bamBusinessBillLogDO.setFlowId(bamLogDO.getZFLOWID() == null ? 0 : Integer.parseInt(bamLogDO.getZFLOWID() + ""));
            bamBusinessBillLogDO.setStepId(bamLogDO.getZSTEPID() == null ? 0 : Integer.parseInt(bamLogDO.getZSTEPID() + ""));
            bamBusinessBillLogDO.setSendSysId(bamLogDO.getZSENDSYSID() == null ? 0 : Integer.parseInt(bamLogDO.getZSENDSYSID() + ""));
            bamBusinessBillLogDO.setReceiveSysId(bamLogDO.getZRECSYSID() == null ? 0 : Integer.parseInt(bamLogDO.getZRECSYSID() + ""));
            bamBusinessBillLogDO.setCompanyId(StringUtils.isEmpty(bamLogDO.getZCOMID()) ? "" : bamLogDO.getZCOMID());
            bamBusinessBillLogDO.setStoreId(StringUtils.isEmpty(bamLogDO.getZSTOREID()) ? "" : bamLogDO.getZSTOREID());
            bamBusinessBillLogDO.setTimestamp(StringUtils.isEmpty(bamLogDO.getZTIMESTAMP()) ? "" : bamLogDO.getZTIMESTAMP());
            bamBusinessBillLogDO.setBusinessDate(StringUtils.isEmpty(bamLogDO.getZBUSINESSDATE()) ? "" : bamLogDO.getZBUSINESSDATE());
            bamBusinessBillLogDO.setItemCount(bamLogDO.getZITEMCOUNT() == null ? 0 : Integer.parseInt(bamLogDO.getZITEMCOUNT() + ""));
            bamBusinessBillLogDO.setStatus(bamLogDO.getZSTATUS() == null ? 0 : Integer.parseInt(bamLogDO.getZSTATUS() + ""));
            bamBusinessBillLogDO.setMessage(StringUtils.isEmpty(bamLogDO.getZMESSAG()) ? "" : bamLogDO.getZMESSAG());
            bamBusinessBillLogDO.setExtend1(StringUtils.isEmpty(bamLogDO.getZEXTEND1()) ? "" : bamLogDO.getZEXTEND1());
            bamBusinessBillLogDO.setExtend2(StringUtils.isEmpty(bamLogDO.getZEXTEND2()) ? "" : bamLogDO.getZEXTEND2());
            bamBusinessBillLogDO.setExtend3(StringUtils.isEmpty(bamLogDO.getZEXTEND3()) ? "" : bamLogDO.getZEXTEND3());
            bamBusinessBillLogDO.setExtend4(StringUtils.isEmpty(bamLogDO.getZEXTEND4()) ? "" : bamLogDO.getZEXTEND4());
            bamBusinessBillLogDO.setGmtCreate(new Date());
            bamBusinessBillLogDO.setEnv(bamLogDO.getEnv());
        }catch (Exception e){
            log.warn("bamBusinessBillLogDO#实体转换异常;TRACEIDId:{};StepId:{};SendSysId:{};ReceiveSysId:{}"
                ,bamLogDO.getZTRACEID(),bamLogDO.getZSTEPID(),bamLogDO.getZSENDSYSID(),bamLogDO.getZRECSYSID(),e);
        }
        return bamBusinessBillLogDO;
    }

}

