package com.cowell.bam.service.impl;

import com.cowell.bam.domain.HanaBdpDO;
import com.cowell.bam.kafka.HanaBdpKafkaProducer;
import com.cowell.bam.repository.JdbcHanaRepository;
import com.cowell.bam.service.EsBdpDataService;
import com.cowell.bam.service.HanaDataService;
import org.apache.commons.collections.CollectionUtils;
import org.joda.time.LocalDateTime;
import org.redisson.api.RBucket;
import org.redisson.api.RedissonClient;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date: 2018/12/27 20:31
 * @description:
 */
@Service
public class HanaDataServiceImpl implements HanaDataService {
    private Logger log = LoggerFactory.getLogger(HanaDataServiceImpl.class);
    @Autowired
    private JdbcHanaRepository jdbcHanaRepository;
    @Autowired
    private HanaBdpKafkaProducer hanaBdpKafkaProducer;
    @Resource
    RedissonClient redissonClient;
    private String redisKey="bam-log-hana-redis-key";
    @Autowired
    private EsBdpDataService esBdpDataService;

    @Value("${data_source_env}")
    private String dataSourceEnv;
    @Override
    public void getHanaBdpData(){
        String key = redisKey + "-" + dataSourceEnv;
        RBucket rBucket = redissonClient.getBucket(key);
        try {
            String endTime = LocalDateTime.now().toString("yyyyMMddHHmmSS");
            //总条数
            Long totalSize = jdbcHanaRepository.selectCount();
            //总页数
            int pageSize = 1000;
            Long pages = (totalSize + pageSize - 1) / pageSize;
            List<HanaBdpDO> hanaBDPResult = new ArrayList<>();
            log.info("HanaDataServiceImpl#getHanaBdpData,总页数{}",pages);
            for (int i = 0; i <pages ; i++) {
                int page = i;
                 hanaBDPResult = searchHanaDataList(rBucket,endTime,page,pageSize);
                //向kafka发送数据
                log.info("HanaDataServiceImpl#getHanaBdpData,发送数据的条数{}",hanaBDPResult.size());
                if(CollectionUtils.isNotEmpty(hanaBDPResult)){
                    hanaBdpKafkaProducer.send(hanaBDPResult);
                }
            }

            //把最新一条数据时间戳放入缓存
            if(CollectionUtils.isNotEmpty(hanaBDPResult)){
                rBucket.set(endTime);
            }

        } catch (Exception e) {
            log.warn("HanaDataServiceImpl#getHanaBdpData执行任务失败{}",e);
        }
    }
    /**
     * 通过开始结束时间获取数据
     * @param rBucket
     * @return
     * @throws Exception
     */
    private List<HanaBdpDO> searchHanaDataList(RBucket rBucket,String endTime,Integer page,Integer pageSize) throws Exception {
        Object redisResult = rBucket.get();
        log.info("HanaDataServiceImpl#searchHabaDataList获取最大时间戳为{}",redisResult);
        String startTime = "";
        if(redisResult!=null){
            startTime = (String)redisResult;
        }
        log.info("HanaDataServiceImpl#searchHabaDataList，开始时间{},结束时间{}",startTime,endTime);
        return jdbcHanaRepository.select(startTime, endTime, page * pageSize, pageSize);
    }


}
