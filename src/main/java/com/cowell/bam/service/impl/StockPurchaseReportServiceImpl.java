package com.cowell.bam.service.impl;

import com.alibaba.fastjson.JSON;
import com.cowell.bam.entity.PurchaseStockMain;
import com.cowell.bam.entity.PurchaseStockDetail;
import com.cowell.bam.service.StockPurchaseReportService;
import com.cowell.bam.web.rest.util.DateUtil;
import com.google.common.collect.Maps;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.jdbc.core.BeanPropertyRowMapper;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Service;

import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

@Service
public class StockPurchaseReportServiceImpl implements StockPurchaseReportService {
    private Logger log = LoggerFactory.getLogger(StockPurchaseReportServiceImpl.class);


    @Qualifier("xbJdbcTemplate")
    @Autowired
    private JdbcTemplate xbJdbcTemplate;


    @Override
    public String HDPurchaseReport(String startDate,String endDate,String busiNo) {
        log.info("StockPurchaseReportServiceImpl|HDPurchaseReport|HDPurchaseReport startDate={},endDate={},busiNo={}",startDate,endDate,busiNo);
        StringBuffer sqlMain = new StringBuffer("SELECT  trans_type,s_ent_name,b_ent_name,trans_date,create_date, report_user,user_link,if_network,")
        .append("s_licence_no,s_ent_type,ADDRESS as s_ent_address,mdm_busno FROM h2.v_shwygj_h where mdm_busno=? ");
        Object[] args;
        if(StringUtils.isNotBlank(startDate)&& StringUtils.isNotBlank(endDate)){
            sqlMain.append(" and trans_date>=? and trans_date<=?");
            args = new Object[]{busiNo, DateUtil.getDate(startDate,DateUtil.TIME_FORMAT), DateUtil.getDate(endDate,DateUtil.TIME_FORMAT)};
        }else{
            args = new Object[]{busiNo};
        }
        log.info("StockPurchaseReportServiceImpl|HDPurchaseReport|main param={}",args);
        List<PurchaseStockMain> mainList = xbJdbcTemplate.query(sqlMain.toString(), args, new BeanPropertyRowMapper<>(PurchaseStockMain.class));
        log.info("StockPurchaseReportServiceImpl|HDPurchaseReport|mainList size={}", mainList.size());
        if(CollectionUtils.isEmpty(mainList)){
            log.error("StockPurchaseReportServiceImpl|HDPurchaseReport|查询海典主数据异常");
            return "海典查询数据不存在";
        }
        Map<String, PurchaseStockMain> mainMap = mainList.stream().filter(t->StringUtils.isNotBlank(t.getS_ent_name()))
                .collect(Collectors.toMap(t -> t.getS_ent_name(), s -> s, (a, b) -> a));

        StringBuffer sqlDetail = new StringBuffer("SELECT  trans_type,s_ent_name,pass_num,goods_name,drug_name,essential_drug,goods_manu,drug_ename,")
                .append("standard,form,alot_num as lot_num,uom,trans_amount,report_amount,mdm_busno from h2.v_shwygj_d where mdm_busno=?");
        if(StringUtils.isNotBlank(startDate)&& StringUtils.isNotBlank(endDate)){
            sqlDetail.append(" and trans_date>=? and trans_date<=?");
        }
        List<PurchaseStockDetail> detailList = xbJdbcTemplate.query(sqlDetail.toString(), args, new BeanPropertyRowMapper<>(PurchaseStockDetail.class));
        log.info("StockPurchaseReportServiceImpl|HDPurchaseReport|detailList size={}", detailList.size());
        if(CollectionUtils.isEmpty(detailList)){
            log.error("StockPurchaseReportServiceImpl|HDPurchaseReport|查询海典明细数据异常");
            return "海典查询数据不存在";
        }
        Map<String, List<PurchaseStockDetail>> detailMap = detailList.stream().filter(t->StringUtils.isNotBlank(t.getS_ent_name()))
                .collect(Collectors.groupingBy(t ->t.getS_ent_name()));
        return convertXml(mainMap,detailMap);
    }

    private String convertXml(Map<String, PurchaseStockMain> mainMap,
                              Map<String, List<PurchaseStockDetail>> detailMap) {
        StringBuffer stringBuffer = new StringBuffer();
        stringBuffer.append("<?xml version=\"1.0\" encoding=\"UTF-8\"?>")
                .append("<data version=\"1.0\" >");
        mainMap.keySet().stream().forEach(key->{
            PurchaseStockMain purchaseStockMain = mainMap.get(key);
            List<PurchaseStockDetail> details =  detailMap.get(key);
            if(CollectionUtils.isNotEmpty(details)){
                toMain(purchaseStockMain,stringBuffer);
                details.stream().forEach(t->toDetail(t,stringBuffer));
            }
        });
        stringBuffer.append("</data>");
        return stringBuffer.toString();
    }

    private void toMain(PurchaseStockMain purchaseStockMain, StringBuffer stringBuffer) {
        stringBuffer.append("<trans_main>");
        Arrays.stream(purchaseStockMain.getClass().getDeclaredFields()).forEach(t->{
            t.setAccessible(true);
            String name = t.getName();
            if(StringUtils.equals(name,"mdm_busno")){
               return;
            }
            stringBuffer.append("<");
            stringBuffer.append(name);
            stringBuffer.append(">");
            try {
                stringBuffer.append(convertSpecinalCharacter(t.get(purchaseStockMain)));
            } catch (IllegalAccessException e) {
                log.error("StockPurchaseReportServiceImpl|HDPurchaseReport|toMain error",e);
            }
            stringBuffer.append("</");
            stringBuffer.append(name);
            stringBuffer.append(">");

        });
        stringBuffer.append("</trans_main>");
    }


    private void toDetail(PurchaseStockDetail purchaseStockDetail, StringBuffer stringBuffer) {
        stringBuffer.append("<trans_detail>");
        Arrays.stream(purchaseStockDetail.getClass().getDeclaredFields()).forEach(t->{
            t.setAccessible(true);
            String name = t.getName();
            if(StringUtils.equals(name,"mdm_busno")){
                return;
            }
            stringBuffer.append("<");
            stringBuffer.append(name);
            stringBuffer.append(">");
            try {
                stringBuffer.append(convertSpecinalCharacter(t.get(purchaseStockDetail)));
            } catch (IllegalAccessException e) {
                log.error("StockPurchaseReportServiceImpl|HDPurchaseReport|toDetail error",e);
            }
            stringBuffer.append("</");
            stringBuffer.append(name);
            stringBuffer.append(">");

        });
        stringBuffer.append("</trans_detail>");
    }

    /**
     * xml特殊字符转义
     * @param o
     * @return
     */
    private String convertSpecinalCharacter(Object  o) {
        if(Objects.isNull(0)){
            return null;
        }
        String str = String.valueOf(o);
        if(StringUtils.contains(str,"&")){
            str = str.replaceAll("&","&amp;");
        }
        if(StringUtils.contains(str,"<")){
            str = str.replaceAll("<","&lt;");
        }
        if(StringUtils.contains(str,">")){
            str = str.replaceAll(">","&gt;");
        }
        if(StringUtils.contains(str,"'")){
            str = str.replaceAll("'","&apos;");
        }
        if(StringUtils.contains(str,"\"")){
            str = str.replaceAll("\"","&quot;");
        }
        return str;
    }

    @Override
    public Map<String, Object> queryHDList(String startDate,String endDate,String busiNo) {
        log.info("StockPurchaseReportServiceImpl|HDPurchaseReport|queryHDList startDate={},endDate={},busiNo={}",startDate,endDate,busiNo);
        Map<String, Object> result = Maps.newHashMap();
        StringBuffer sqlMain = new StringBuffer("SELECT  *  FROM h2.v_shwygj_h  ");
        Object[] args = null;
        if(StringUtils.isNotBlank(startDate)&& StringUtils.isNotBlank(endDate)){
            sqlMain.append(" where mdm_busno=? and trans_date>=? and trans_date<=?");
            args = new Object[]{busiNo, DateUtil.getDate(startDate,DateUtil.TIME_FORMAT), DateUtil.getDate(endDate,DateUtil.TIME_FORMAT)};
        }else if(StringUtils.isNotBlank(busiNo)){
            sqlMain.append(" where mdm_busno=? ");
            args = new Object[]{busiNo};
        }
        List<Map<String, Object>> main ;
        if(StringUtils.isNotBlank(busiNo)){
            main = xbJdbcTemplate.queryForList(sqlMain.toString(),args);
        }else{
            main = xbJdbcTemplate.queryForList(sqlMain.toString());
        }
        result.put("main",main);
        log.info("StockPurchaseReportServiceImpl|HDPurchaseReport|main size={}",main.size());
        StringBuffer sqlDetail = new StringBuffer("SELECT  *  from h2.v_shwygj_d ");
        if(StringUtils.isNotBlank(startDate) && StringUtils.isNotBlank(endDate) && StringUtils.isNotBlank(busiNo)){
            sqlDetail.append("  where mdm_busno=? and trans_date>=? and trans_date<=?");
        }else if( StringUtils.isNotBlank(busiNo)){
            sqlDetail.append("  where mdm_busno=? ");
        }
        List<Map<String, Object>> detail ;
        if(StringUtils.isNotBlank(busiNo)){
            detail = xbJdbcTemplate.queryForList(sqlDetail.toString(),args);
        }else{
            detail = xbJdbcTemplate.queryForList(sqlDetail.toString());
        }
        result.put("detail",detail);
        log.info("StockPurchaseReportServiceImpl|HDPurchaseReport|detail size={}",detail.size());
        return result;
    }
}
