package com.cowell.bam.service.impl;

import com.cowell.bam.entity.ItemPrice;
import com.cowell.bam.mq.CompareOneselfProducer;
import com.cowell.bam.mq.PriceImportProducer;
import com.cowell.bam.mq.message.CompareOneselfMessage;
import com.cowell.bam.service.*;
import com.cowell.bam.service.dto.*;
import com.cowell.bam.service.dto.base.CommonResponse;
import com.cowell.bam.web.rest.util.BigDecimalUtils;
import com.google.common.collect.Lists;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.core.task.AsyncTaskExecutor;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 类说明
 *
 * @Author: liw
 * @Date: 2020-05-23 00:34
 */
@Service
public class PriceCompareAppointStoreServiceImpl implements PriceCompareAppointStoreService {

    private final Logger log = LoggerFactory.getLogger(PriceCompareAppointStoreServiceImpl.class);


    @Autowired
    private ThirdService thirdService;
    @Autowired
    private HdDataService hdDataService;
    @Autowired
    private CompareOneselfProducer compareOneselfProducer;
    @Autowired
    private IPriceCenterFeignService priceCenterFeignService;
    @Autowired
    private PriceImportProducer priceImportProducer;
    @Autowired
    @Qualifier("taskExecutorTrace")
    private AsyncTaskExecutor asyncTaskExecutor;
    @Autowired
    @Qualifier("taskExecutorTrace2")
    private AsyncTaskExecutor asyncTaskExecutor2;



    @Override
    public boolean compare(String paramStr) {
        try {
            if (StringUtils.isEmpty(paramStr)) {
                log.warn("PriceCompareAppointStoreServiceImpl|compare|xxl-job下未配置连锁信息comparePriceConfig:{}", paramStr);
                return false;
            }

            String[] paramArr = paramStr.split(":");

            String comId = paramArr[0];
            List<MdmStoreBaseDTO> mdmStoreBaseDTOList = thirdService.findMdmStoreByComId(comId);
            log.info("|PriceCompareAppointStoreServiceImpl|获取连锁门店信息, comId:{},storeSize={}",comId,mdmStoreBaseDTOList == null? 0:mdmStoreBaseDTOList.size());
            if (CollectionUtils.isEmpty(mdmStoreBaseDTOList)) {
                return true;
            }

            List<String> storeNos = Arrays.asList(paramArr[1].split(","));
            boolean isAllStore = storeNos.stream().anyMatch("0000"::equals);

            if (isAllStore){
                log.info("配置文件中有全门店编码-00000:{}",paramStr);
                storeNos = mdmStoreBaseDTOList.stream().map(MdmStoreBaseDTO::getStoreNo).collect(Collectors.toList());
            }



            for (String storeNo : storeNos) {

                //根据不同的连锁查询不同的视图
                List<ItemPrice> itemPriceList = hdDataService.queryPriceByComIdAndBusNo(comId,storeNo);

                if (CollectionUtils.isEmpty(itemPriceList)) {
                    log.warn("PriceCompareAppointStoreServiceImpl|compare|门店storeNo:{}下bdp没有抽到库存", storeNo);
                    continue;
                }
                //根据门店编码进行 分组
                Map<String, List<ItemPrice>> storePriceListMap = itemPriceList.stream().collect(Collectors.groupingBy(ItemPrice::getBusno));

                storePriceListMap.forEach((key, value) -> {

                    ItemPrice comparePrice = value.stream().findFirst().get();

                    String businessIdAndStoreIdStr = thirdService.getBusinessIdAndStoreId(comparePrice.getCompid(), comparePrice.getBusno());
                    String[] businessIdAndStoreIdStrArr = businessIdAndStoreIdStr.split(":");
                    if (StringUtils.isBlank(businessIdAndStoreIdStrArr[0]) || StringUtils.isBlank(businessIdAndStoreIdStrArr[1])) {
                        log.error("PriceCompareAppointStoreServiceImpl|compare|根据mdm连锁门店编码未查询到businessId或storeId,mdm连锁编码:{},mdm门店编码:{}", comparePrice.getCompid(), comparePrice.getBusno());
                        return;
                    }

                    Long businessId = Long.parseLong(businessIdAndStoreIdStrArr[0]);
                    Long storeId = Long.parseLong(businessIdAndStoreIdStrArr[1]);

                    //将列表进行 500 分组
                    List<List<ItemPrice>> partition1 = Lists.partition(value, 10000);

                    partition1.forEach(item1-> asyncTaskExecutor.execute(()->{
                        //将列表进行 500 分组
                        List<List<ItemPrice>> partition = Lists.partition(item1, 50);
                        partition.forEach(list -> {
                            log.info("进行价格比对任务拆解:{}",comparePrice.getBusno());
                            //价格中心的
                            List<ItemPriceResponse> curGroupListPriceCenter = Lists.newArrayList();
                            List<String> goodsNoList = list.stream().map(ItemPrice::getWarecode).distinct().collect(Collectors.toList());

                            //组装查询商品中台价格数据参数
                            PriceQueryParam priceQueryParam = new PriceQueryParam();
                            priceQueryParam.setBusinessId(businessId);
                            priceQueryParam.setStoreId(storeId);
                            priceQueryParam.setGoodsNoList(goodsNoList);
                            List<ItemPriceResponse> itemListPriceCenter = null;
                            try {




                                CommonResponse<List<PriceQueryGoodsNoDTO>> commonResponse = priceCenterFeignService.getPriceAndMemberPrice(priceQueryParam);

                                if (commonResponse.getCode() == 0 && CollectionUtils.isNotEmpty(commonResponse.getResult())) {
                                    List<PriceQueryGoodsNoDTO> priceQueryGoodsNoDTOList = commonResponse.getResult();
                                    log.info("PriceCompareAppointStoreServiceImpl|compare|根据连锁Id，门店id查询到价格中台的商品:{},{},{}", businessId, storeId, priceQueryGoodsNoDTOList.toString());
                                    itemListPriceCenter = priceQueryGoodsNoDTOList.stream().map(item -> toItemListPriceCenter(item, businessId, storeId)).collect(Collectors.toList());
                                    curGroupListPriceCenter.addAll(itemListPriceCenter);
                                }
                            } catch (Exception e) {
                                log.warn("PriceCompareAppointStoreServiceImpl|compare|根据连锁Id，门店id:{},{}查询价格中台超时或者为空", businessId, storeId);
                            }
                            //通过 mq 将任务分解出去
                            compareOneselfProducer.sendMessage(new CompareOneselfMessage(list,curGroupListPriceCenter,businessId,storeId));
                            //priceCompareService.checkStoreItemPrice(list,curGroupListPriceCenter,businessId,storeId,48);
                            log.info("发送价格比对消息成功:{}",comparePrice.getBusno());
                        });
                    }));
                });

            }
        } catch (Exception e) {
            log.error("PriceCompareServiceImpl|compare|价格比对失败:{}", paramStr,e);
        }
        return true;
    }

    @Override
    public void compareBJView(String param) {

        String[] paramArr = param.split(":");

        String comId = paramArr[0];
        List<MdmStoreBaseDTO> mdmStoreBaseDTOList = thirdService.findMdmStoreByComId(comId);
        log.info("|PriceCompareAppointStoreServiceImpl|compareSelfView｜获取连锁门店信息, comId:{},storeSize={}",comId,mdmStoreBaseDTOList == null? 0:mdmStoreBaseDTOList.size());
        if (CollectionUtils.isEmpty(mdmStoreBaseDTOList)) {
            return;
        }

        List<String> storeNos = Arrays.asList(paramArr[1].split(","));
        boolean isAllStore = storeNos.stream().anyMatch("0000"::equals);

        if (isAllStore){
            log.info("自己对比视图配置文件中有全门店编码-00000:{}",param);
            storeNos = mdmStoreBaseDTOList.stream().map(MdmStoreBaseDTO::getStoreNo).collect(Collectors.toList());
        }

        for (String busNo: storeNos) {
            asyncTaskExecutor.execute(()->{
                try {
                    List<ItemPrice> queryBjPriceData = hdDataService.queryPriceByComIdAndBusNo(comId,busNo);
                    //将列表进行 5000 分组
                    List<List<ItemPrice>> partition1 = Lists.partition(queryBjPriceData, 5000);
                    for (List<ItemPrice> itemPrices : partition1) {
                        asyncTaskExecutor2.execute(()->{
                            CompareOneselfMessage message = new CompareOneselfMessage();
                            message.setItemPriceList(itemPrices);
                            priceImportProducer.sendMessage(message);
                            log.info("定时任务执行-价格查询发送mq成功:{}",busNo);
                        });
                    }
                }catch (Exception e){
                    log.error("|queryStoreGoodsPrice|查询海典商品价格异常",e);
                    e.printStackTrace();
                }
            });
        }
    }

    /**
     *
     * @param dto
     * @param businessId
     * @param storeId
     * @return
     */
    private ItemPriceResponse toItemListPriceCenter(PriceQueryGoodsNoDTO dto,Long businessId,Long storeId){
        ItemPriceResponse itemPriceResponse = new ItemPriceResponse();
        BeanUtils.copyProperties(dto,itemPriceResponse);

        itemPriceResponse.setBusinessId(businessId);
        itemPriceResponse.setStoreId(storeId);
        itemPriceResponse.setPrice(BigDecimalUtils.convertYuanByFen(dto.getPrice()));
        itemPriceResponse.setMemberPrice(BigDecimalUtils.convertYuanByFen(dto.getMemberPrice()));

        return itemPriceResponse;
    }

}
