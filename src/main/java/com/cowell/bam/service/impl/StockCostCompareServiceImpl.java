package com.cowell.bam.service.impl;

import com.alibaba.fastjson.JSON;
import com.cowell.bam.domain.DiffStockDataCompareInfoWithBLOBs;
import com.cowell.bam.repository.mybatis.dao.DiffStockDataCompareInfoMapper;
import com.cowell.bam.service.IStockCenterFeignService;
import com.cowell.bam.service.StockCostCompareService;
import com.cowell.bam.service.dto.StockBatchCodeCostDTO;
import com.cowell.bam.service.dto.StockCostQueryParam;
import com.cowell.bam.service.dto.StockGoodsCountInfo;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.Lists;
import org.apache.commons.collections4.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @ProjectName bam
 * @Description:  批次成本比对
 * @date 2019/10/08 15:18
 */
@Service
public class StockCostCompareServiceImpl implements StockCostCompareService {
    private Logger logger = LoggerFactory.getLogger(StockCostCompareServiceImpl.class);

    @Autowired
    private DiffStockDataCompareInfoMapper diffStockDataCompareInfoMapper;
    @Autowired
    private IStockCenterFeignService stockCenterFeignService;

    public static final Integer PAGESIZE = 100;
    @Override
    public void compare(Long storeId){

        //1.查询批次库存
        List<StockGoodsCountInfo> stockGoodsCountInfoList = Lists.newArrayList();
        //recurrenceStock(stockGoodsCountInfoList,storeId, i);
        ResponseEntity<PageInfo<StockGoodsCountInfo>> pageInfo = stockCenterFeignService.getGoodsBatchCodeList(storeId, 1, PAGESIZE);
        if (pageInfo != null && pageInfo.getBody() != null && CollectionUtils.isNotEmpty(pageInfo.getBody().getList())) {
            stockGoodsCountInfoList.addAll(pageInfo.getBody().getList());

            long total = pageInfo.getBody().getTotal();
            long pageTotal= total / PAGESIZE;
            if(total % PAGESIZE!=0){
                pageTotal += 1;
            }
            for (int i = 2; i <= pageTotal; i++) {
                ResponseEntity<PageInfo<StockGoodsCountInfo>> responseEntity = stockCenterFeignService.getGoodsBatchCodeList(storeId, 1, PAGESIZE);
                if (Objects.isNull(responseEntity)||responseEntity.getBody() == null) {
                    logger.warn("StockCostCompareServiceImpl|compare|循环查询次数:{}",i);
                    continue;
                }
                stockGoodsCountInfoList.addAll(responseEntity.getBody().getList());
            }
        }else {
            logger.warn("StockCostCompareServiceImpl|compare|根据门店查询不到批次库存数据:{}",storeId);
            return;
        }
        logger.info("StockCostCompareServiceImpl|compare|sap批次库存大小size:{}",stockGoodsCountInfoList.size());

        if (CollectionUtils.isEmpty(stockGoodsCountInfoList)) {
            logger.warn("StockCostCompareServiceImpl|compare|批次库存数据为空:{}",stockGoodsCountInfoList);
            return;
        }
        List<String> goodsNoList = stockGoodsCountInfoList.stream().map(StockGoodsCountInfo::getSkuMerchantCode).collect(Collectors.toList());
        //2.查询批次成本库存
        List<StockBatchCodeCostDTO> costResultList = Lists.newArrayList();
        for (List<String> goodsNos : Lists.partition(goodsNoList, PAGESIZE)) {
            StockCostQueryParam param = new StockCostQueryParam();
            param.setStoreId(storeId);
            param.setSkuMerchantCodeList(goodsNos);
            ResponseEntity<List<StockBatchCodeCostDTO>> responseEntity = stockCenterFeignService.getStockCostList(param);
            if (responseEntity != null && responseEntity.getStatusCode().equals(HttpStatus.OK)) {
                costResultList.addAll(responseEntity.getBody());
            }
        }
        logger.info("StockCostCompareServiceImpl|compare|批次成本库存大小size:{}",costResultList.size());
        //3. 比对总库存合格品区 合格品区小库存 待验区 不合格品  待出库区 库存
        Map<String, List<StockBatchCodeCostDTO>> batchCodeMap = costResultList.stream().collect(Collectors.groupingBy(a -> a.getSkuMerchantCode() + "_" + a.getBatchNo() + "_" + a.getBatchCode()));
        for (StockGoodsCountInfo stockGoodsCountInfo : stockGoodsCountInfoList) {
            List<StockBatchCodeCostDTO> batchCodeCosts = batchCodeMap.get(stockGoodsCountInfo.getSkuMerchantCode() + "_" + stockGoodsCountInfo.getBatchNo() + "_" + stockGoodsCountInfo.getBatchCode());
            if (CollectionUtils.isEmpty(batchCodeCosts)) {
                insertDiffData(stockGoodsCountInfo, batchCodeCosts);
                continue;
            }
            double stock = batchCodeCosts.stream().mapToDouble(e -> e.getStock().doubleValue()).reduce(0, Double::sum);
            double buyStock = batchCodeCosts.stream().mapToDouble(e -> e.getBuyStock().doubleValue()).reduce(0, Double::sum);
            double waittingAreaStock = batchCodeCosts.stream().mapToDouble(e -> e.getWaittingAreaStock().doubleValue()).reduce(0, Double::sum);
            double unqualifiedAreStock = batchCodeCosts.stream().mapToDouble(e -> e.getUnqualifiedAreaStock().doubleValue()).reduce(0, Double::sum);
            double waitStock = batchCodeCosts.stream().mapToDouble(e -> e.getWaitStock().doubleValue()).reduce(0, Double::sum);
            double pieceBuyStock = batchCodeCosts.stream().mapToDouble(e -> e.getPieceBuyStock().doubleValue()).reduce(0, Double::sum);
            if (stockGoodsCountInfo.getStock().compareTo(new BigDecimal(stock)) != 0||
                stockGoodsCountInfo.getBuyStock().compareTo(new BigDecimal(buyStock)) != 0||
                stockGoodsCountInfo.getWaittingAreaStock().compareTo(new BigDecimal(waittingAreaStock)) != 0||
                stockGoodsCountInfo.getUnqualifiedAreaStock().compareTo(new BigDecimal(unqualifiedAreStock)) != 0||
                stockGoodsCountInfo.getWaitStock().compareTo(new BigDecimal(waitStock)) != 0||
                stockGoodsCountInfo.getPieceBuyStock().compareTo(new BigDecimal(pieceBuyStock)) != 0) {
                //4.差异数据入库
                insertDiffData(stockGoodsCountInfo, batchCodeCosts);
            }
        }
        logger.info("StockCostCompareServiceImpl|compare|比对完成");

    }

    private void insertDiffData(StockGoodsCountInfo stockGoodsCountInfo, List<StockBatchCodeCostDTO> batchCodeCosts) {
        DiffStockDataCompareInfoWithBLOBs bloBs = new DiffStockDataCompareInfoWithBLOBs();
        BeanUtils.copyProperties(stockGoodsCountInfo,bloBs);
        bloBs.setGoodsNo(stockGoodsCountInfo.getSkuMerchantCode());
        bloBs.setBatchCodeData(JSON.toJSONString(stockGoodsCountInfo));
        bloBs.setBatchCostData(JSON.toJSONString(batchCodeCosts));
        bloBs.setGmtCreate(new Date());
        logger.info("StockCostCompareServiceImpl|insertDiffData|bloBs:{}",bloBs);
        diffStockDataCompareInfoMapper.insertSelective(bloBs);
    }

}
