package com.cowell.bam.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.cowell.bam.domain.DiffDataCompareInfo;
import com.cowell.bam.domain.DiffDataCompareInfoWithBLOBs;
import com.cowell.bam.entity.Stock;
import com.cowell.bam.enums.DefaultBatchCodeBatchNoEnum;
import com.cowell.bam.enums.SyncTypeEnum;
import com.cowell.bam.mq.DeleteCompareStockProducer;
import com.cowell.bam.mq.EmailTransitStockProducer;
import com.cowell.bam.repository.mybatis.dao.DiffDataCompareInfoMapper;
import com.cowell.bam.service.*;
import com.cowell.bam.service.dto.*;
import com.cowell.bam.service.dto.base.AmisCommonResponse;
import com.cowell.bam.service.dto.base.AmisListResponse;
import com.cowell.bam.service.utils.ExcelUtil;
import com.cowell.bam.web.rest.util.ConstantPool;
import com.cowell.bam.web.rest.util.EmailUtils;
import com.cowell.bam.web.rest.util.RedisKeysConstant;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.xxl.job.core.biz.model.ReturnT;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang.StringUtils;
import org.joda.time.LocalDate;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.task.AsyncTaskExecutor;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;

import java.io.ByteArrayOutputStream;
import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * 在途库存相关
 * <AUTHOR>
 * @date 2022/11/1 17:01
 */
@Service
public class StockTransitCompareServiceImpl implements StockTransitCompareService {

    private Logger log = LoggerFactory.getLogger(StockTransitCompareServiceImpl.class);

    @Autowired
    private ThirdService thirdService;
    @Autowired
    private HdDataService hdDataService;
    @Autowired
    private SendCompareService sendCompareService;
    @Autowired
    private IRedissonCacheService redissonCacheService;
    @Autowired
    private IStockCenterFeignService stockCenterFeignService;
    @Autowired
    private DiffDataCompareInfoMapper diffDataCompareInfoMapper;
    @Autowired
    private DeleteCompareStockProducer deleteCompareStockProducer;
    @Autowired
    private DataCompareService dataCompareService;
    @Autowired
    private EmailTransitStockProducer emailTransitStockProducer;
    @Autowired
    private RedissonClient redissonClient;
    @Autowired
    @Qualifier("taskExecutor")
    private AsyncTaskExecutor executor;
    @Autowired
    @Qualifier("taskExecutorTrace")
    private AsyncTaskExecutor asyncTaskExecutor;
    @Value("${able.sendhd:0}")
    private int ableSendhd;
    @Value("${noticeHDSendData:}")
    private String noticeHDSendData;
    @Value("${transitStockCalc:}")
    private String transitStockCalc;
    @Value("${compare.partition:2}")
    private int comparePartition;
    @Value("${send.close.company:}")
    private String sendCloseCompany;

    private final static Integer SENDHD_TRANSIT = 1;
    private static final String BANGJIANDBSTR = "bj";
    private static final String subject = "在途库存同步差异统计";
    private static final String TRAN_CENTER_NO_GOODSDATA_REASON = "海典未推送该商品到库存中台";
    private static final String TRAN_THIRD_NO_GOODSDATA_REASON = "海典不存在该商品且中台库存不为0";
    private static final String TRAN_CENTER_ALL_NO_DATA_REASON = "中台门店无库存，需要海典全量推送";
    private static final String TRAN_DATA_ERROR_REASON = "海典推送库存和中台库存不一致";

    @Override
    public boolean compareTransit(String comId, Integer version, String date) {
        log.info("StockTransitCompareServiceImpl|compareTransit|比对连锁{}开始|批次：{}.日期：{}。.", comId, version, date);

        if (StringUtils.isEmpty(comId)) {
            log.warn("StockTransitCompareServiceImpl|compareTransit|comId为空");
            return true;
        }
        List<MdmStoreBaseDTO> mdmStoreBaseDTOList = thirdService.findMdmStoreByComId(comId);
        if (CollectionUtils.isEmpty(mdmStoreBaseDTOList)) {
            log.warn("StockTransitCompareServiceImpl|compare|根据comid获取storeNo返回值为空入参comId:{}", comId);
            return true;
        }
        log.info("StockTransitCompareServiceImpl|compareTransit|根据comId:{},查询后返回门店数量：{}", comId, mdmStoreBaseDTOList.size());
        Set<String> storeNoSet = Sets.newHashSet();
        try {
            storeNoSet = thirdService.queryStoreApplyDateSet(date);
            if (CollectionUtils.isEmpty(storeNoSet)) {
                log.error("CompareTransitStockJobService|获取门店请货日期失败 date:{}", date);
                throw new RuntimeException("获取门店请货日期失败");
            }
            log.info("StockTransitCompareServiceImpl|compareTransit|根据comId:{},查询后返回门店数量：{}", comId, storeNoSet.size());
        }catch (Exception e) {
            log.error("获取门店请货日期失败|失败comId:{}", comId, e);
            throw new RuntimeException("获取门店请货日期失败");
        }
        List<List<MdmStoreBaseDTO>> storeLists = Lists.partition(mdmStoreBaseDTOList, comparePartition);
        log.info("StockTransitCompareServiceImpl|compareTransit|根据comId:{},storeLists：{}..comparePartition:{}.", comId, storeLists.size(), comparePartition);
        Set<String> finalStoreNoSet = storeNoSet;
        storeLists.forEach(storeLists2 -> asyncTaskExecutor.execute(() -> {
            log.info("StockTransitCompareServiceImpl|compareTransit|根据comId:{},storeLists2：{}", comId, storeLists2.size());
            storeLists2.forEach(storeBase -> {
                if(finalStoreNoSet.contains(storeBase.getStoreNo())){
                    compareTransitStock(comId, storeBase, version, date);
                }else {
                    log.info("StockTransitCompareServiceImpl|compareTransit| storeNo={} 非请货日不比较", storeBase.getStoreNo());
                }
            });
        }));
        log.info("StockTransitCompareServiceImpl|compareTransit|比对连锁{}结束", comId);
        return true;
    }


    @Override
    public boolean compareTransitByStoreNo(String comId, String storeNo) {
        log.info("StockTransitCompareServiceImpl|compareTransitByStoreNo|库存比对开始comId:{},storeNo:{}", comId, storeNo);
        if (StringUtils.isEmpty(comId)) {
            log.warn("StockTransitCompareServiceImpl|compareTransitByStoreNo|comId为空");
            return true;
        }
        MdmStoreBaseDTO mdmStoreByStoreNo = thirdService.getMdmStoreByStoreNo(storeNo);
        if (Objects.isNull(mdmStoreByStoreNo)) {
            log.warn("StockTransitCompareServiceImpl|compareTransitByStoreNo|根据comid获取storeNo返回值为空入参storeNo:{}", storeNo);
            return true;
        }
        try {
            String date = new LocalDate().toString(ConstantPool.YYYYMMDD);
            Integer version = redissonCacheService.versionIncr(SyncTypeEnum.STOCK_TRANSIT.getCode(), date);
            compareTransitStock(comId, mdmStoreByStoreNo, version, date);
        } catch (Exception e) {
            log.error("StockTransitCompareServiceImpl|compareTransitByStoreNo|比对数据失败comId:{},storeNo:{}", comId, mdmStoreByStoreNo, e);
            return false;
        }
        return true;
    }

    @Override
    public void compareTransitByCom(String comId) {
        if (StringUtils.isEmpty(comId)) {
            log.warn("StockTransitCompareServiceImpl|compareTransitByCom|comId为空");
            return;
        }
        try {
            String date = new LocalDate().toString(ConstantPool.YYYYMMDD);
            Integer version = redissonCacheService.versionIncr(SyncTypeEnum.STOCK_TRANSIT.getCode(), date);
            compareTransit(comId, version, date);
        } catch (Exception e) {
            log.error("StockTransitCompareServiceImpl|compareTransitByCom|比对数据失败comId:{}", comId, e);
            return;
        }
    }

    @Override
    public void sendDifferEmailAndFile(DiffDataRequestDTO diffDataRequestDTO, String date, String toUsers, List<Long> businessIdList) {
        if(Objects.isNull(diffDataRequestDTO) || CollectionUtils.isEmpty(businessIdList)){
            log.warn("没有需要发送的差异");
            return;
        }

        //按照连锁分组
//        businessIdList.forEach(businessId->{
//            EmailTransitStockDTO emailTransitStockDTO = new EmailTransitStockDTO();
//            diffDataRequestDTO.setBusinessId(businessId);
//            emailTransitStockDTO.setDiffDataRequestDTO(diffDataRequestDTO);
//            emailTransitStockDTO.setBusinessIdList(businessIdList);
//            emailTransitStockDTO.setDate(date);
//            emailTransitStockDTO.setToUsers(toUsers);
//            emailTransitStockProducer.sendMessage(emailTransitStockDTO);
//        });
        EmailTransitStockDTO emailTransitStockDTO = new EmailTransitStockDTO();
        emailTransitStockDTO.setDiffDataRequestDTO(diffDataRequestDTO);
        emailTransitStockDTO.setBusinessIdList(businessIdList);
        emailTransitStockDTO.setDate(date);
        emailTransitStockDTO.setToUsers(toUsers);
        emailTransitStockProducer.sendMessage(emailTransitStockDTO);
    }

    /**
     * 发送邮件
     * @param date
     * @param toUsers
     */
    @Override
    public void sendDifferEmmailByCom(DiffDataRequestDTO diffDataRequestDTO, String date, String toUsers){
//        log.info("sendDifferEmmail|diffCollectList:{}.",diffCollectList);
        Long businessId = diffDataRequestDTO.getBusinessId();
        try {
            List<DiffTransitDataResponseDTO> diffCollectList = dataCompareService.geTransittDiffCollectList(diffDataRequestDTO);
            List<DiffTransitDataResponseDTO> emailDTOList = Lists.newArrayList();
            DiffTransitDataResponseDTO response = diffCollectList.stream().findFirst().orElse(new DiffTransitDataResponseDTO());
            String compId = response.getCompId();
            String title = new StringBuilder().append("【库存中台】").append(businessId).append("-").append(compId).append(" ").append(subject).append(" ").append(date).toString();
            diffCollectList.stream().filter(v->StringUtils.isNotBlank(v.getGoodsCode())).collect(Collectors.groupingBy(v->v.getCompId()+"_"+v.getBusNo())).forEach((key,list)->{
                DiffTransitDataResponseDTO responseDTO = new DiffTransitDataResponseDTO();
                DiffTransitDataResponseDTO dto = list.stream().findFirst().orElse(new DiffTransitDataResponseDTO());
                responseDTO.setCompId(key.split("_")[0]);
                responseDTO.setBusNo(key.split("_")[1]);
                responseDTO.setStoreId(dto.getStoreId());
                responseDTO.setStoreName(dto.getStoreName());
                responseDTO.setGoodsCount(list.size());
                emailDTOList.add(responseDTO);
            });

            StringBuilder content = new StringBuilder("<html><head></head><body><h3>"+ title +"</h3>");
            content.append("<table border=\"5\" style=\"border:solid 1px #E8F2F9;font-size=14px;;font-size:18px;width:500px;text-align:center\">");
            content.append("<tr ><td>序号</td><td>门店ID</td><td>门店编码</td><td>门店名称</td><td>差异sku数量</td></tr>");


            for(int i=0;i<emailDTOList.size();i++){
                DiffTransitDataResponseDTO diffCollectDataResponseDTO = emailDTOList.get(i);
                content.append("<tr>");
                content.append("<td>").append(i+1).append("</td>"); //第0列
                content.append("<td>").append(diffCollectDataResponseDTO.getStoreId()).append("</td>"); //第一列
                content.append("<td>").append(diffCollectDataResponseDTO.getBusNo()).append("</td>"); //第二列
                content.append("<td>").append(diffCollectDataResponseDTO.getStoreName()).append("</td>"); //第三列
                content.append("<td>").append(diffCollectDataResponseDTO.getGoodsCount()).append("</td>"); //第四列
                content.append("</tr>");
            }
            content.append("</table>");
            content.append("<h3>差异明细见附件</h3>");
            content.append("</body></html>");
            String[] to = org.apache.commons.lang3.StringUtils.split(toUsers, ",");

            ByteArrayOutputStream bos = new ByteArrayOutputStream();
            ExcelUtil.listToExcel(diffCollectList, getExcelColumnTitle(), "sheet1",0,  bos);
            String attachName = new StringBuilder().append(businessId).append("-").append(compId).append("_").append(date).append("_在途库存异常附件.xls").toString();

            EmailUtils.sendMail(to, title, content.toString(), bos, attachName);
            log.info("[sendDifferEmmail] 【{}】sendMailOk !", compId);
        } catch (Exception e) {
            log.warn("sendDifferEmmail|邮件任务执行失败,连锁：{}， 收件人：{}", businessId, toUsers, e);
        }
    }

    @Override
    public void sendBatchDifferEmmailByCom(EmailTransitStockDTO messageDTO){
        List<Long> businessIdList = messageDTO.getBusinessIdList();
        DiffDataRequestDTO diffDataRequestDTO = messageDTO.getDiffDataRequestDTO();
        diffDataRequestDTO.setBusinessIdList(businessIdList);
        if(Objects.nonNull(diffDataRequestDTO.getVersion()) && diffDataRequestDTO.getVersion().equals(ConstantPool.TRANSIT_ALL_VERSION)){
            diffDataRequestDTO.setVersion(null);
        }
        String date = messageDTO.getDate();
        String toUsers = messageDTO.getToUsers();
        try {
            long startTime = System.currentTimeMillis();
            List<DiffTransitDataResponseDTO> diffCollectList = dataCompareService.geTransittDiffCollectList(diffDataRequestDTO);
            log.info("sendBatchDifferEmmailByCom|同步耗时|{}。", (System.currentTimeMillis() - startTime));

            log.info("sendBatchDifferEmmailByCom|diffCollectList|size:{}.", diffCollectList.size());
            List<DiffTransitDataResponseDTO> emailDTOList = Lists.newArrayList();
            String title = new StringBuilder().append("【库存中台】").append(" ").append(subject).append(" ").append(date).toString();
            diffCollectList.stream().filter(v->StringUtils.isNotBlank(v.getCompId())).collect(Collectors.groupingBy(v->v.getCompId())).forEach((key,list)->{
                DiffTransitDataResponseDTO responseDTO = new DiffTransitDataResponseDTO();
                responseDTO.setCompId(key);
                responseDTO.setGoodsCount((int)list.stream().map(v->v.getGoodsCode()).count());
                responseDTO.setBusinessId(list.stream().findFirst().orElse(new DiffTransitDataResponseDTO()).getBusinessId());
                emailDTOList.add(responseDTO);
            });

            StringBuilder content = new StringBuilder("<html><head></head><body><h3>"+ title +"</h3>");
            content.append("<table border=\"5\" style=\"border:solid 1px #E8F2F9;font-size=14px;;font-size:18px;width:500px;text-align:center\">");
            content.append("<tr ><td>序号</td><td>连锁ID</td><td>连锁编码</td><td>差异sku数量</td></tr>");


            for(int i=0;i<emailDTOList.size();i++){
                DiffTransitDataResponseDTO diffCollectDataResponseDTO = emailDTOList.get(i);
                content.append("<tr>");
                content.append("<td>").append(i+1).append("</td>"); //第0列
                content.append("<td>").append(diffCollectDataResponseDTO.getBusinessId()).append("</td>"); //第一列
                content.append("<td>").append(diffCollectDataResponseDTO.getCompId()).append("</td>"); //第二列
//                content.append("<td>").append(diffCollectDataResponseDTO.getStoreCount()).append("</td>"); //第三列
                content.append("<td>").append(diffCollectDataResponseDTO.getGoodsCount()).append("</td>"); //第四列
                content.append("</tr>");
            }
            content.append("</table>");
            content.append("<h3>差异明细见附件</h3>");
            content.append("</body></html>");
            String[] to = org.apache.commons.lang3.StringUtils.split(toUsers, ",");

            ByteArrayOutputStream bos = new ByteArrayOutputStream();
            ExcelUtil.listToExcel(diffCollectList, getExcelColumnTitle(), "sheet1",0,  bos);
            String attachName = new StringBuilder().append(date).append("_在途库存异常附件.xls").toString();
            EmailUtils.sendMail(to, title, content.toString(), bos, attachName);
            log.info("[sendBatchDifferEmmailByCom] 【{}】sendMailOk !", title);
        } catch (Exception e) {
            log.warn("sendBatchDifferEmmailByCom|邮件任务执行失败,连锁：{}， 收件人：{}", businessIdList, toUsers, e);
        }
    }

    private LinkedHashMap<String, String> getExcelColumnTitle() {
        LinkedHashMap<String, String> fieldMap = new LinkedHashMap<>();
        fieldMap.put("compId", "连锁");
        fieldMap.put("busNo", "门店");
        fieldMap.put("storeId", "门店id");
        fieldMap.put("goodsCode", "sku");
        fieldMap.put("ztTransitCount", "中台数量");
        fieldMap.put("hdTransitCount", "海典数量");
        fieldMap.put("differTransitCount", "差异数量");
        fieldMap.put("reason", "异常原因");
        return fieldMap;
    }

    /**
     * 比较在途库存
     * @param comId
     * @param mdmStoreBaseDTO
     * @return
     */
    private boolean compareTransitStock(String comId, MdmStoreBaseDTO mdmStoreBaseDTO, Integer version, String date) {
        String storeNo = mdmStoreBaseDTO.getStoreNo();
        log.info("StockTransitCompareServiceImpl|compareTransitStock|库存比对开始comId:{},storeNo:{}", comId, storeNo);
        //判断调用海典数据库还是邦健数据库
        String dbSource = hdDataService.getDBSource(comId);
        List<Stock> stockList;
        if (org.apache.commons.lang3.StringUtils.isEmpty(dbSource) || !BANGJIANDBSTR.equals(dbSource)) {
            log.info("StockTransitCompareServiceImpl|compareTransitStock|查询海典数据库开始:comId:{},storeNo:{}", comId, storeNo);
            stockList = hdDataService.queryHdTransitStockData(comId, storeNo);
            log.info("StockTransitCompareServiceImpl|compareTransitStock|查询海典数据库结束:comId:{},storeNo:{},result:{}", comId, storeNo,stockList.size());
        } else {
            log.warn("StockTransitCompareServiceImpl|compareTransitStock|其他数据不比对");
            return true;
        }

        if (CollectionUtils.isEmpty(stockList)) {
            log.warn("StockTransitCompareServiceImpl|compareTransitStock|海典推送库存数据为空|:comId:{},storeNo:{}", comId, storeNo);
            return true;
        }

        Long businessId = thirdService.getBussiness(comId);
        if (businessId == null) {
            log.warn("StockTransitCompareServiceImpl|compareTransitStock|根据comId未查到连锁信息{}", comId);
            return true;
        }
        Long storeId = thirdService.getStore(storeNo, comId);
        if (storeId == null) {
            log.warn("StockTransitCompareServiceImpl|compareTransitStock|根据comId、storeNo未查到连锁门店信息{},{}", comId, storeNo);
            return true;
        }
        List<String> goodsNoList = stockList.stream().map(a -> StringUtils.trim(a.getWarecode()))
            .filter(StringUtils::isNotBlank).distinct().collect(Collectors.toList());

        log.info("StockTransitCompareServiceImpl|compareTransitStock|comId={},storeNo={},businessId={},storeId={},goodsNoListSize:{}",
            comId, storeNo, businessId, storeId, goodsNoList.size());

        //获取在途库存列表
        List<StockGoodsCountInfo> stockGoodsInfoList = getTransitStockList(businessId, storeId, null);

        if (CollectionUtils.isEmpty(stockGoodsInfoList)) {
            DiffDataCompareInfoWithBLOBs diffDataCompareInfo = new DiffDataCompareInfoWithBLOBs();
            Stock cmallStockRequestDTO = new Stock();
            cmallStockRequestDTO.setCompid(comId);
            cmallStockRequestDTO.setBusno(storeNo);
            cmallStockRequestDTO.setStoreName(mdmStoreBaseDTO.getStoreName());
            diffDataCompareInfo.setReason(TRAN_CENTER_ALL_NO_DATA_REASON);
            diffDataCompareInfo.setStoreId(storeId);
            diffDataCompareInfo.setBusinessId(businessId);
            diffDataCompareInfo.setVersion(version);
            diffDataCompareInfo.setExtend(date);
            getTransitDiffDataCompareInfo(cmallStockRequestDTO, null, diffDataCompareInfo);
            diffDataCompareInfoMapper.insertSelective(diffDataCompareInfo);
            if(ableSendhd == 0 && checkSendCloseCompany(comId)){
                sendDiffData2Hd(businessId, storeId, SENDHD_TRANSIT);
            }
            log.info("StockTransitCompareServiceImpl|compareTransitStock|中台该门店无库存,需要英克海典全量推送:businessId:{},storeId:{}", businessId, storeId);
            return true;
        }
        mdmStoreBaseDTO.setComId(comId);
        mdmStoreBaseDTO.setStoreNo(storeNo);
        compareTransitStock(stockList, stockGoodsInfoList, businessId, storeId, mdmStoreBaseDTO, version, date);

        stockList = null;
        return true;
    }

    /**
     * 在途库存比对
     *
     * @param hdStockList
     * @param stockGoodsInfoList
     */
    private void compareTransitStock(List<Stock> hdStockList, List<StockGoodsCountInfo> stockGoodsInfoList, Long businessId, Long storeId, MdmStoreBaseDTO mdmStoreBaseDTO, Integer version, String date) {

        Map<String, StockGoodsCountInfo> storeStockMap = stockGoodsInfoList.stream().collect(Collectors.toMap(info -> new StringBuilder().append(mdmStoreBaseDTO.getComId()).append("_").append(mdmStoreBaseDTO.getStoreNo()).append("_").append(info.getSkuMerchantCode()).toString(), info -> info, (v1, v2) -> v2));

        //需要通知SAP重新推动库存进行更新的物料集合
        List<DiffDataCompareInfoWithBLOBs> notifySendList = new ArrayList<>();

        Set<String> executeKey = Sets.newHashSet();

        for (Stock cmallStockRequestDTO : hdStockList) {

            String key = new StringBuilder().append(cmallStockRequestDTO.getCompid()).append("_").append(cmallStockRequestDTO.getBusno()).append("_").append(cmallStockRequestDTO.getWarecode()).toString();
            cmallStockRequestDTO.setStoreName(mdmStoreBaseDTO.getStoreName());

            if (executeKey.contains(key)) {
                log.info("StockTransitCompareServiceImpl|compareTransitStock|已经对比过此数据|markNo={},key={}",
                    cmallStockRequestDTO.getMakeno(), key);
                continue;
            }

            StockGoodsCountInfo storeStock = storeStockMap.get(key);

            log.info("StockTransitCompareServiceImpl|compareTransitStock|markNo={},key={},海典库存={},库存中台库存={}",
                cmallStockRequestDTO.getMakeno(), key, cmallStockRequestDTO, storeStock);


            executeKey.add(key);


            BigDecimal qty1 = StringUtils.isBlank(cmallStockRequestDTO.getZtqtyqh()) ? BigDecimal.ZERO : new BigDecimal(cmallStockRequestDTO.getZtqtyqh());//请货
            BigDecimal qty2 = StringUtils.isBlank(cmallStockRequestDTO.getZtqtyph()) ? BigDecimal.ZERO : new BigDecimal(cmallStockRequestDTO.getZtqtyph());//铺货
            BigDecimal qty3 = StringUtils.isBlank(cmallStockRequestDTO.getZtqtydb()) ? BigDecimal.ZERO : new BigDecimal(cmallStockRequestDTO.getZtqtydb());//调拨
            List<BigDecimal> allTransit = Arrays.asList(qty1, qty2, qty3);
            BigDecimal transitStock = BigDecimal.ZERO;
            if (StringUtils.isNotBlank(transitStockCalc)) {
                // 根据配置，计算在途库存
                String[] indexArray = transitStockCalc.split(",");
                for (String index : indexArray) {
                    int i = Integer.parseInt(index);
                    if(Objects.isNull(allTransit.get(i-1))){
                        continue;
                    }
                    transitStock = transitStock.add(allTransit.get(i-1));
                }
            } else {
                transitStock = qty1.add(qty2).add(qty3);
            }
            log.info("StockTransitCompareServiceImpl|compareTransitStock|transitStock:{}..key:{}.", transitStock, key);
            if(Objects.nonNull(transitStock)){
                cmallStockRequestDTO.setTransitStock(transitStock.toPlainString());
            }

            if (storeStock == null) {
//                log.info("StockTransitCompareServiceImpl|compareTransitStock|库存比对|库存中心商品库存为空");
                DiffDataCompareInfoWithBLOBs diffDataCompareInfo = new DiffDataCompareInfoWithBLOBs();
                diffDataCompareInfo.setReason(TRAN_CENTER_NO_GOODSDATA_REASON);
                diffDataCompareInfo.setStoreId(storeId);
                diffDataCompareInfo.setBusinessId(businessId);
                diffDataCompareInfo.setVersion(version);
                diffDataCompareInfo.setExtend(date);
                getTransitDiffDataCompareInfo(cmallStockRequestDTO, null, diffDataCompareInfo);
                notifySendList.add(diffDataCompareInfo);

            } else {
                if (transitStock.compareTo(storeStock.getTransitStock()) != 0) {
                    if (transitStock.subtract(storeStock.getTransitStock()).abs().compareTo(BigDecimal.ONE) < 0) {
                        storeStockMap.remove(key);
                        continue;
                    }
                    DiffDataCompareInfoWithBLOBs diffDataCompareInfo = new DiffDataCompareInfoWithBLOBs();
                    diffDataCompareInfo.setReason(TRAN_DATA_ERROR_REASON);
                    diffDataCompareInfo.setBusinessId(businessId);
                    diffDataCompareInfo.setStoreId(storeId);
                    diffDataCompareInfo.setVersion(version);
                    diffDataCompareInfo.setExtend(date);
                    getTransitDiffDataCompareInfo(cmallStockRequestDTO, storeStock, diffDataCompareInfo);
                    notifySendList.add(diffDataCompareInfo);

                }else{
                    log.info("StockTransitCompareServiceImpl|compareTransitStock|无差异:{}.", key);
                }
                //删除比对完的库存
                storeStockMap.remove(key);
            }
        }
//        log.info("storeStockMap|{}..compId:{}.storeNo:{}.", storeStockMap, mdmStoreBaseDTO.getComId(), mdmStoreBaseDTO.getStoreNo());

        //中台有，但是海典没了，说明在途释放了
        if (MapUtils.isNotEmpty(storeStockMap)) {
            String batchCode = DefaultBatchCodeBatchNoEnum.TRANSIT_STOCK.getBatchCode();//批次
            String batchNo = DefaultBatchCodeBatchNoEnum.TRANSIT_STOCK.getBatchNo();//批号
            List<String> sqlList = new ArrayList<>();
            String value = getFirstNumber(getLastNumber(storeId + "", 10), 5);
            long db = Long.parseLong(getFirstNumber(value, 2)) % 4L;
            Long table = Long.parseLong(getLastNumber(value, 3)) % 256;
            for (String key : storeStockMap.keySet()) {
                StockGoodsCountInfo stockGoodsCountInfo = storeStockMap.get(key);
                if (BigDecimal.ZERO.compareTo(stockGoodsCountInfo.getTransitStock()) == 0) {
                    continue;
                }
                String sqlResult = "delete from stock_goods_batch_code_" + String.format("%0" + 3 + "d", table) + " where store_id = " + stockGoodsCountInfo.getStoreId()
                    + " and sku_merchant_code = '" + stockGoodsCountInfo.getSkuMerchantCode() + "' and batch_no = '"+batchNo+"' and batch_code = '"+batchCode+"';";
                sqlList.add(sqlResult);
                stockGoodsCountInfo.setBatchCode(batchCode);
                stockGoodsCountInfo.setBatchNo(batchNo);
                DiffDataCompareInfoWithBLOBs diffDataCompareInfo = new DiffDataCompareInfoWithBLOBs();
                diffDataCompareInfo.setReason(TRAN_THIRD_NO_GOODSDATA_REASON);
                diffDataCompareInfo.setBusinessId(businessId);
                diffDataCompareInfo.setStoreId(storeId);
                Stock cmallStockRequestDTO = new Stock();
                cmallStockRequestDTO.setCompid(mdmStoreBaseDTO.getComId());
                cmallStockRequestDTO.setBusno(mdmStoreBaseDTO.getStoreNo());
                cmallStockRequestDTO.setStoreName(mdmStoreBaseDTO.getStoreName());
                diffDataCompareInfo.setVersion(version);
                diffDataCompareInfo.setExtend(date);
                getTransitDiffDataCompareInfo(cmallStockRequestDTO, stockGoodsCountInfo, diffDataCompareInfo);
                notifySendList.add(diffDataCompareInfo);
            }
            log.warn("StockTransitCompareServiceImpl|compareStock|海典释放在途库存后需要删除的商品数据库:{}:{}", db, sqlList);
        }

        //插入存在差异的数据到对比表
        executor.execute(() -> {
            for (DiffDataCompareInfoWithBLOBs diffDataCompareInfo : notifySendList) {
                diffDataCompareInfoMapper.insertSelective(diffDataCompareInfo);
            }
        });

        //通知重发存在差异的数据,ableSendhd 0-发送  其他不发,逻辑删除也放到这里
        if(ableSendhd == 0 && checkSendCloseCompany(mdmStoreBaseDTO.getComId())){

            //在途不删除库存，如果执行这段逻辑，库存中台没法接收海典推送的在途
//            if (CollectionUtils.isNotEmpty(stockGoodsCountInfoList)) {
//                deleteCompareStockProducer.send(JSON.toJSONString(stockGoodsCountInfoList));
//            }

            executor.execute(() -> {
                for (List<DiffDataCompareInfoWithBLOBs> list : Lists.partition(notifySendList, 500)) {
                    send2HD(list, SENDHD_TRANSIT);
                }
            });
        }
        if(CollectionUtils.isEmpty(notifySendList)){
            log.info("StockTransitCompareServiceImpl|StockTransitCompareServiceImpl|compareStock|库存比对结束|无差异：连锁：{}。 门店：{}。", businessId, storeId);
        }else{
            log.info("StockTransitCompareServiceImpl|StockTransitCompareServiceImpl|compareStock|库存比对结束：size:{}. 连锁：{}。 门店：{}。", notifySendList.size(), businessId, storeId);
        }
    }

    /**
     * 组织入库信息
     * @param cmallStockRequestDTO
     * @param storeStock
     * @param diffDataCompareInfo
     */
    private void getTransitDiffDataCompareInfo(Stock cmallStockRequestDTO, StockGoodsCountInfo storeStock, DiffDataCompareInfoWithBLOBs diffDataCompareInfo) {
        fillViewTransitStockInfo(diffDataCompareInfo, cmallStockRequestDTO);
        fillTransitDiffBasicInfo(diffDataCompareInfo);
        fillTransitStockGoodsInfo(diffDataCompareInfo, storeStock);
    }

    /**
     * 组织海典信息
     * @param diffDataCompareInfo
     * @param viewStock
     */
    private void fillViewTransitStockInfo(DiffDataCompareInfoWithBLOBs diffDataCompareInfo, Stock viewStock) {
        if (Objects.isNull(viewStock)) {
            return;
        }
        ThridStockInfoDTO thridStockInfoDTO = new ThridStockInfoDTO();
        thridStockInfoDTO.setCompId(viewStock.getCompid());
        thridStockInfoDTO.setBusNo(viewStock.getBusno());
        thridStockInfoDTO.setStoreName(StringUtils.isNotBlank(viewStock.getStoreName())?viewStock.getStoreName():"");
        thridStockInfoDTO.setWareCode(StringUtils.isNotBlank(viewStock.getWarecode())?viewStock.getWarecode():"");
        thridStockInfoDTO.setTransitStock(StringUtils.isNotBlank(viewStock.getTransitStock())?viewStock.getTransitStock():"");
        diffDataCompareInfo.setGoodsNo(StringUtils.isNotBlank(viewStock.getWarecode())?viewStock.getWarecode():"全部");
        diffDataCompareInfo.setThirdData(JSON.toJSONString(thridStockInfoDTO));
    }

    @Override
    public void sendDiffData2Hd(Long businessId, Long sid, Integer transitStock) {
        List<DiffDataCompareInfoWithBLOBs> list = new ArrayList<>();
        DiffDataCompareInfoWithBLOBs diffDataCompareInfoWithBLOB = new DiffDataCompareInfoWithBLOBs();
        diffDataCompareInfoWithBLOB.setBusinessId(businessId);
        diffDataCompareInfoWithBLOB.setStoreId(sid);
        list.add(diffDataCompareInfoWithBLOB);
        send2HD(list, transitStock);
    }

    private void send2HD(List<DiffDataCompareInfoWithBLOBs> diffDataCompareInfos, Integer transitStock) {
        Map<String, List<DiffDataCompareInfo>> collect = diffDataCompareInfos.stream().collect(Collectors.groupingBy(a -> a.getBusinessId() + "_" + a.getStoreId()));
        for (String bid_storeId : collect.keySet()) {
            List<DiffDataCompareInfo> infos = collect.get(bid_storeId);
            CmallReqDTO cmallStockReqDTO = new CmallReqDTO();
            String[] arr = StringUtils.split(bid_storeId, "_");
            MdmDataTransformDTO mdmDataTransformDTO = thirdService.getMdmDataTransformDTO(Long.valueOf(arr[0]), Long.valueOf(arr[1]));
            if (mdmDataTransformDTO == null || CollectionUtils.isEmpty(mdmDataTransformDTO.getStoreNos())) {
                continue;
            }
            if(Objects.nonNull(transitStock)){
                cmallStockReqDTO.setTransitStock(transitStock);
            }
            cmallStockReqDTO.setCompId(mdmDataTransformDTO.getComId());
            cmallStockReqDTO.setBusNo(mdmDataTransformDTO.getStoreNos().get(0));
            cmallStockReqDTO.setDetails(sendCompareService.getInfoList(infos));
            CompareDataDTO compareDataDTO = getCompareDataDTO(cmallStockReqDTO);
            sendCompareService.callBackHdCompensate(compareDataDTO);
        }
    }

    private CompareDataDTO getCompareDataDTO(CmallReqDTO cmallStockReqDTO) {
        CompareDataDTO compareDataDTO = new CompareDataDTO();
        compareDataDTO.setBdata(JSON.toJSONString(cmallStockReqDTO));
        compareDataDTO.setComId(cmallStockReqDTO.getCompId());
        compareDataDTO.setBtype(ConstantPool.BTYPE_STOCK);

        //根据comid分配url
        String dbSource = hdDataService.getDBSource(cmallStockReqDTO.getCompId());
        if (org.apache.commons.lang3.StringUtils.isEmpty(dbSource) || !BANGJIANDBSTR.equals(dbSource)) {
            compareDataDTO.setRequestUrl(noticeHDSendData + ConstantPool.STOCK_REQUEST_URL);
        } else {
            log.info("在途英克不发");
        }

        compareDataDTO.setServiceDesc("库存对比服务");
        return compareDataDTO;
    }

    /**
     * 组织基本信息
     * @param diffDataCompareInfo
     */
    private void fillTransitDiffBasicInfo(DiffDataCompareInfoWithBLOBs diffDataCompareInfo){
        diffDataCompareInfo.setDataType(SyncTypeEnum.STOCK_TRANSIT.getCode());
        diffDataCompareInfo.setStatus(0);
        diffDataCompareInfo.setUpdatedBy("-1");
        diffDataCompareInfo.setCreatedBy("-1");
        diffDataCompareInfo.setGmtCreate(new Date());
        diffDataCompareInfo.setGmtUpdate(new Date());
    }

    /**
     * 组织中台信息
     * @param diffDataCompareInfo
     * @param storeStock
     */
    private void fillTransitStockGoodsInfo(DiffDataCompareInfoWithBLOBs diffDataCompareInfo, StockGoodsCountInfo storeStock) {
        if (Objects.isNull(storeStock)) {
            return;
        }
        diffDataCompareInfo.setGoodsNo(storeStock.getSkuMerchantCode());
        StockDataInfoDTO stockDataInfoDTO = new StockDataInfoDTO();
        stockDataInfoDTO.setBusinessId(storeStock.getBusinessId());
        stockDataInfoDTO.setStoreId(storeStock.getStoreId());
        stockDataInfoDTO.setSkuMerchantCode(storeStock.getSkuMerchantCode());
        stockDataInfoDTO.setTransitStock(storeStock.getTransitStock());
        stockDataInfoDTO.setStoreName(storeStock.getStoreName());
        diffDataCompareInfo.setOurData(JSON.toJSONString(stockDataInfoDTO));
    }

    /**
     * 调用库存中心查询物料在途库存数量
     *
     * @param businessId
     * @param storeId
     * @param goodsNoList
     * @return
     */
    protected List<StockGoodsCountInfo> getTransitStockList(Long businessId, Long storeId, List<String> goodsNoList) {

        List<StockGoodsCountInfo> goodsCountInfos = Lists.newArrayList();
        List<StockGoodsCountInfo> finalGoodsCountInfos = Lists.newArrayList();
        if (Objects.isNull(businessId) || Objects.isNull(storeId)) {
            return finalGoodsCountInfos;
        }

        StockAggregateQuery param = new StockAggregateQuery();
        param.setStoreId(storeId);
        param.setBusinessId(businessId);
        if(CollectionUtils.isNotEmpty(goodsNoList)){
            param.setGoodsNos(goodsNoList);
        }
        param.setSize(100);
        log.info("getBatchCodeList|param:{}.", param);
        int page = 1;
        do {
            param.setPage(page);
            ResponseEntity<PageInfo<StockGoodsCountInfo>> response = stockCenterFeignService.stockAggregate(param);
            if (Objects.nonNull(response) && HttpStatus.OK.equals(response.getStatusCode()) && Objects.nonNull(response.getBody())) {
                goodsCountInfos = response.getBody().getList();
//                log.info("getBatchCodeList|goodsCountInfos:{}.", goodsCountInfos);
                if(CollectionUtils.isNotEmpty(goodsCountInfos)){
                    finalGoodsCountInfos.addAll(goodsCountInfos);
                }
                page++;
            }
        } while (CollectionUtils.isNotEmpty(goodsCountInfos));
        log.info("getBatchCodeList|finalGoodsCountInfos..page:{}.", page);
        return finalGoodsCountInfos;
    }

    private static String getFirstNumber(String idStr, int length) {
        idStr = idStr.substring(0, length);
        return String.format("%0" + length + "d", Long.valueOf(idStr));
    }

    private static String getLastNumber(String idStr, int length) {
        int size = Math.min(idStr.length(), length);
        idStr = idStr.substring(idStr.length() - size);
        return String.format("%0" + length + "d", Long.valueOf(idStr));
    }

    /**
     * 校验发送连锁
     * @param companyCode
     * @return
     */
    private Boolean checkSendCloseCompany(String companyCode){
        if(StringUtils.isBlank(sendCloseCompany)){
            return true;
        }
        List<String> sendCloseCompanyList = Arrays.stream(sendCloseCompany.split(",")).collect(Collectors.toList());
        return !sendCloseCompanyList.contains(companyCode);
    }

//    public static void main(String[] args) {
//        String value = getFirstNumber(getLastNumber("82464919484", 10), 5);
//        long db = Long.parseLong(getFirstNumber(value, 2)) % 4L;
//        Long table = Long.parseLong(getLastNumber(value, 3)) % 256;
//        System.out.println(db);
//        System.out.println(table);
//    }
}
