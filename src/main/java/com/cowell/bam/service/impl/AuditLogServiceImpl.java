package com.cowell.bam.service.impl;

import com.cowell.bam.domain.AuditLog;
import com.cowell.bam.domain.AuditLogDTO;
import com.cowell.bam.domain.AuditLogExample;
import com.cowell.bam.repository.mybatis.dao.AuditLogMapper;
import com.cowell.bam.service.AuditLogService;
import com.cowell.bam.service.utils.Pagination;
import com.cowell.bam.web.rest.errors.BusinessErrorException;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class AuditLogServiceImpl implements AuditLogService {
    private Logger log = LoggerFactory.getLogger(AuditLogServiceImpl.class);

    @Autowired
    private AuditLogMapper auditLogMapper;
    @Override
    public Pagination<AuditLog> searchAuditLog(AuditLogDTO auditLogDTO) {

        Integer page = auditLogDTO.getPage();
        Integer pageSize = auditLogDTO.getPageSize();
        AuditLogExample example = new AuditLogExample();
        AuditLogExample.Criteria criteria = example.createCriteria();
        Boolean isInput = false;
        if (StringUtils.isNotEmpty(auditLogDTO.getUserId())) {
            criteria.andUserIdEqualTo(auditLogDTO.getUserId());
            isInput = true;
        }
        if (StringUtils.isNotEmpty(auditLogDTO.getUserName())) {
            criteria.andUserNameEqualTo(auditLogDTO.getUserName());
            isInput = true;
        }
        if (StringUtils.isNotEmpty(auditLogDTO.getSystemName())) {
            criteria.andSystemNameEqualTo(auditLogDTO.getSystemName());
            isInput = true;
        }
        if (auditLogDTO.getStartDate() != null && auditLogDTO.getEndDate() != null) {
            criteria.andOperateDateBetween(auditLogDTO.getStartDate(), auditLogDTO.getEndDate());
            isInput = true;
        }
        example.setLimit(pageSize);
        example.setOffset((page-1)*pageSize);
        example.setOrderByClause("`id` desc");
        List<AuditLog> list = null;
        if (isInput) {
            try {
                list = auditLogMapper.selectByExample(example);
            } catch (Exception e) {
                log.error("查询审计日志失败：", e);
                throw new BusinessErrorException("查询审计日志失败");
            }
        } else {
            log.error("查询条件不能为空");
            throw new BusinessErrorException("查询条件不能为空");
        }
        if (CollectionUtils.isEmpty(list)) {
            throw new BusinessErrorException("查询记录为空");
        }
        Long count = 0L;
        try {
            count = auditLogMapper.countByExample(example);
        } catch (Exception e) {
            log.error("查询审计日志分页总数失败：", e);
            throw new BusinessErrorException("查询审计日志分页总数失败");
        }
        Pagination<AuditLog> pagination = new Pagination<>(page, pageSize);
        pagination.setHitCount(count);
        pagination.setList(list);
        return pagination;
    }
}
