package com.cowell.bam.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.cowell.bam.enums.BdataTypeEnum;
import com.cowell.bam.enums.RequestStatusEnum;
import com.cowell.bam.service.IThirdPlatformService;
import com.cowell.bam.service.dto.*;
import com.cowell.bam.web.rest.util.ErpRequestConstants;
import com.cowell.bam.web.rest.util.Md5Utils;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.joda.time.DateTime;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.*;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;
import org.springframework.web.client.RestTemplate;

import java.io.Serializable;
import java.util.*;

/**
 * bam
 * 2020/9/14 11:29
 * 第三方平台接口实现类
 *
 * <AUTHOR>
 * @since
 **/

@Slf4j
@Service
public class ThirdPlatformServiceImpl implements IThirdPlatformService, Serializable {

    public static final String APP_NAME = "bam";
    public static final String NOTIFY_SAP_SEND_STOCK_COMPARE_DATA_ACTION = "notifySAPSendStockCompareData";

    private static final String IDOC_DATA_TYPE = "sapIdocHandler";

    private static final String PROXY_DATA_TYPE = "sapProxyHandler";


    @Autowired
    @Qualifier("vanillaRestTemplate")
    private RestTemplate restTemplate;



    @Value("${mb.config}")
    private String configJson;

    @Autowired
    private ThirdService thirdService;



    private String callMB(RequestBodyDTO requestBodyDTO) {
        builderParam(requestBodyDTO);
        return asySendRequest(requestBodyDTO);
    }

    @Override
    public void notifySAPSendStockData(ThirdPlatformStockDTO dto) {
        if (Objects.isNull(dto) || StringUtils.isBlank(dto.getBusNo())) {
            throw new IllegalArgumentException("请求参数不正确!");
        }
        MdmStoreBaseDTO mdmStoreByStoreNo = thirdService.getMdmStoreByStoreNo(dto.getBusNo());
        if (Objects.isNull(mdmStoreByStoreNo)) {
            throw new IllegalArgumentException("门店不存在!门店编码：" + dto.getBusNo());
        }
        Boolean warehouse = thirdService.isWarehouse(mdmStoreByStoreNo.getStoreId());
        if (!warehouse) {
            throw new IllegalArgumentException("当前门店非大仓!门店编码：" + dto.getBusNo());
        }

        String bguid = UUID.randomUUID().toString();
        log.info("ThirdPlatformServiceImpl|notifySAPSendStockData|通知SAP发送对比库存数据开始|bguid={},start={}", bguid, new Date());
        RequestBodyDTO requestBodyDTO = new RequestBodyDTO();
        requestBodyDTO.setBguid(bguid);
        requestBodyDTO.setAppName(APP_NAME);
        requestBodyDTO.setAction(NOTIFY_SAP_SEND_STOCK_COMPARE_DATA_ACTION);
        requestBodyDTO.setBdata(JSON.toJSONString(dto));
        callMB(requestBodyDTO);
    }

    private ActionBean getActionBean(String action) {
        if (StringUtils.isEmpty(configJson)) {
            throw new IllegalArgumentException("请在apollo中配置mb.config");
        }
        for (ActionBean actionBean : JSONObject.parseArray(configJson, ActionBean.class)) {
            if (action.equals(actionBean.getAction())) {
                return actionBean;
            }
        }
        throw new IllegalArgumentException("请在apollo中配置mb.config");
    }

    private void builderParam(RequestBodyDTO requestBodyDTO) {

        ActionBean actionsBean = getActionBean(requestBodyDTO.getAction());

        String requestUrl = actionsBean.getUrl();

        log.info("ThirdPlatformServiceImpl|buildParam|apollo获取URL信息|action={},url={}", requestBodyDTO.getAction(), requestUrl);

        if (ObjectUtils.isEmpty(requestBodyDTO.getInnerRequestParamDTO())) {
            InnerRequestParamDTO innerParam = new InnerRequestParamDTO();
            requestBodyDTO.setInnerRequestParamDTO(innerParam);
        }

        requestBodyDTO.getInnerRequestParamDTO().setRequestUrl(requestUrl);

        //请求类型
        String methodType = actionsBean.getMethodType();

        //获取授权认证信息
        String isAuth = actionsBean.getIsAuth();

        //接口请求模式
        String requestMode = actionsBean.getRequetMode();
        //接口描述
        String serviceDesc = actionsBean.getDesc();

        String bdataType = actionsBean.getBDataType();

        requestBodyDTO.getInnerRequestParamDTO().setMethodType(methodType);
        requestBodyDTO.getInnerRequestParamDTO().setIsAuth(isAuth);
        requestBodyDTO.getInnerRequestParamDTO().setRequestMode(requestMode);
        requestBodyDTO.getInnerRequestParamDTO().setRequestDesc(serviceDesc);

        if (StringUtils.isNotEmpty(bdataType)) {
            requestBodyDTO.getInnerRequestParamDTO().setBDataType(bdataType);
        }


        String bType = actionsBean.getBtype();
        String bSource = actionsBean.getBsource();
        String bDestination = actionsBean.getBdestination();

        requestBodyDTO.getInnerRequestParamDTO().setBtype(bType);
        requestBodyDTO.getInnerRequestParamDTO().setBsource(bSource);
        requestBodyDTO.getInnerRequestParamDTO().setBdestination(bDestination);

    }


    protected List<Map<String, Object>> buildParam(RequestBodyDTO requestBodyDTO) {
        ActionBean actionBean = getActionBean(requestBodyDTO.getAction());

        if (PROXY_DATA_TYPE.equals(actionBean.getBusinessHandlerType())) {
            return buildParamProxy(requestBodyDTO);
        } else if (IDOC_DATA_TYPE.equals(actionBean.getBusinessHandlerType())) {
            return buildParamIdoc(requestBodyDTO);
        } else {
            throw new IllegalArgumentException("不支持的action：" + actionBean.getBusinessHandlerType());
        }
    }

    protected String asySendRequest(RequestBodyDTO requestBodyDTO) {
        List<Map<String, Object>> paramMap;

        if (CollectionUtils.isEmpty(requestBodyDTO.getBdatas())) {
            paramMap = buildParam(requestBodyDTO);
        } else {
            paramMap = buildBatchParam(requestBodyDTO);
        }
        //用户名
        String userName = requestBodyDTO.getInnerRequestParamDTO().getUserName();
        //密码
        String password = requestBodyDTO.getInnerRequestParamDTO().getPassword();

        String requestUrl = requestBodyDTO.getInnerRequestParamDTO().getRequestUrl();

        String erpType = requestBodyDTO.getInnerRequestParamDTO().getErpType();

        String requestDesc = requestBodyDTO.getInnerRequestParamDTO().getRequestDesc();

        String result = "success";

        JSONObject jsonObject = new JSONObject();

        jsonObject.put(ErpRequestConstants.REQUEST_TABLE_FIELD, paramMap);

        SendRequestBodyDTO sendBody = new SendRequestBodyDTO();
        sendBody.setPassWord(password);
        sendBody.setUserName(userName);
        sendBody.setRequestUrl(requestUrl);
        sendBody.setRequestBody(jsonObject.toJSONString());
        sendBody.setSource(requestBodyDTO.getAppName());
        sendBody.setDestination(erpType);
        //接口描述信息
        sendBody.setServiceDesc(requestDesc);

        sendBody.setAppName(requestBodyDTO.getAppName());
        sendBody.setAction(requestBodyDTO.getAction());
        sendBody.setErpType(erpType);
        if (Objects.nonNull(requestBodyDTO.getBusinessId())) {
            sendBody.setBusinessId(Long.valueOf(requestBodyDTO.getBusinessId()));
        }
        sendBody.setRequestBguid(requestBodyDTO.getBguid());
        execute(sendBody.getRequestBody(), String.class, sendBody.getRequestUrl(), 3);
        return result;
    }


    protected List<Map<String, Object>> buildBatchParam(RequestBodyDTO requestBodyDTO) {

        List<String> bDatas = requestBodyDTO.getBdatas();
        if (CollectionUtils.isEmpty(bDatas)) {
            return null;
        }
        List<Map<String, Object>> list = Lists.newArrayList();
        for (int i = 0, l = bDatas.size(); i < l; i++) {
            if (StringUtils.isEmpty(bDatas.get(i))) {
                continue;
            }
            String bguid = String.format("%s%s", "BAM", UUID.randomUUID().toString());

            log.info("ThirdPlatformServiceImpl|buildBatchParam|构建请求批量erp参数|param:bguid = {}", bguid);
            bguid = StringUtils.replace(bguid, "-", "");
            Map<String, Object> paramMap = Maps.newHashMap();

            paramMap.put(ErpRequestConstants.REQUEST_B_TYPE_FIELD, requestBodyDTO.getInnerRequestParamDTO().getBtype());
            paramMap.put(ErpRequestConstants.REQUEST_B_G_UID_FIELD, bguid);

            paramMap.put(ErpRequestConstants.REQUEST_B_SOURCE_FIELD, requestBodyDTO.getInnerRequestParamDTO().getBsource());
            paramMap.put(ErpRequestConstants.REQUEST_B_KEYS_FIELD, StringUtils.isEmpty(requestBodyDTO.getBkeyss().get(i)) ? ""
                : requestBodyDTO.getBkeyss().get(i));


            paramMap.put(ErpRequestConstants.REQUEST_B_STATUS_FIELD, RequestStatusEnum.SENT.getCode());
            paramMap.put(ErpRequestConstants.REQUEST_B_DATETIME_FIELD, DateTime.now().toString(ErpRequestConstants.REQUEST_DATE_FORMAT_PATTERN));
            paramMap.put(ErpRequestConstants.REQUEST_B_DESTINATION_FIELD, requestBodyDTO.getInnerRequestParamDTO().getBdestination());
            paramMap.put(ErpRequestConstants.REQUEST_B_CALLBACK_FIELD, requestBodyDTO.getInnerRequestParamDTO().getBcallback());
            paramMap.put(ErpRequestConstants.REQUEST_B_VERSION_FIELD, "v_1_1");

            try {
                JSONObject jsonObject = JSONObject.parseObject(bDatas.get(i));
                if (null == jsonObject.getJSONObject(ErpRequestConstants.REQUEST_B_DATA_FIELD)) {
                    paramMap.put(ErpRequestConstants.REQUEST_B_DATAHASH_FIELD, Md5Utils.MD5Encode(bDatas.get(i)));
                    paramMap.put(ErpRequestConstants.REQUEST_B_DATA_FIELD, bDatas.get(i));
                } else {
                    paramMap.put(ErpRequestConstants.REQUEST_B_DATAHASH_FIELD, Md5Utils.MD5Encode(jsonObject.getString(ErpRequestConstants.REQUEST_B_DATA_FIELD)));
                    paramMap.put(ErpRequestConstants.REQUEST_B_DATA_FIELD, jsonObject.getString(ErpRequestConstants.REQUEST_B_DATA_FIELD));
                }
            } catch (Exception e) {
                log.error("ThirdPlatformServiceImpl|buildBatchParam|构建请求批量erp参数异常!|param={}", requestBodyDTO.toString(), e);
            }
            list.add(paramMap);
        }

        return list;
    }

    /**
     * proxy类型数据param
     *
     * @param requestBodyDTO
     * @return
     */
    private List<Map<String, Object>> buildParamProxy(RequestBodyDTO requestBodyDTO) {
        String bguid = requestBodyDTO.getBguid();

        String bData = requestBodyDTO.getBdata();

        if (StringUtils.isEmpty(bguid)) {
            bguid = String.format("%s-%s", "BAM", UUID.randomUUID().toString());
            //将uuid中的-横杠去掉
            bguid = StringUtils.replace(bguid, "-", "");
            requestBodyDTO.setBguid(bguid);
        }

        log.info("ThirdPlatformServiceImpl|buildParam|构建请求erp参数|param:bguid = {}", bguid);

        Map<String, Object> param = Maps.newHashMap();

        param.put(ErpRequestConstants.REQUEST_B_G_UID_FIELD, bguid);
        param.put(ErpRequestConstants.REQUEST_B_TYPE_FIELD, requestBodyDTO.getInnerRequestParamDTO().getBtype());
        param.put(ErpRequestConstants.REQUEST_B_SOURCE_FIELD, requestBodyDTO.getInnerRequestParamDTO().getBsource());
        param.put(ErpRequestConstants.REQUEST_B_KEYS_FIELD, StringUtils.isEmpty(requestBodyDTO.getBkeys()) ? "" : requestBodyDTO.getBkeys());


        param.put(ErpRequestConstants.REQUEST_B_STATUS_FIELD, RequestStatusEnum.SENT.getCode());
        param.put(ErpRequestConstants.REQUEST_B_DESTINATION_FIELD, requestBodyDTO.getInnerRequestParamDTO().getBdestination());
        param.put(ErpRequestConstants.REQUEST_B_DATETIME_FIELD, DateTime.now().toString(ErpRequestConstants.REQUEST_DATE_FORMAT_PATTERN));
        param.put(ErpRequestConstants.REQUEST_B_CALLBACK_FIELD, StringUtils.isNotBlank(requestBodyDTO.getInnerRequestParamDTO().getBcallback())
            ? requestBodyDTO.getInnerRequestParamDTO().getBcallback() : "");
        param.put(ErpRequestConstants.REQUEST_B_VERSION_FIELD, "v_1_1");

        try {
            JSONObject jsonObject = JSONObject.parseObject(bData);
            if (null == jsonObject.getJSONObject(ErpRequestConstants.REQUEST_B_DATA_FIELD)) {
                param.put(ErpRequestConstants.REQUEST_B_DATAHASH_FIELD, Md5Utils.MD5Encode(bData));
                param.put(ErpRequestConstants.REQUEST_B_DATA_FIELD, bData);
            } else {
                param.put(ErpRequestConstants.REQUEST_B_DATAHASH_FIELD, Md5Utils.MD5Encode(jsonObject.getString(ErpRequestConstants.REQUEST_B_DATA_FIELD)));
                param.put(ErpRequestConstants.REQUEST_B_DATA_FIELD, jsonObject.getString(ErpRequestConstants.REQUEST_B_DATA_FIELD));
            }
        } catch (Exception e) {
            log.error("ThirdPlatformServiceImpl|buildParam|构建请求erp参数异常!|param={}", requestBodyDTO.toString(), e);
        }

        List<Map<String, Object>> list = Lists.newArrayList();

        list.add(param);

        return list;
    }

    private List<Map<String, Object>> buildParamIdoc(RequestBodyDTO requestBodyDTO) {

        List<Map<String, Object>> list = Lists.newArrayList();
        Map<String, Object> map = Maps.newHashMap();

        switch (BdataTypeEnum.getByType(requestBodyDTO.getInnerRequestParamDTO().getBDataType())) {

            case BATA_TYPE_STRING:
                map.put(ErpRequestConstants.REQUEST_B_DATA_FIELD, requestBodyDTO.getBdata());
                break;
            case BDATA_TYPE_JSON:
                map.put(ErpRequestConstants.REQUEST_B_DATA_FIELD, JSONObject.parseObject(requestBodyDTO.getBdata()));
                break;
            case BDATA_TYPE_JSONARRAY:
                map.put(ErpRequestConstants.REQUEST_B_DATA_FIELD, JSONArray.parseArray(requestBodyDTO.getBdata()));
                break;
            default:
                break;
        }
        list.add(map);
        return list;
    }

    /**
     * rest调用第三方平台接口
     *
     * @param req
     * @param res
     * @param url
     * @param retryTimes
     * @param <Req>
     * @param <Res>
     * @return
     */
    private <Req, Res> Res execute(Req req, Class<Res> res, String url, int retryTimes) {
        MediaType type = MediaType.parseMediaType("application/json; charset=UTF-8");

        HttpHeaders requestHeaders = new HttpHeaders();
        requestHeaders.setContentType(type);
        requestHeaders.add("Accept", MediaType.APPLICATION_JSON.toString());
        HttpEntity<Req> requestEntity = new HttpEntity(req, requestHeaders);

        ResponseEntity<Res> responseEntity = null;
        try {


            long startTime = System.currentTimeMillis();
            responseEntity = restTemplate.exchange(url, HttpMethod.POST, requestEntity, res);
            log.info("ThirdPlatformServiceImpl|execute|请求第三方接口返回结果|响应结果|result={},useTime={}",
                responseEntity, System.currentTimeMillis() - startTime);

        } catch (Exception e) {
            retryTimes--;
            if (retryTimes <= 0) {
                log.error("ThirdPlatformServiceImpl|execute|掉第三方接口异常|req={},res={}", req, responseEntity, e);
                return null;
            }
            return  execute(req, res, url, retryTimes);
        }
        if (Objects.isNull(responseEntity) || Objects.isNull(requestEntity.getBody())) {
            return null;
        }
        return responseEntity.getBody();
    }

}
