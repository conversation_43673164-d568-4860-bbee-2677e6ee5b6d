package com.cowell.bam.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.cowell.bam.domain.DiffDataCompareInfo;
import com.cowell.bam.domain.DiffDataCompareInfoWithBLOBs;
import com.cowell.bam.enums.SyncTypeEnum;
import com.cowell.bam.repository.mybatis.dao.DiffDataCompareInfoMapper;
import com.cowell.bam.service.HdDataService;
import com.cowell.bam.service.IItemcenterFeignService;
import com.cowell.bam.service.IPriceCenterFeignService;
import com.cowell.bam.service.PriceCompareService;
import com.cowell.bam.service.dto.*;
import com.cowell.bam.entity.ItemPrice;
import com.cowell.bam.service.dto.base.CommonResponse;
import com.cowell.bam.web.rest.util.BigDecimalUtils;
import com.cowell.bam.web.rest.util.ConstantPool;
import com.google.common.collect.Lists;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.task.AsyncTaskExecutor;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date: 2018/12/27 20:31
 * @description:
 */
@Service
public class PriceCompareServiceImpl implements PriceCompareService {
    private Logger log = LoggerFactory.getLogger(PriceCompareServiceImpl.class);


    @Autowired
    private ThirdService thirdService;

    @Autowired
    private IItemcenterFeignService itemcenterFeignService;

    @Autowired
    private IPriceCenterFeignService priceCenterFeignService;


    @Autowired
    private DiffDataCompareInfoMapper diffDataCompareInfoMapper;


    public static final String DATA_ERROR_REASON = "海典推送价格和中台价格不一致";
    public static final String DATA_ERROR_REASON_MEMBER_PRICE = "海典推送会员价格和中台会员价格不一致";
    public static final String DATA_NO_REASON = "海典未推送价格数据到中台";


    @Value("${noticeHDSendData}")
    private String noticeHDSendData;
    @Value("${noticeBJSendData}")
    private String noticeBJSendData;
    @Value("${dbMapConfig}")
    private String dbMapConfigStr;
    @Autowired
    @Qualifier("taskExecutor")
    private AsyncTaskExecutor executor;


    @Autowired
    private SendCompareService sendCompareService;

    @Autowired
    private HdDataService hdDataService;

    /**
     * 对比
     *
     * @return
     */

    @Override
    public boolean compare(String comId) {

        try {
            if (StringUtils.isEmpty(comId)) {
                log.warn("PriceCompareServiceImpl|compare|xxl-job下未配置连锁信息comparePriceConfig:{}", comId);
                return false;
            }
            List<MdmStoreBaseDTO> mdmStoreBaseDTOList = thirdService.findMdmStoreByComId(comId);
            log.info("|PriceCompareServiceImpl|获取连锁门店信息，comId:{},storeSize={}",comId,mdmStoreBaseDTOList == null? 0:mdmStoreBaseDTOList.size());
            if (CollectionUtils.isEmpty(mdmStoreBaseDTOList)) {
                return true;
            }
            List<String> storeNos = mdmStoreBaseDTOList.stream().map(MdmStoreBaseDTO::getStoreNo).collect(Collectors.toList());
            for (String storeNo : storeNos) {
                //根据不同的连锁查询不同的视图
                List<ItemPrice> itemPriceList = hdDataService.queryPriceByComIdAndBusNo(comId,storeNo);
                if (CollectionUtils.isEmpty(itemPriceList)) {
                    log.warn("PriceCompareServiceImpl|compare|门店storeNo:{}下bdp没有抽到库存", storeNo);
                    continue;
                }
                /**获取连锁 门店信息**/
                Map<String, List<ItemPrice>> storePriceListMap = itemPriceList.stream().collect(Collectors.groupingBy(ItemPrice::getBusno));
                storePriceListMap.entrySet().forEach(entry -> {
                    List<ItemPriceResponse> allList = Lists.newArrayList();
                    //价格中心的
                    List<ItemPriceResponse> allListPriceCenter = Lists.newArrayList();

                    ItemPrice comparePrice = entry.getValue().stream().findFirst().get();
                    Long businessId = thirdService.getBussiness(comparePrice.getCompid());
                    Long storeId = thirdService.getStore(comparePrice.getBusno(), comparePrice.getCompid());
                    if (businessId == null || storeId == null) {
                        log.error("PriceCompareServiceImpl|compare|根据mdm连锁门店编码未查询到businessId或storeId,mdm连锁编码:{},mdm门店编码:{}", comparePrice.getCompid(), comparePrice.getBusno());
                        return;
                    }
                    List<List<ItemPrice>> partition = Lists.partition(storePriceListMap.get(entry.getKey()), 50);
                    partition.forEach(list -> {
                        List<String> goodsNoList = list.stream().map(ItemPrice::getWarecode).distinct().collect(Collectors.toList());
                        List<String> hyPriceGoodNoList = list.stream().filter(dto -> StringUtils.isNotBlank(dto.getMemprice())).map(ItemPrice::getWarecode).collect(Collectors.toList());
                        //组装查询商品中台价格数据参数
                        ItemPriceQueryParam param = new ItemPriceQueryParam();
                        param.setBusinessId(businessId);
                        param.setStoreId(storeId);
                        param.setMpriceGoodsNo(hyPriceGoodNoList);
                        param.setPriceGoodsNo(goodsNoList);
                        log.info("PriceCompareServiceImpl|compare|调用itemcenter参数|param={}",JSON.toJSONString(param));

                        //查询商品中心的价格
                        List<ItemPriceResponse> itemList = null;
                        try {
                            itemList = itemcenterFeignService.queryStoreItemPrice(param);
                            if (CollectionUtils.isNotEmpty(itemList)) {
                                log.info("PriceCompareServiceImpl|compare|根据连锁Id，门店id查询到商品:{},{},{}", businessId, storeId,itemList.size());
                                allList.addAll(itemList);
                            }
                        } catch (Exception e) {
                            log.warn("PriceCompareServiceImpl|compare|根据连锁Id，门店id:{},{}查询超时或者为空", businessId, storeId);
                        }


                        //查询价格中台价格
                        PriceQueryParam priceQueryParam = new PriceQueryParam();
                        priceQueryParam.setBusinessId(businessId);
                        priceQueryParam.setStoreId(storeId);
                        priceQueryParam.setGoodsNoList(goodsNoList);
                        List<ItemPriceResponse> itemListPriceCenter = null;
                        try {



                            CommonResponse<List<PriceQueryGoodsNoDTO>> commonResponse = priceCenterFeignService.getPriceAndMemberPrice(priceQueryParam);

                            if (commonResponse.getCode() == 0 && CollectionUtils.isNotEmpty(commonResponse.getResult())){
                                List<PriceQueryGoodsNoDTO> priceQueryGoodsNoDTOList = commonResponse.getResult();
                                log.info("PriceCompareServiceImpl|compare|根据连锁Id，门店id查询到价格中台的商品:{},{},{}", businessId, storeId,priceQueryGoodsNoDTOList.toString());
                                itemListPriceCenter = priceQueryGoodsNoDTOList.stream().map(item->toItemListPriceCenter(item,businessId,storeId)).collect(Collectors.toList());
                                allListPriceCenter.addAll(itemListPriceCenter);
                            }
                        } catch (Exception e) {
                            log.warn("PriceCompareServiceImpl|compare|根据连锁Id，门店id:{},{}查询价格中台超时或者为空", businessId, storeId);
                        }

                    });
                    /**开始比对价格**/
                    checkStoreItemPrice(entry.getValue(), allList,businessId,storeId,null);
                    /**
                     * 开始比对 海典价格和中台价格 version 指定为 1
                     */
                    checkStoreItemPrice(entry.getValue(), allListPriceCenter,businessId,storeId,8);
                });

            }
        } catch (Exception e) {
            log.error("PriceCompareServiceImpl|compare|价格比对失败:{}", comId,e);
        }
        return true;
    }

    private ItemPriceResponse toItemListPriceCenter(PriceQueryGoodsNoDTO dto,Long businessId,Long storeId){


        ItemPriceResponse itemPriceResponse = new ItemPriceResponse();

        BeanUtils.copyProperties(dto,itemPriceResponse);

        itemPriceResponse.setBusinessId(businessId);
        itemPriceResponse.setStoreId(storeId);
        itemPriceResponse.setPrice(BigDecimalUtils.convertYuanByFen(dto.getPrice()));
        itemPriceResponse.setMemberPrice(BigDecimalUtils.convertYuanByFen(dto.getMemberPrice()));

        return itemPriceResponse;
    }

    @Override
    public void checkStoreItemPrice(List<ItemPrice> storeItemPriceList, List<ItemPriceResponse> itemList,Long businessId,Long storeId,Integer version) {
        log.info("PriceCompareServiceImpl|checkStoreItemPrice|checkStoreItemPrice|请求连锁:{} 门店:{} version:{}",businessId,storeId,version);
        Map<String, ItemPriceResponse> itemMap = itemList.stream().collect(Collectors.toMap(item -> item.getGoodsNo(), item -> item, (v1, v2) -> v2));
        List<DiffDataCompareInfoWithBLOBs> diffDataCompareInfos = new ArrayList<>();
        storeItemPriceList.forEach(storeItemPrice -> {
            ItemPriceResponse itemPriceResponse = itemMap.get(storeItemPrice.getWarecode());

            BigDecimal hdPrice = new BigDecimal(storeItemPrice.getSaleprice()).setScale(2, BigDecimal.ROUND_FLOOR);

            if (itemPriceResponse == null) {
                if (new BigDecimal(0).compareTo(hdPrice) == 0) {
                    return;
                }
                log.warn("PriceCompareServiceImpl|checkStoreItemPrice|商品价格比对为找到门店商品|storeItemPrice = {}", storeItemPrice);
                DiffDataCompareInfoWithBLOBs diffDataCompareInfo = new DiffDataCompareInfoWithBLOBs();
                diffDataCompareInfo.setBusinessId(businessId);
                diffDataCompareInfo.setStoreId(storeId);
                diffDataCompareInfo.setReason(DATA_NO_REASON);
                diffDataCompareInfo.setVersion(version);
                getDiffDataCompareInfo(storeItemPrice, itemPriceResponse, diffDataCompareInfo);
                diffDataCompareInfos.add(diffDataCompareInfo);
                return;
            }

            if (StringUtils.isNotBlank(storeItemPrice.getSaleprice())
                && StringUtils.isNotBlank(itemPriceResponse.getPrice())){

                BigDecimal itemPrice = new BigDecimal(itemPriceResponse.getPrice()).setScale(2, BigDecimal.ROUND_FLOOR);

                if (itemPrice.compareTo(hdPrice) != 0) {
                    log.warn("PriceCompareServiceImpl|checkStoreItemPrice||门店商品与海典价格不一致|itemPriceResponse ={},storeItemPrice = {}", itemPriceResponse.toString(), storeItemPrice.toString());
                    DiffDataCompareInfoWithBLOBs diffDataCompareInfo = new DiffDataCompareInfoWithBLOBs();
                    diffDataCompareInfo.setBusinessId(businessId);
                    diffDataCompareInfo.setStoreId(storeId);
                    if (version != null && itemPriceResponse.getIsHasPrice() == 0){
                        diffDataCompareInfo.setReason(DATA_NO_REASON);
                    }else{
                        diffDataCompareInfo.setReason(DATA_ERROR_REASON);
                    }
                    diffDataCompareInfo.setVersion(version);
                    getDiffDataCompareInfo(storeItemPrice, itemPriceResponse, diffDataCompareInfo);
                    diffDataCompareInfos.add(diffDataCompareInfo);
                    return;
                }

            }



            if (StringUtils.isNotBlank(itemPriceResponse.getMemberPrice())
                && StringUtils.isNotBlank(storeItemPrice.getMemprice())){
                BigDecimal hdMemberPrice = new BigDecimal(storeItemPrice.getMemprice()).setScale(2, BigDecimal.ROUND_FLOOR);
                BigDecimal itemMemberPrice = new BigDecimal(itemPriceResponse.getMemberPrice()).setScale(2, BigDecimal.ROUND_FLOOR);
                if (itemMemberPrice.compareTo(hdMemberPrice) != 0) {
                    log.warn("PriceCompareServiceImpl|checkStoreItemPrice||门店商品会员价与海典会员价格不一致|itemPriceResponse ={},storeItemPrice = {}", itemPriceResponse.toString(), storeItemPrice.toString());
                    DiffDataCompareInfoWithBLOBs diffDataCompareInfo = new DiffDataCompareInfoWithBLOBs();
                    diffDataCompareInfo.setBusinessId(businessId);
                    diffDataCompareInfo.setStoreId(storeId);
                    if (version != null && itemPriceResponse.getIsHasMemberPrice() == 0){
                        diffDataCompareInfo.setReason(DATA_NO_REASON);
                    }else{
                        diffDataCompareInfo.setReason(DATA_ERROR_REASON_MEMBER_PRICE);
                    }
                    diffDataCompareInfo.setVersion(version+1);
                    getDiffDataCompareInfo(storeItemPrice, itemPriceResponse, diffDataCompareInfo);
                    diffDataCompareInfos.add(diffDataCompareInfo);
                    return;
                }
            }
        });
        log.info("PriceCompareServiceImpl|checkStoreItemPrice|checkStoreItemPrice|价格差异数据diffDataCompareInfos:{}",diffDataCompareInfos.toString());
        for (DiffDataCompareInfoWithBLOBs diffDataCompareInfo : diffDataCompareInfos) {
            diffDataCompareInfoMapper.insertSelective(diffDataCompareInfo);
        }
        log.info("PriceCompareServiceImpl|checkStoreItemPrice|checkStoreItemPrice|商品价格比对完成!!");

        //对比完成调用海典接口进行查询补偿
        executor.execute(() -> {
            send2HD(diffDataCompareInfos);
        });
    }

    private void getDiffDataCompareInfo(ItemPrice itemPriceBdataDTO, ItemPriceResponse itemPriceResponse, DiffDataCompareInfoWithBLOBs diffDataCompareInfo) {


        PriceDataInfoDTO priceDataInfoDTO = new PriceDataInfoDTO();
        priceDataInfoDTO.setMessage(diffDataCompareInfo.getReason());
        priceDataInfoDTO.setSkuMerchantCode(diffDataCompareInfo.getGoodsNo());

        if (itemPriceResponse != null) {
            BeanUtils.copyProperties(itemPriceResponse, diffDataCompareInfo);
            diffDataCompareInfo.setGoodsNo(itemPriceResponse.getGoodsNo().toString());
            BeanUtils.copyProperties(itemPriceResponse, priceDataInfoDTO);
            priceDataInfoDTO.setMemberPrice(itemPriceResponse.getMemberPrice());
            priceDataInfoDTO.setPrice(priceDataInfoDTO.getPrice());
            priceDataInfoDTO.setSkuMerchantCode(itemPriceResponse.getGoodsNo());
        }

        diffDataCompareInfo.setOurData(JSON.toJSONString(priceDataInfoDTO));

        diffDataCompareInfo.setDataType(SyncTypeEnum.PRICE.getCode());
        ThridPriceInfoDTO thridPriceInfoDTO = new ThridPriceInfoDTO();
        if (itemPriceBdataDTO != null) {
            BeanUtils.copyProperties(itemPriceBdataDTO, thridPriceInfoDTO);
            diffDataCompareInfo.setGoodsNo(itemPriceBdataDTO.getWarecode());
            thridPriceInfoDTO.setPrice(itemPriceBdataDTO.getSaleprice());
            thridPriceInfoDTO.setSkuMerchantCode(itemPriceBdataDTO.getWarecode());
            thridPriceInfoDTO.setHyprice(itemPriceBdataDTO.getMemprice());
            diffDataCompareInfo.setThirdData(JSON.toJSONString(thridPriceInfoDTO));
        }
        diffDataCompareInfo.setCreatedBy("-1");
        diffDataCompareInfo.setGmtCreate(new Date());
        diffDataCompareInfo.setGmtUpdate(new Date());
        diffDataCompareInfo.setStatus(0);
        diffDataCompareInfo.setUpdatedBy("-1");
    }


    /**
     * 回调海典补偿库存价格数据
     *
     * @return
     */
    @Override
    public void send2HD(List<DiffDataCompareInfoWithBLOBs> diffDataCompareInfos) {
        Map<String, List<DiffDataCompareInfo>> collect = diffDataCompareInfos.stream().collect(Collectors.groupingBy(a -> a.getBusinessId() + "_" + a.getStoreId()));
        for (String bid_storeId : collect.keySet()) {
            List<DiffDataCompareInfo> infos = collect.get(bid_storeId);
            CmallReqDTO cmallStockReqDTO = new CmallReqDTO();
            String[] arr = StringUtils.split(bid_storeId, "_");
            cmallStockReqDTO.setCompId(thirdService.getComId(Long.valueOf(arr[0])));
            cmallStockReqDTO.setBusNo(thirdService.transferBusinessIdAndStoreIdToMdmStoreNo(Long.valueOf(arr[0]), Long.valueOf(arr[1])));
            cmallStockReqDTO.setDetails(sendCompareService.getInfoList(infos));
            CompareDataDTO compareDataDTO = new CompareDataDTO();
            compareDataDTO.setBdata(JSON.toJSONString(cmallStockReqDTO));
            compareDataDTO.setComId(cmallStockReqDTO.getCompId());
            compareDataDTO.setBtype(ConstantPool.BTYPE_PRICE);

            //判断如果是邦建 需要换地址
            if (thirdService.isBangJianComId(cmallStockReqDTO.getCompId())){
                compareDataDTO.setRequestUrl(noticeBJSendData+ConstantPool.YK_STOCK_REQUEST_URL);
            }else{
                compareDataDTO.setRequestUrl(noticeHDSendData+ConstantPool.PRICE_REQUEST_URL);
            }

            compareDataDTO.setServiceDesc("价格对比服务");
            sendCompareService.callBackHdCompensate(compareDataDTO);
        }
    }
}
