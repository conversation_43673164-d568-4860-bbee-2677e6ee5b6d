package com.cowell.bam.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.cowell.bam.domain.DiffDataCompareInfo;
import com.cowell.bam.enums.SystemCodeEnum;
import com.cowell.bam.service.dto.*;
import com.cowell.bam.web.rest.errors.ErrorCodeEnum;
import com.cowell.bam.web.rest.util.ConstantPool;
import com.cowell.bam.web.rest.util.ErpRequestConstants;
import com.cowell.bam.web.rest.util.Md5Utils;
import com.google.common.collect.Maps;
import org.apache.commons.lang3.StringUtils;
import org.joda.time.DateTime;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.*;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;
import org.springframework.util.CollectionUtils;
import org.springframework.web.client.RestClientException;
import org.springframework.web.client.RestTemplate;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.UUID;

/**
 * <AUTHOR>
 * @date: 2018/12/27 20:31
 * @description:
 */
@Service
public class SendCompareService {
    private Logger log = LoggerFactory.getLogger(SendCompareService.class);



    @Autowired
    @Qualifier("vanillaRestTemplate")
    private RestTemplate restTemplate;

    @Autowired
    private ThirdService thirdService;

    @Value("${haidianSop:haidian}")
    private String haidian;

    @Value("${yingkeSop:yingke}")
    private String yingke;


    /**
     * 回调海典补偿库存价格数据
     *
     * @return
     */
    public boolean callBackHdCompensate(CompareDataDTO compareDataDTO) {
        Map<String, Object> paramMap = getParamMap(compareDataDTO);

        List<Map<String, Object>> paramList = new ArrayList<>();
        paramList.add(paramMap);

        JSONObject jsonObject = new JSONObject();

        jsonObject.put(ErpRequestConstants.REQUEST_TABLE_FIELD, paramList);
        jsonObject.put("route", "haidian");
        jsonObject.put("comId", compareDataDTO.getComId());
        if (thirdService.isBangJianComId(compareDataDTO.getComId())){
            jsonObject.put("route", yingke);
        }else{
            jsonObject.put("route", haidian);
        }

        SendRequestBodyDTO sendBody = new SendRequestBodyDTO();

        sendBody.setRequestUrl(compareDataDTO.getRequestUrl());

        sendBody.setRequestBody(jsonObject.toJSONString());

        //接口描述信息
        sendBody.setServiceDesc(compareDataDTO.getServiceDesc());
        return sendRequest(sendBody);

    }

    private Map<String, Object> getParamMap(CompareDataDTO compareDataDTO) {
        Map<String, Object> paramMap = Maps.newHashMap();
        String bguid = String.format("%s%s", "STOCK", UUID.randomUUID().toString());
        bguid = org.apache.commons.lang3.StringUtils.replace(bguid, "-", "");
        paramMap.put(ErpRequestConstants.REQUEST_B_G_UID_FIELD, bguid);
        paramMap.put(ErpRequestConstants.REQUEST_B_TYPE_FIELD, compareDataDTO.getBtype());
        paramMap.put(ErpRequestConstants.REQUEST_B_SOURCE_FIELD, SystemCodeEnum.POS.getValue());
        paramMap.put(ErpRequestConstants.REQUEST_B_KEYS_FIELD, "");
        paramMap.put(ErpRequestConstants.REQUEST_B_STATUS_FIELD, ConstantPool.POS_RESPONSE_STATUS_SENT);
        paramMap.put(ErpRequestConstants.REQUEST_B_DESTINATION_FIELD, SystemCodeEnum.HD_POS.getValue());
        paramMap.put(ErpRequestConstants.REQUEST_B_DATETIME_FIELD, DateTime.now().toString(ErpRequestConstants.REQUEST_DATE_FORMAT_PATTERN));
        paramMap.put(ErpRequestConstants.REQUEST_B_CALLBACK_FIELD, null);
        paramMap.put(ErpRequestConstants.REQUEST_B_VERSION_FIELD, "v_1_1");

        paramMap.put(ErpRequestConstants.REQUEST_B_DATA_FIELD, compareDataDTO.getBdata());

        paramMap.put(ErpRequestConstants.REQUEST_B_DATAHASH_FIELD, Md5Utils.MD5Encode(compareDataDTO.getBdata()));
        return paramMap;
    }

    private boolean sendRequest(SendRequestBodyDTO sendBody) {
        try {
            log.info("AbstractCompareService|sendRequest|sendBody:【{}】", JSON.toJSONString(sendBody));

            HttpHeaders requestHeaders = new HttpHeaders();
            MediaType type = MediaType.parseMediaType("application/json; charset=UTF-8");
            requestHeaders.setContentType(type);
            requestHeaders.add("Accept", MediaType.APPLICATION_JSON.toString());

            HttpEntity<JSONObject> requestEntity = new HttpEntity(sendBody.getRequestBody(), requestHeaders);

            log.info("AbstractCompareService|sendRequest|开始发送请求|param:url={}, userName={},password={},requestBody={}",
                sendBody.getRequestUrl(), sendBody.getUserName(), sendBody.getPassWord(), sendBody.getRequestBody());

            ResponseEntity<String> responseEntity = restTemplate.exchange(sendBody.getRequestUrl(), HttpMethod.POST, requestEntity, String.class);
            log.info("AbstractCompareService|sendRequest|调用海典返回值:responseEntity:{}",responseEntity);
            Assert.notNull(requestEntity, ErrorCodeEnum.HTTP_RESPONSE_FAIL.getMsg());

            String objStr = responseEntity.getBody();

            if (StringUtils.isEmpty(objStr)) {
                log.warn("AbstractCompareService|sendRequest|调用海典返回值:responseEntity:{}",responseEntity);
                return false;
               // return responseEntity.getStatusCode() + "";
            }

            JSONObject jsonObject = (JSONObject) JSON.parse(objStr);
            String result = jsonObject.getJSONArray("Table").getJSONObject(0).getJSONObject("bdata").getString("code");
            if ("200".equals(result)) {
                return true;
            }
            log.error("AbstractCompareService|sendRequest|调用海典返回错误:{}",jsonObject);
            return false;
        } catch (Exception e) {
            log.error("SendCompareService|error",e);
            return false;
        }
    }

    /**
     * 库存价格用
     * @param infos
     * @return
     */
    public List<CompareDataInfo> getInfoList(List<DiffDataCompareInfo> infos) {
        List<CompareDataInfo> infoList = new ArrayList<>();
        if (CollectionUtils.isEmpty(infos)) {
            return infoList;
        }
        for (int i = 0; i < infos.size(); i++) {
            DiffDataCompareInfo diffDataCompareInfo = infos.get(i);
            CompareDataInfo info =new CompareDataInfo();
            if (StringUtils.isEmpty(diffDataCompareInfo.getGoodsNo())) {
                continue;
            }
            info.setRowNo(i);
            info.setGoodsNo(diffDataCompareInfo.getGoodsNo());
            infoList.add(info);
        }
        return infoList;
    }
}
