package com.cowell.bam.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.cowell.bam.config.DynamicBeanRegistry;
import com.cowell.bam.entity.GoodsCounterDO;
import com.cowell.bam.entity.ItemPrice;
import com.cowell.bam.entity.Stock;
import com.cowell.bam.entity.WareDxDateVO;
import com.cowell.bam.enums.PriceTypeEnum;
import com.cowell.bam.service.HdDataService;
import com.cowell.bam.service.IStoreSyncFeignService;
import com.cowell.bam.service.dto.*;
import com.cowell.bam.web.rest.CompareDataResource;
import com.cowell.bam.web.rest.util.DateUtil;
import com.cowell.bam.web.rest.vo.ApiParamVo;
import com.cowell.bam.web.rest.vo.HdDxParamVO;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.jdbc.core.BeanPropertyRowMapper;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.jdbc.core.namedparam.NamedParameterJdbcTemplate;
import org.springframework.stereotype.Service;

import java.util.*;

/**
 * <AUTHOR>
 * @ProjectName bam
 * @Description:
 * @date 2019/10/25 11:51
 */
@Service
public class HdDataServiceImpl implements HdDataService{

    private final Logger log = LoggerFactory.getLogger(CompareDataResource.class);

    @Autowired
    private IStoreSyncFeignService storeFeignService;
    @Autowired
    private ThirdService thirdService;

    @Value("${dbMapConfig}")
    private String dbMapConfigStr;

    @Qualifier("hdJdbcTemplate")
    @Autowired
    public JdbcTemplate hdJdbcTemplate;

    @Qualifier("hbJdbcTemplate")
    @Autowired
    private JdbcTemplate hbJdbcTemplate;

    @Qualifier("hzJdbcTemplate")
    @Autowired
    private JdbcTemplate hzJdbcTemplate;

    @Qualifier("xbJdbcTemplate")
    @Autowired
    private JdbcTemplate xbJdbcTemplate;

    @Qualifier("bjJdbcTemplate")
    @Autowired
    private JdbcTemplate bjJdbcTemplate;

    @Qualifier("xszkJdbcTemplate")
    @Autowired
    private JdbcTemplate xszkJdbcTemplate;

    @Qualifier("hnJdbcTemplate")
    @Autowired
    private JdbcTemplate hnJdbcTemplate;

    @Qualifier("hubeiJdbcTemplate")
    @Autowired
    private JdbcTemplate hubeiJdbcTemplate;

    @Qualifier("xjJdbcTemplate")
    @Autowired
    private JdbcTemplate xjJdbcTemplate;

    @Qualifier("xnNewJdbcTemplate")
    @Autowired
    private JdbcTemplate xnNewJdbcTemplate;

    @Qualifier("longyiNewJdbcTemplate")
    @Autowired
    private JdbcTemplate longyiNewJdbcTemplate;

    @Qualifier("xn1JdbcTemplate")
    @Autowired
    private JdbcTemplate xn1JdbcTemplate;

    @Value("${xszkComIdMapping}")
    private String xszkComIdMapping;

    public static final String BANGJIANDBSTR = "bj";

    @Autowired
    private DynamicBeanRegistry dynamicBeanRegistry;

//    @Qualifier("xbJdbcTemplate")
//    @Autowired
//    private JdbcTemplate xbJdbcTemplate;


    @Override
    public Integer queryHdData(String comid) {
        JdbcTemplate jdbcTemplate = getJdbcTemplate(comid);
        String sql = "select count(0) from h2.v_xls_jg where compid=?";
        return jdbcTemplate.queryForObject(sql,Integer.class,comid);
    }

    @Override
    public List<Stock> queryHdTransitStockData(String comId, String storeNo) {
        JdbcTemplate jdbcTemplate = getJdbcTemplate(comId);
        String sql = "select compid, busno, warecode, ZTQTY_QH ztqtyqh, ZTQTY_DB ztqtydb, ZTQTY_PH ztqtyph from h2.v_hd_wareqty_zt where compid = ? and busno = ? \n";

        List<Stock> list = jdbcTemplate.query(sql, new Object[]{comId, storeNo}, new BeanPropertyRowMapper<>(Stock.class));
        return list;
    }

    @Override
    public List<Stock> queryHdStockData(String comId, String storeNo) {
        JdbcTemplate jdbcTemplate = getJdbcTemplate(comId);
        String sql = " select busno,compid,orgname,warecode,barcode,max(makeno) makeno,expiredate,unit,sum(tnum) tnum,sum(num) num,syncdate," +
            " batchno,zonename,sum(qualifiedstock) qualifiedstock,sum(checkstock) checkstock,sum(unqualifiedstock) unqualifiedstock,expdate_type expDateType,max(synctime) syncTime  \n" +
            " from h2.v_xls_kc \n" +
            " where compid = ? and busno = ? and batchno is not null and warecode not like '82%' and warecode not like '83%' and warecode not like '84%' " +
            " group by busno,compid,orgname,warecode,barcode,expiredate,unit,syncdate,batchno,zonename,expdate_type ";

        List<Stock> list = jdbcTemplate.query(sql, new Object[]{comId, storeNo}, new BeanPropertyRowMapper<>(Stock.class));
        return list;
    }

    @Override
    public List<Stock> queryHdStockGoodsData(String comId, String storeNo) {
        JdbcTemplate jdbcTemplate = getJdbcTemplate(comId);
        String sql = " select busno,compid,warecode  \n" +
            " from h2.v_xls_kc \n" +
            " where compid = ? and busno = ? and batchno is not null and warecode not like '82%' and warecode not like '83%' and warecode not like '84%' " +
            " group by busno,compid,warecode ";

        List<Stock> list = jdbcTemplate.query(sql, new Object[]{comId, storeNo}, new BeanPropertyRowMapper<>(Stock.class));
        return list;
    }

    @Override
    public List<Stock> queryBjStockData(String comId, String storeNo) {
        //1查询总页数
        List<Stock> allList = Lists.newArrayList();
        JdbcTemplate jdbcTemplate = getJdbcTemplate(comId);
        String queryTotalSql = " select count(0) as total from  v_xls_kc where busno = ? ";
        List<Stock> stockList = jdbcTemplate.query(queryTotalSql, new Object[]{storeNo}, new BeanPropertyRowMapper<>(Stock.class));
        //Stock stock = jdbcTemplate.queryForObject(queryTotalSql, Stock.class);
        if (CollectionUtils.isEmpty(stockList)) {
            return allList;
        }
        Stock stock = stockList.stream().findFirst().get();
        // 2.分页查询
        int  pageSize = 500;
        int pageTotal = stock.getTotal() / pageSize + 1;
        for (int i = 0; i < pageTotal; i++) {
            String sql = "SELECT busno,compid,warecode,makeno,expiredate,unit,tnum tnum,num num,syncdate," +
                 " batchno,qualifiedstock,checkstock,unqualifiedstock FROM (SELECT t.*,ROWNUM rn FROM V_XLS_KC t WHERE BUSNO = ? order by warecode,makeno,batchno) WHERE rn > ? AND rn <= ? ";

//            String sql = " select busno,compid,orgname,warecode,barcode,makeno,expiredate,unit,tnum tnum,num num,syncdate," +
//                " batchno,qualifiedstock,checkstock,unqualifiedstock " +
//                " from  v_xls_kc where busno = ? ";
            log.info("HdDataServiceImpl|queryBjStockData|邦健分页查询page:{}",i);
            long tt = System.currentTimeMillis();
            List<Stock> list = jdbcTemplate.query(sql, new Object[]{storeNo,i* pageSize,(i+1) * pageSize}, new BeanPropertyRowMapper<>(Stock.class));
            log.info("HdDataServiceImpl|queryBjStockData|邦健分页查询返回值:{},用时:{}",list.size(),System.currentTimeMillis()-tt);
            allList.addAll(list);
        }
        return allList;
    }

    @Override
    public List<Stock> queryAllBjStockData(String comId, String storeNo) {

        if (StringUtils.isBlank(comId) || StringUtils.isBlank(storeNo)) {
            return Lists.newArrayList();
        }

        JdbcTemplate jdbcTemplate = getJdbcTemplate(comId);
        String sql = "SELECT busno,compid,warecode,makeno,expiredate,unit,tnum tnum,num num,syncdate," +
            " batchno,qualifiedstock,checkstock,unqualifiedstock FROM (SELECT t.*,ROWNUM rn FROM V_XLS_KC t WHERE BUSNO = ? ) ";

//        long start = System.currentTimeMillis();
        List<Stock> list = jdbcTemplate.query(sql, new Object[]{storeNo}, new BeanPropertyRowMapper<>(Stock.class));
//        log.info("HdDataServiceImpl|queryAllBjStockData|查询邦健门店下所有商品|comId={},storeNo={},size={},time={}",
//            comId, storeNo, list.size(), System.currentTimeMillis() - start);
        return list;
    }

    @Override
    public List<Stock> queryAllBjStockGoodsData(String comId, String storeNo) {

        if (StringUtils.isBlank(comId) || StringUtils.isBlank(storeNo)) {
            return Lists.newArrayList();
        }

        JdbcTemplate jdbcTemplate = getJdbcTemplate(comId);
        String sql = "SELECT busno,compid,warecode FROM (SELECT t.*,ROWNUM rn FROM V_XLS_KC t WHERE BUSNO = ? ) ";

//        long start = System.currentTimeMillis();
        List<Stock> list = jdbcTemplate.query(sql, new Object[]{storeNo}, new BeanPropertyRowMapper<>(Stock.class));
//        log.info("HdDataServiceImpl|queryAllBjStockData|查询邦健门店下所有商品|comId={},storeNo={},size={},time={}",
//            comId, storeNo, list.size(), System.currentTimeMillis() - start);
        return list;
    }

    @Override
    public Integer queryYKStoreCount(String comId,String storeNo) {
        JdbcTemplate jdbcTemplate = getJdbcTemplate(comId);
        String queryTotalSql = " select count(0) as total from  v_xls_kc where busno = ? ";
        List<Stock> stockList = jdbcTemplate.query(queryTotalSql, new Object[]{storeNo}, new BeanPropertyRowMapper<>(Stock.class));

        if (CollectionUtils.isEmpty(stockList)) {
            log.info("HdDataServiceImpl|queryYKStoreCount|查询总数为空|comId={},storeNo={}", comId, storeNo);
            return 0;
        }
        Stock stock = stockList.stream().findFirst().get();
        log.info("HdDataServiceImpl|queryYKStoreCount|获取门店总数|comId={},storeNo={},total={}", comId, storeNo, stock.getTotal());
        return stock.getTotal();

    }

    @Override
    public String getDBSource(String comId){
        Map<String, String> dbMapConfig = JSONObject.parseObject(dbMapConfigStr, Map.class);
        String envPath = null;
        for (Map.Entry<String, String> entry : dbMapConfig.entrySet()) {
            if (entry.getValue().contains(comId)) {
                envPath = entry.getKey();
                break;
            }
        }
        return envPath;
    }

    @Override
    public List<Stock> queryBjStockData(Long businessId, Long storeId, List<String> goodsNos) {
        MdmDataTransformDTO mdmStoreInfo = getMdmStoreInfo(businessId, storeId);
        JdbcTemplate jdbcTemplate = getJdbcTemplate(mdmStoreInfo.getComId());
        String goodsStr = "";
        if (CollectionUtils.isNotEmpty(goodsNos)) {
            String goodsNoStr = " ";
            for (String goodsNo : goodsNos) {
                goodsNoStr += "'"+goodsNo +"',";
            }
            goodsStr = " and warecode in ("+StringUtils.substring(goodsNoStr,0,goodsNoStr.length()-1)+")";
        }

        String sql = " select busno,compid,orgname,warecode,barcode,makeno,expiredate,unit,tnum tnum,num num,syncdate," +
            " batchno,qualifiedstock,checkstock,unqualifiedstock " +
            " from  v_xls_kc where busno = ? " +
            goodsStr ;
        List<Stock> list = jdbcTemplate.query(sql, new Object[]{mdmStoreInfo.getStoreNos().get(0)}, new BeanPropertyRowMapper<>(Stock.class));
        log.info("|HdDataServiceImpl|queryHdStockData|查询海典库存数据返回|result={},comId:{},storeNo:{}",list.size(),mdmStoreInfo.getComId(),mdmStoreInfo.getStoreNos().get(0));
        return list;
    }

    @Override
    public List<Stock> queryBjStockData(String comId, String storeNo, List<String> goodsNos) {
        JdbcTemplate jdbcTemplate = getJdbcTemplate(comId);
        String goodsStr = "";
        if (CollectionUtils.isNotEmpty(goodsNos)) {
            String goodsNoStr = " ";
            for (String goodsNo : goodsNos) {
                goodsNoStr += "'"+goodsNo +"',";
            }
            goodsStr = " and warecode in ("+StringUtils.substring(goodsNoStr,0,goodsNoStr.length()-1)+")";
        }

        String sql = " select busno,compid,orgname,warecode,barcode,makeno,expiredate,unit,tnum tnum,num num,syncdate," +
            " batchno,qualifiedstock,checkstock,unqualifiedstock " +
            " from  v_xls_kc where busno = ? " +
            goodsStr ;
        List<Stock> list = jdbcTemplate.query(sql, new Object[]{storeNo}, new BeanPropertyRowMapper<>(Stock.class));
        log.info("|HdDataServiceImpl|queryHdStockData|查询英克库存数据返回|result={},comId:{},storeNo:{}",list.size(), comId, storeNo);
        return list;
    }


    @Override
    public List<Stock> queryHdStockData(Long businessId, Long storeId, List<String> goodsNos) {
        log.info("|HdDataServiceImpl|queryHdStockData|查询海典库存数据goodsNos|businessId={},storeId={}", businessId, storeId);
        MdmDataTransformDTO mdmStoreInfo = getMdmStoreInfo(businessId, storeId);
        log.info("|HdDataServiceImpl|queryHdStockData|查询海典库存数据goodsNos|mdmStoreInfo={}", mdmStoreInfo);
        Map<String, Object> paramMap = Maps.newHashMap();
        String busNo = mdmStoreInfo.getStoreNos().get(0);
        paramMap.put("compId", mdmStoreInfo.getComId());
        paramMap.put("busNo", busNo);
        paramMap.put("wareCodes", goodsNos);
        String sql = " select busno,compid,orgname,warecode,barcode,makeno,expiredate,unit,tnum,num,syncdate," +
            "   batchno,zonename,qualifiedstock,checkstock,unqualifiedstock \n" +
            "        from h2.v_xls_kc \n" +
            "  where compid =:compId and busno =:busNo and warecode in (:wareCodes) ";
        NamedParameterJdbcTemplate jdbcTemplate = new NamedParameterJdbcTemplate(getJdbcTemplate(mdmStoreInfo.getComId()));
        List<Stock> list = jdbcTemplate.query(sql, paramMap, new BeanPropertyRowMapper<>(Stock.class));
        return list;
    }


    @Override
    public List<Stock> queryHdStockInfo(String comId, String busNo, List<String> goodsNos) {
        log.info("HdDataServiceImpl|queryHdStockInfo|查询海典库存数据comId={},busNo={}", comId, busNo);

        Map<String, Object> paramMap = Maps.newHashMap();
        paramMap.put("compId", comId);
        paramMap.put("busNo", busNo);
        paramMap.put("wareCodes", goodsNos);
        String sql = " select busno,compid,orgname,warecode,barcode,makeno,expiredate,unit,tnum,num,syncdate," +
            "   batchno,zonename,qualifiedstock,checkstock,unqualifiedstock,expdate_type expDateType,synctime syncTime \n" +
            "        from h2.v_xls_kc \n" +
            "  where compid =:compId and busno =:busNo and warecode in (:wareCodes) ";
        return new NamedParameterJdbcTemplate(getJdbcTemplate(comId))
            .query(sql, paramMap, new BeanPropertyRowMapper<>(Stock.class));
    }

    @Override
    public List<Stock> queryPosStockInfo(String comId, String busNo, List<String> goodsNos) {
        String dbSource = getDBSource(comId);
        List<Stock> stockList;
        if (org.apache.commons.lang3.StringUtils.isEmpty(dbSource) || !BANGJIANDBSTR.equals(dbSource)) {
            log.info("StockCompareServiceImpl|compare|查询海典数据库开始:comId:{},storeNo:{}", comId, busNo);
            stockList = queryHdStockInfo(comId, busNo, goodsNos);
            log.info("StockCompareServiceImpl|compare|查询海典数据库结束result:{}", stockList.size());
        } else {
            log.info("StockCompareServiceImpl|compare|查询所有英克数据库开始:comId:{},storeNo:{}", comId, busNo);
            stockList = queryBjStockData(comId, busNo, goodsNos);
            log.info("StockCompareServiceImpl|compare|查询英克数据库结束result:{}", stockList.size());
        }

        return stockList;
    }

    @Override
    public List<GoodsCounterDO> queryGoodsCounterInfo(ApiParamVo apiParamVo) {
        log.info("HdDataServiceImpl|queryGoodsCounterInfo|查询海典货位数据businessId={},storeId={},goodsNo={}",apiParamVo.getBusinessId(),  apiParamVo.getStoreId(),  apiParamVo.getGoodsNos());
        if(apiParamVo.getBusinessId()==null || apiParamVo.getStoreId()==null || CollectionUtils.isEmpty(apiParamVo.getGoodsNos())){
            return new ArrayList<>();
        }
        MdmDataTransformDTO mdmStoreInfo = getMdmStoreInfo(apiParamVo.getBusinessId(), apiParamVo.getStoreId());

        if(CollectionUtils.isEmpty(mdmStoreInfo.getStoreNos()) || StringUtils.isEmpty(mdmStoreInfo.getComId())){
            return new ArrayList<>();
        }
        String compId = mdmStoreInfo.getComId();
        String busNo = mdmStoreInfo.getStoreNos().get(0);

        Map<String, Object> paramMap = Maps.newHashMap();
        paramMap.put("compId", compId);
        paramMap.put("busNo", busNo);
        paramMap.put("wareCodes", apiParamVo.getGoodsNos());
        String sql = " select scrm_compid,mdm_busno,warecode as warecode,counterno as counterno,countername as countername" +
            "        from h2.hd_hj_view \n" +
            "  where SCRM_COMPID =:compId and MDM_BUSNO =:busNo and WARECODE in (:wareCodes)  ";
        return new NamedParameterJdbcTemplate(getJdbcTemplate(compId))
            .query(sql, paramMap, new BeanPropertyRowMapper<>(GoodsCounterDO.class));
    }

    @Override
    public List<ItemPrice> queryPriceByComIdAndBusNo(String comId, String busNo) {
        return this.queryPriceDataListCommon(null,null,comId,busNo,new ArrayList<>());
    }

    @Override
    public List<ItemPrice> queryPriceGoodsNoByComIdAndBusNo(String comId, String busNo) {
        return this.queryPriceGoodsNo(comId, busNo);
    }

    @Override
    public List<ItemPrice> queryPriceByComIdAndBusNoAndDate(String comId, String busNo, String queryDate) {
        return queryPriceDataListCommon2(null,null,comId,busNo,null,queryDate);
    }

    @Override
    public List<ItemPrice> queryPriceByBusinessIdAndStoreIdAndGoods(Long businessId, Long storeId, List<String> goodsNos) {
        return this.queryPriceDataListCommon(businessId,storeId,null,null,goodsNos);
    }

    @Override
    public List<ItemPrice> queryPriceByComIdAndBusNoAndGoods(String comId, String busNo, List<String> goodsNos) {
        return this.queryPriceDataListCommon(null,null,comId,busNo,goodsNos);
    }

    @Override
    public List<ItemPrice> queryPriceCount(Long businessId, Long storeId, String comId, String busNo) {
        return this.queryPriceDataCountCommon(businessId,storeId,comId,busNo);
    }

    @Override
    public List<ItemPrice> queryPriceCountByData(Long businessId, Long storeId, String comId, String busNo, String queryDate) {
        return null;
    }

    @Override
    public List<HdOrderDTO> queryHdOrderInfo(String orderCreateTime, String orderEndTime ,String dataSource,Integer pageNumber,Integer pageSize) {

        if (pageNumber < 1) {
            pageNumber = 1;
        }
        Map<String, Object> paramMap = Maps.newHashMap();
        paramMap.put("startDate", orderCreateTime);
        paramMap.put("endDate", orderEndTime);
        paramMap.put("startNum", pageNumber * pageSize - pageSize + 1);
        paramMap.put("endNum", pageNumber * pageSize + 1);

        String sql = "select V.scrm_compid as scrmCompid," +
            " V.mdm_busno as mdmBusno," +
            " V.cashno," +
            " V.scrm_userid as scrmUserid," +
            " V.createtime as createTime," +
            " V.integral_pst as integralPst," +
            " V.status from " +
            " (SELECT ROWNUM RN,scrm_compid,mdm_busno,cashno,scrm_userid," +
            " createtime,integral_pst,status from h2.V_ACC_POINTS_DESTROY " +
            " WHERE createtime > to_date(:startDate , 'yyyy-mm-dd hh24:mi:ss') and createtime < to_date(:endDate , 'yyyy-mm-dd hh24:mi:ss') order by createtime) V " +
            " WHERE V.RN >= :startNum AND V.RN <:endNum";
        log.info("queryHdOrderInfo-sql:{}, param:{}", sql, paramMap);
        long tt = System.currentTimeMillis();
        NamedParameterJdbcTemplate jdbcTemplate = new NamedParameterJdbcTemplate(getHdJdbcTemplate(dataSource));
        List<HdOrderDTO> list = jdbcTemplate.query(sql, paramMap, new BeanPropertyRowMapper<>(HdOrderDTO.class));
        log.info("queryHdOrderInfo-sql:{}, param:{}， costTime:{}", sql, paramMap, System.currentTimeMillis() - tt);
        return list;
    }

    @Override
    public List<HdOrderDTO> queryHdOrderInfo(String orderCreateTime, String orderEndTime, String dataSource, Integer status, Integer pageNumber, Integer pageSize) {
        if (pageNumber < 1) {
            pageNumber = 1;
        }
        Map<String, Object> paramMap = Maps.newHashMap();
        paramMap.put("startDate", orderCreateTime);
        paramMap.put("endDate", orderEndTime);
        paramMap.put("startNum", pageNumber * pageSize - pageSize + 1);
        paramMap.put("endNum", pageNumber * pageSize + 1);
        paramMap.put("statusParam", status);

        String sql = "select V.scrm_compid as scrmCompid," +
            " V.mdm_busno as mdmBusno," +
            " V.cashno," +
            " V.scrm_userid as scrmUserid," +
            " V.createtime as createTime," +
            " V.integral_pst as integralPst," +
            " V.status from " +
            " (SELECT ROWNUM RN,scrm_compid,mdm_busno,cashno,scrm_userid," +
            " createtime,integral_pst,status from h2.V_ACC_POINTS_DESTROY " +
            " WHERE status=:statusParam and createtime > to_date(:startDate , 'yyyy-mm-dd hh24:mi:ss') and createtime < to_date(:endDate , 'yyyy-mm-dd hh24:mi:ss') order by createtime) V " +
            " WHERE V.RN >= :startNum AND V.RN <:endNum";
        log.info("queryHdOrderInfo-sql:{}, param:{}", sql, paramMap);
        long tt = System.currentTimeMillis();
        NamedParameterJdbcTemplate jdbcTemplate = new NamedParameterJdbcTemplate(getHdJdbcTemplate(dataSource));
        List<HdOrderDTO> list = jdbcTemplate.query(sql, paramMap, new BeanPropertyRowMapper<>(HdOrderDTO.class));
        log.info("queryHdOrderInfo-sql:{}, param:{}， costTime:{}", sql, paramMap, System.currentTimeMillis() - tt);
        return list;
    }

    @Override
    public List<HdOrderDTO> queryHdOrderViewByScrmUserIdAndcashnoAndScrmCompid(Long scrmUserid, String cashno, String scrmCompid) {
        Map<String, Object> paramMap = Maps.newHashMap();
        paramMap.put("scrmUserid", scrmUserid);
        paramMap.put("cashno", cashno);
        paramMap.put("scrmCompid", scrmCompid);

        String sql = "select scrm_compid as scrmCompid," +
                            "mdm_busno as mdmBusno," +
                            "cashno," +
                            "scrm_userid as scrmUserid," +
                            "createtime as createTime," +
                            "integral_pst as integralPst," +
                            "status from " +
                                    " h2.V_ACC_POINTS_DESTROY " +
                                            " where scrm_userid =:scrmUserid and cashno =:cashno and scrm_compid = :scrmCompid";
        log.info("queryHdOrderViewByScrmUserIdAndcashnoAndScrmCompid-sql:{}, param:{}", sql, paramMap);
        NamedParameterJdbcTemplate jdbcTemplate = new NamedParameterJdbcTemplate(getJdbcTemplate(scrmCompid));
        List<HdOrderDTO> list = jdbcTemplate.query(sql, paramMap, new BeanPropertyRowMapper<>(HdOrderDTO.class));
        return list;
    }

    /**
     * 获取MDM门店信息
     *
     * @param businessId
     * @param storeId
     * @return
     */
    @Override
    public MdmDataTransformDTO getMdmStoreInfo(Long businessId, Long storeId) {
        MdmDataTransformDTO mdmDataTransformDTO = new MdmDataTransformDTO();
        mdmDataTransformDTO.setTransFormType(1);
        mdmDataTransformDTO.setBusinessId(String.valueOf(businessId));
        if(storeId != null){
            mdmDataTransformDTO.setStoreIds(Lists.newArrayList(storeId));
            mdmDataTransformDTO.setDataType(2);
        }else {
            mdmDataTransformDTO.setDataType(1);
        }
        MdmDataTransformDTO mdmStoreInfo = storeFeignService.transformMdmData(mdmDataTransformDTO);
        return mdmStoreInfo;
    }

    @Override
    public JdbcTemplate getJdbcTemplate(String comid) {
        Map<String, String> dbMapConfig = JSONObject.parseObject(dbMapConfigStr, Map.class);
        log.info("|HdDataServiceImpl|getJdbcTemplate|获取数据源|dbMapConfig:{}|comid:{}", dbMapConfig, comid);
        String envPath = "";
        for (Map.Entry<String, String> entry : dbMapConfig.entrySet()) {
            if (entry.getValue().contains(comid)) {
                envPath = entry.getKey();
                break;
            }
        }
        log.info("|HdDataServiceImpl|getJdbcTemplate|envPath={}", envPath);
        JdbcTemplate jdbcTemplate = dynamicBeanRegistry.getJdbcTemplate(envPath);
        if (jdbcTemplate != null) {
            return jdbcTemplate;
        }
        switch (envPath) {
            case "hd":
                log.info("|HdDataServiceImpl|getJdbcTemplate|获取数据源|envPath:{}", "hd");
                return hdJdbcTemplate;
            case "hb":
                log.info("|HdDataServiceImpl|getJdbcTemplate|获取数据源|envPath:{}", "hb");
                return hbJdbcTemplate;
            case "hz":
                log.info("|HdDataServiceImpl|getJdbcTemplate|获取数据源|envPath:{}", "hz");
                return hzJdbcTemplate;
            case "xb":
                log.info("|HdDataServiceImpl|getJdbcTemplate|获取数据源|envPath:{}", "xb");
                return xbJdbcTemplate;
            case "bj":
                log.info("|HdDataServiceImpl|getJdbcTemplate|获取数据源|envPath:{}", "bj");
                return bjJdbcTemplate;
            case "xszk":
                log.info("|HdDataServiceImpl|getJdbcTemplate|获取数据源|envPath:{}", "xszk");
                return xszkJdbcTemplate;
            case "hn":
                log.info("|HdDataServiceImpl|getJdbcTemplate|获取数据源|envPath:{}", "hn");
                return hnJdbcTemplate;
            case "hubei":
                log.info("|HdDataServiceImpl|getJdbcTemplate|获取数据源|envPath:{}", "hubei");
                return hubeiJdbcTemplate;
            case "xj":
                log.info("|HdDataServiceImpl|getJdbcTemplate|获取数据源|envPath:{}", "xj");
                return xjJdbcTemplate;
            case "xn_new":
                log.info("|HdDataServiceImpl|getJdbcTemplate|获取数据源|envPath:{}", "xn_new");
                return xnNewJdbcTemplate;
            case "longyi_new":
                log.info("|HdDataServiceImpl|getJdbcTemplate|获取数据源|envPath:{}", "longyi_new");
                return longyiNewJdbcTemplate;
            case "xn_1":
                log.info("|HdDataServiceImpl|getJdbcTemplate|获取数据源|envPath:{}", "xn_1");
                return xn1JdbcTemplate;
            default:
                return hdJdbcTemplate;
        }
    }

    @Override
    public JdbcTemplate getHdJdbcTemplate(String dataSource) {
        JdbcTemplate jdbcTemplate = dynamicBeanRegistry.getJdbcTemplate(dataSource);
        if (jdbcTemplate != null) {
            return jdbcTemplate;
        }
        switch (dataSource) {
            case "hd":
                log.info("|HdDataServiceImpl|getJdbcTemplate|获取数据源|envPath:{}", "hd");
                return hdJdbcTemplate;
            case "hb":
                log.info("|HdDataServiceImpl|getJdbcTemplate|获取数据源|envPath:{}", "hb");
                return hbJdbcTemplate;
            case "hz":
                log.info("|HdDataServiceImpl|getJdbcTemplate|获取数据源|envPath:{}", "hz");
                return hzJdbcTemplate;
            case "xb":
                log.info("|HdDataServiceImpl|getJdbcTemplate|获取数据源|envPath:{}", "bj");
                return xbJdbcTemplate;
            case "bj":
                log.info("|HdDataServiceImpl|getJdbcTemplate|获取数据源|envPath:{}", "bj");
                return bjJdbcTemplate;
            case "xszk":
                log.info("|HdDataServiceImpl|getJdbcTemplate|获取数据源|envPath:{}", "xszk");
                return xszkJdbcTemplate;
            case "hn":
                log.info("|HdDataServiceImpl|getJdbcTemplate|获取数据源|envPath:{}", "hn");
                return hnJdbcTemplate;
            case "hubei":
                return hubeiJdbcTemplate;
            case "xj":
                log.info("|HdDataServiceImpl|getJdbcTemplate|获取数据源|envPath:{}", "xj");
                return xjJdbcTemplate;
            case "xn_new":
                log.info("|HdDataServiceImpl|getJdbcTemplate|获取数据源|envPath:{}", "xn_new");
                return xnNewJdbcTemplate;
            case "longyi_new":
                log.info("|HdDataServiceImpl|getJdbcTemplate|获取数据源|envPath:{}", "longyi_new");
                return longyiNewJdbcTemplate;
            case "xn_1":
                log.info("|HdDataServiceImpl|getJdbcTemplate|获取数据源|envPath:{}", "xn_1");
                return xn1JdbcTemplate;
            default:
                return hdJdbcTemplate;
        }
    }

    /**
     * 查询价格数量 根据连锁 和 门店
     * @param comId
     * @param busNo
     * @return
     */
    private List<ItemPrice> queryPriceGoodsNo(String comId, String busNo){
//        log.info("查询价格数量入参|businessId={}|storeId={}|comId={}|busNo={}|goodsNos={}|queryDate={}", businessId, storeId, comId, busNo, JSONObject.toJSONString(goodsNos), queryDate);
        String comIdCur = comId;

        List<ItemPrice> list = Lists.newArrayList();
        String sql = "";
        //是否是 邦建（英克）
        boolean isBangJianComId = thirdService.isBangJianComId(comIdCur,"bj");
        boolean isXszkComId = thirdService.isBangJianComId(comIdCur,"xszk");
        if (isBangJianComId){
            sql = " select warecode from zx_togj_ngpcs_all_price_v where compid =:compId and busno =:busNo ";
        }else if (isXszkComId){
            comIdCur = this.getXszkComIdMapping(comIdCur);
            sql = " select warecode from h2.v_xls_jg where compid =:compId and busno =:busNo ";
        }else{
            sql = " select warecode from h2.v_xls_jg where compid =:compId and busno =:busNo ";
        }
        Map<String, Object> paramMap = Maps.newHashMap();
        paramMap.put("compId", comIdCur);
        paramMap.put("busNo", busNo);

        log.info("需要执行查询的sql:{}",sql);

        log.info("查询的sql参数:compId={}|busNo={}", comIdCur, busNo);

        try{
            NamedParameterJdbcTemplate jdbcTemplate = new NamedParameterJdbcTemplate(getJdbcTemplate(comIdCur));
            list = jdbcTemplate.query(sql, paramMap, new BeanPropertyRowMapper<>(ItemPrice.class));
        }catch (Exception e){
            log.warn("查询第三方价格异常:",e);
        }

        if (CollectionUtils.isEmpty(list)){
            log.info("查询第三方商品价格为空 :{} :{} ",comId,busNo);
            return new ArrayList<>();
        }

        return list;
    }

    /**
     * 查询价格数量 根据连锁 和 门店
     * @param businessId
     * @param storeId
     * @param comId
     * @param busNo
     * @return
     */
    private List<ItemPrice> queryPriceDataListCommon(Long businessId, Long storeId, String comId, String busNo,List<String> goodsNos,String queryDate){
//        log.info("查询价格数量入参|businessId={}|storeId={}|comId={}|busNo={}|goodsNos={}|queryDate={}", businessId, storeId, comId, busNo, JSONObject.toJSONString(goodsNos), queryDate);
        String busNoCur = busNo;
        String comIdCur = comId;

        List<ItemPrice> list = Lists.newArrayList();
        String sql = "";
        if (StringUtils.isBlank(busNoCur) || StringUtils.isBlank(comIdCur)){
            MdmDataTransformDTO mdmStoreInfo = getMdmStoreInfo(businessId, storeId);
            busNoCur = mdmStoreInfo.getStoreNos().get(0);
            comIdCur = mdmStoreInfo.getComId();
        }
        //是否是根据商品 查询
        boolean isByGoodsNoSelect = CollectionUtils.isNotEmpty(goodsNos);
        //是否是 邦建（英克）
        boolean isBangJianComId = thirdService.isBangJianComId(comIdCur,"bj");
        boolean isXszkComId = thirdService.isBangJianComId(comIdCur,"xszk");
        if (isBangJianComId){
            sql = " select busno,compid,price,warecode,credate,type from zx_togj_ngpcs_all_price_v where compid =:compId and busno =:busNo ";
        }else if (isXszkComId){
            comIdCur = this.getXszkComIdMapping(comIdCur);
            sql = " select busno,compid,saleprice,memprice,minprice,memminprice,warecode,isSpecial from h2.v_xls_jg where compid =:compId and busno =:busNo ";
        }else{
            sql = " select busno,compid,saleprice,memprice,minprice,memminprice,warecode,isSpecial from h2.v_xls_jg where compid =:compId and busno =:busNo ";
        }
        Map<String, Object> paramMap = Maps.newHashMap();
        paramMap.put("compId", comIdCur);
        paramMap.put("busNo", busNoCur);

        if (isByGoodsNoSelect){
            paramMap.put("wareCodes", goodsNos);
            sql = sql + " and warecode in (:wareCodes)";

        }


        if (StringUtils.isNotBlank(queryDate) && isBangJianComId){
            paramMap.put("startDate", "'"+queryDate+"'");
            paramMap.put("endDate", "'"+DateUtil.dateToStr(new Date(),"yyyy-MM-dd HH:mm:ss")+"'");
            //sql = sql + " and credate > to_date(:startDate , 'yyyy-mm-dd hh24:mi:ss') and credate < to_date(:endDate , 'yyyy-mm-dd hh24:mi:ss') ";

            sql = sql + " and credate > to_date(:startDate , 'yyyy-MM-dd HH:mm:ss') and credate < to_date(:endDate , 'yyyy-MM-dd HH:mm:ss') ";
        }

        log.info("需要执行查询的sql:{}",sql);

        log.info("查询的sql参数:compId={}|busNo={}|wareCodes={}", comIdCur, busNoCur, JSONObject.toJSONString(goodsNos));

        try{
            NamedParameterJdbcTemplate jdbcTemplate = new NamedParameterJdbcTemplate(getJdbcTemplate(comIdCur));
            list = jdbcTemplate.query(sql, paramMap, new BeanPropertyRowMapper<>(ItemPrice.class));
        }catch (Exception e){
            log.warn("查询第三方价格异常:",e);
        }



        if (CollectionUtils.isEmpty(list)){
            log.info("查询第三方商品价格为空 :{} :{} ",comId,busNo);
            return new ArrayList<>();
        }

        return recombinationItem(isBangJianComId,list);
    }

    /**
     * 功 能: 获取先声再康连锁映射comId
     * 作 者: xujunzhang
     * 时 间: 2022/4/26
     */
    private String getXszkComIdMapping(String comIdCur) {
        if (StringUtils.isBlank(xszkComIdMapping)) {
            return comIdCur;
        }
        JSONObject xszkComIdMap = JSONObject.parseObject(xszkComIdMapping);
        log.info("getXszkComIdMapping|xszkComIdMap={}", JSONObject.toJSONString(xszkComIdMap));
        if (xszkComIdMap.containsKey(comIdCur)) {
            return xszkComIdMap.getString(comIdCur);
        }
        return comIdCur;
    }

    private List<ItemPrice> queryPriceDataListCommon2(Long businessId, Long storeId, String comId, String busNo,List<String> goodsNos,String queryDate){
        String busNoCur = busNo;
        String comIdCur = comId;

        List<ItemPrice> list = Lists.newArrayList();
        String sql = "";
        if (StringUtils.isBlank(busNoCur) || StringUtils.isBlank(comIdCur)){
            MdmDataTransformDTO mdmStoreInfo = getMdmStoreInfo(businessId, storeId);
            busNoCur = mdmStoreInfo.getStoreNos().get(0);
            comIdCur = mdmStoreInfo.getComId();
        }
        //是否是根据商品 查询
        boolean isByGoodsNoSelect = CollectionUtils.isNotEmpty(goodsNos);
        //是否是 邦建（英克）
        boolean isBangJianComId = thirdService.isBangJianComId(comIdCur,"bj");
        boolean isXszkComId = thirdService.isBangJianComId(comIdCur,"xszk");
        if (isBangJianComId){
            sql = " select busno,compid,price,warecode,to_date(credate , 'yyyy-mm-dd') credate,type from zx_togj_ngpcs_all_price_v where compid =:compId and busno =:busNo ";
        }else if (isXszkComId){
            sql = " select busno,compid,saleprice,memprice,minprice,memminprice,warecode,isSpecial from h2.v_xls_jg where compid =:compId and busno =:busNo ";
        }else{
            sql = " select busno,compid,saleprice,memprice,minprice,memminprice,warecode,isSpecial from h2.v_xls_jg where compid =:compId and busno =:busNo ";
        }
        Map<String, Object> paramMap = Maps.newHashMap();
        paramMap.put("compId", comIdCur);
        paramMap.put("busNo", busNoCur);

        if (isByGoodsNoSelect){
            paramMap.put("wareCodes", goodsNos);
            sql = sql + " and warecode in (:wareCodes)";

        }

        if (StringUtils.isNotBlank(queryDate) && isBangJianComId){
            paramMap.put("startDate", "'"+queryDate+"'");
            paramMap.put("endDate", "'"+DateUtil.dateToStr(new Date(),"yyyy-MM-dd")+" 00:00:00'");
            sql = sql + " and credate > to_char(:startDate , 'yyyy-mm-dd hh24:mi:ss') and credate < to_char(:endDate , 'yyyy-mm-dd hh24:mi:ss') ";
        }

        log.info("需要执行查询的sql:{}",sql);
        log.info("需要执行查询的sql的参数:{}",JSONObject.toJSONString(paramMap));


        try{
            NamedParameterJdbcTemplate jdbcTemplate = new NamedParameterJdbcTemplate(getJdbcTemplate(comIdCur));
            list = jdbcTemplate.query(sql, paramMap, new BeanPropertyRowMapper<>(ItemPrice.class));
        }catch (Exception e){
            log.warn("查询第三方价格异常:",e);
        }



        if (CollectionUtils.isEmpty(list)){
            log.info("查询第三方商品价格为空 :{} :{} ",comId,busNo);
            return new ArrayList<>();
        }

        return recombinationItem(isBangJianComId,list);
    }

    /**
     * 查询价格数量 根据连锁 和 门店
     * @param businessId
     * @param storeId
     * @param comId
     * @param busNo
     * @return
     */
    private List<ItemPrice> queryPriceDataListCommon(Long businessId, Long storeId, String comId, String busNo,List<String> goodsNos){

        return this.queryPriceDataListCommon(businessId, storeId, comId, busNo, goodsNos,null);
    }

    /**
     * 进行商品价格 统一
     * @param isBangJianComId
     * @param itemList
     */
    private List<ItemPrice> recombinationItem(boolean isBangJianComId, List<ItemPrice> itemList){

//        log.info("根据价格类型进行拆分List, 请求参数:{}",JSONObject.toJSONString(itemList));

        if (isBangJianComId){
            return itemList;
        }
        List<ItemPrice> itemPriceListOld = JSONObject.parseArray(JSONObject.toJSONString(itemList),ItemPrice.class);
        List<ItemPrice> itemPriceListNew = Lists.newArrayList();
        itemPriceListOld.forEach(item->{

            if (StringUtils.isNotBlank(item.getSaleprice())){
                ItemPrice price1 = new ItemPrice();
                BeanUtils.copyProperties(item,price1);
                price1.setPrice(item.getSaleprice());
                price1.setType(String.valueOf(PriceTypeEnum.LSJ.getCode()));
                itemPriceListNew.add(price1);
            }

            if (StringUtils.isNotBlank(item.getMemprice())){
                ItemPrice price2 = new ItemPrice();
                BeanUtils.copyProperties(item,price2);
                price2.setPrice(item.getMemprice());
                price2.setType(String.valueOf(PriceTypeEnum.HYJ.getCode()));
                itemPriceListNew.add(price2);
            }
            if (StringUtils.isNotBlank(item.getMinprice())){
                ItemPrice price2 = new ItemPrice();
                BeanUtils.copyProperties(item,price2);
                price2.setPrice(item.getMinprice());
                price2.setType(String.valueOf(PriceTypeEnum.CLJ.getCode()));
                itemPriceListNew.add(price2);
            }
            if (StringUtils.isNotBlank(item.getMemminprice())){
                ItemPrice price2 = new ItemPrice();
                BeanUtils.copyProperties(item,price2);
                price2.setPrice(item.getMemminprice());
                price2.setType(String.valueOf(PriceTypeEnum.CHYJ.getCode()));
                itemPriceListNew.add(price2);
            }
        });

//        log.info("查询价格返回值:{}",JSONObject.toJSONString(itemPriceListNew));

        return itemPriceListNew;
    }


    /**
     * 查询价格数量 根据连锁 和 门店
     * @param businessId
     * @param storeId
     * @param comId
     * @param busNo
     * @return
     */
    private List<ItemPrice> queryPriceDataCountCommon(Long businessId, Long storeId, String comId, String busNo){
        String busNoCur = busNo;
        String comIdCur = comId;
        if (StringUtils.isBlank(busNoCur) || StringUtils.isBlank(comIdCur)){
            MdmDataTransformDTO mdmStoreInfo = getMdmStoreInfo(businessId, storeId);
            busNoCur = mdmStoreInfo.getStoreNos().get(0);
            comIdCur = mdmStoreInfo.getComId();
        }
        JdbcTemplate jdbcTemplate = getJdbcTemplate(comIdCur);

        String sql = "";
        if (thirdService.isBangJianComId(comIdCur,"bj")){
            sql = " select count(1) as total from zx_togj_ngpcs_all_price_v where compid = ? and busno = ? ";
        }else{
            sql = " select count(1) as total from h2.v_xls_jg where compid = ? and busno = ? ";
        }
        List<ItemPrice> list = jdbcTemplate.query(sql, new Object[]{comIdCur, busNoCur}, new BeanPropertyRowMapper<>(ItemPrice.class));
        return list;
    }


    /**
     * 查询价格数量 根据连锁 和 门店
     * @param businessId
     * @param storeId
     * @param comId
     * @param busNo
     * @return
     */
    private List<ItemPrice> queryPriceDataCountCommonByDate(Long businessId, Long storeId, String comId, String busNo,String queryDate){
        String busNoCur = busNo;
        String comIdCur = comId;
        if (StringUtils.isBlank(busNoCur) || StringUtils.isBlank(comIdCur)){
            MdmDataTransformDTO mdmStoreInfo = getMdmStoreInfo(businessId, storeId);
            busNoCur = mdmStoreInfo.getStoreNos().get(0);
            comIdCur = mdmStoreInfo.getComId();
        }
        JdbcTemplate jdbcTemplate = getJdbcTemplate(comIdCur);

        String sql = "";
        if (thirdService.isBangJianComId(comIdCur,"bj")){
            sql = " select count(1) as total from zx_togj_ngpcs_all_price_v where compid = ? and busno = ? and credate = ?";
        }else{
            sql = " select count(1) as total from h2.v_xls_jg where compid = ? and busno = ? and syncDate = ?";
        }
        List<ItemPrice> list = jdbcTemplate.query(sql, new Object[]{comIdCur, busNoCur,queryDate}, new BeanPropertyRowMapper<>(ItemPrice.class));
        return list;
    }

    /**
     * 获取积分账户信息列表
     * 此视图来自于 邦健(华南平台) 林晓根
     * 计算结果为凌晨00:30计算前一天的数据,预计2分钟左右计算完成。计算好后，积分校对再进行同步
     */
    @Override
    public List<PosFundDailyDTO> queryFundAccount(String compId, String fundDate, Integer pageNo, Integer pageSize) {
        Map<String, Object> paramMap = getParamMap(compId,fundDate,pageNo,pageSize);

        String sql = "select V.comp_id as compid," +
            " V.fund_date as lasttime," +
            " V.userid as userid," +
            " V.phone as phone," +
            " V.erpcode as memcardno," +
            " V.add_total_pos as fundaddtotal," +
            " V.minus_total_pos as fundminustotal from " +
            " (SELECT ROWNUM RN,id,comp_id,fund_date, userid,phone,erpcode,add_total_pos, minus_total_pos from t_fund_account WHERE comp_id =:compId and fund_date = to_date(:fundDate, 'yyyy-MM-dd HH24:mi:ss') order by id) V WHERE V.RN >= :startNum AND V.RN <:endNum";
        log.info("queryFundAccount-sql:{}, param:{}", sql, paramMap);
        long tt = System.currentTimeMillis();
        NamedParameterJdbcTemplate jdbcTemplate = new NamedParameterJdbcTemplate(getHdJdbcTemplate(StockCompareServiceImpl.BANGJIANDBSTR));
        List<PosFundDailyDTO> list = jdbcTemplate.query(sql, paramMap, new BeanPropertyRowMapper<>(PosFundDailyDTO.class));
        log.info("queryFundAccount-sql:{}, param:{}， costTime:{}", sql, paramMap, System.currentTimeMillis() - tt);
        return list;
    }

    /**
     * 获取积分明细信息列表
     */
    @Override
    public List<PosFundDetailDTO> queryFundDetails(String compId, String fundDate, String erpCode) {
        Map<String, Object> paramMap = getParamMapForFundDetail(compId,fundDate,erpCode);

        String sql = "select V.id as id," +
            " V.comp_id as compId," +
            " V.fund_date as fundDate," +
            " V.userid as userid," +
            " V.phone as phone," +
            " V.erp_code as memcardno," +
            " V.bdid as bdid," +
            " V.fund_val_pos as fundval," +
            " V.change_reason as notes," +
            " V.fund_detail_time_pos as funddatetime," +
            " V.from_user_id as fromUserId," +
            " V.from_erp_code as fromErpCode," +
            " V.merge_date as mergeDate from " +
            " (SELECT ROWNUM RN,id,comp_id,fund_date,userid,phone,erp_code,bdid,fund_val_pos,change_reason,fund_detail_time_pos,from_user_id,from_erp_code,merge_date from v_fund_details WHERE comp_id =:compId and erp_code=:erpCode and fund_date = to_date(:fundDate, 'yyyy-MM-dd HH24:mi:ss') order by id) V";
        log.info("queryFundAccount-sql:{}, param:{}", sql, paramMap);
        long tt = System.currentTimeMillis();
        NamedParameterJdbcTemplate jdbcTemplate = new NamedParameterJdbcTemplate(getHdJdbcTemplate(StockCompareServiceImpl.BANGJIANDBSTR));
        List<PosFundDetailDTO> list = jdbcTemplate.query(sql, paramMap, new BeanPropertyRowMapper<>(PosFundDetailDTO.class));
        log.info("queryFundAccount-sql:{}, param:{}， costTime:{}", sql, paramMap, System.currentTimeMillis() - tt);
        return list;
    }

    /**
     * 参数组装
     * @param compId
     * @param fundDate
     * @param pageNo
     * @param pageSize
     * @return
     */
    private Map getParamMap(String compId, String fundDate, Integer pageNo, Integer pageSize){
        if (pageNo < 1) {
            pageNo = 1;
        }
        Map<String, Object> paramMap = Maps.newHashMap();
        paramMap.put("compId", compId);
        paramMap.put("fundDate", fundDate);
        paramMap.put("startNum", pageNo * pageSize - pageSize + 1);
        paramMap.put("endNum", pageNo * pageSize + 1);

        return paramMap;
    }

    /**
     * 参数组装
     * @param compId
     * @param fundDate
     * @param erpCode
     * @return
     */
    private Map getParamMapForFundDetail(String compId, String fundDate, String erpCode){
        Map<String, Object> paramMap = Maps.newHashMap();
        paramMap.put("compId", compId);
        paramMap.put("fundDate", fundDate);
        paramMap.put("erpCode", erpCode);
        return paramMap;
    }

    /**
     * 测试数据库
     * @param compId
     * @param fundDate
     * @param erpCode
     * @return
     */
    @Override
    public String getOracleTime(String compId,String fundDate,String erpCode){
        Map<String, Object> paramMap = new HashMap<>();
        String sql = "select sysdate from dual";
        long tt = System.currentTimeMillis();
        NamedParameterJdbcTemplate jdbcTemplate = new NamedParameterJdbcTemplate(getHdJdbcTemplate(StockCompareServiceImpl.BANGJIANDBSTR));
        List<String> list = jdbcTemplate.query(sql, paramMap, new BeanPropertyRowMapper<>(String.class));
        log.info("queryFundAccount-sql:{}, list.get(0):{}， costTime:{}", sql, list.get(0), System.currentTimeMillis() - tt);
        return list.get(0);
    }

    @Override
    public List<WareDxDateVO> getDXHdItemInfo(Long businessId, Long storeId, String lastDxdate, String startDxDate, List<String> goodsNos) {
        log.info("|HdDataServiceImpl|getDXHdItemInfo|查询动销海典库存数据|businessId={},storeId={},lastDxdate={}", businessId, storeId,lastDxdate);
        if(businessId==null || storeId==null || lastDxdate==null){
            return new ArrayList<>();
        }

        MdmDataTransformDTO mdmStoreInfo = getMdmStoreInfo(businessId, storeId);
        log.info("|HdDataServiceImpl|getDXHdItemInfo|查询动销海典库存数据mdmStoreInfo={}", mdmStoreInfo);

        if(CollectionUtils.isEmpty(mdmStoreInfo.getStoreNos()) || StringUtils.isEmpty(mdmStoreInfo.getComId())){
            return new ArrayList<>();
        }

        Map<String, Object> paramMap = Maps.newHashMap();
        String busNo = mdmStoreInfo.getStoreNos().get(0);
        paramMap.put("compId", mdmStoreInfo.getComId());
        paramMap.put("busNo", busNo);
        paramMap.put("lastDxdate", lastDxdate);
        String sql = "SELECT a.scrm_compid scrmCompid,a.mdm_busno mdmBusno,a.warecode warecode,a.last_dxdate lastDxdate,a.sumqty sumqty,a.sumawaitqty sumawaitqty  \n" +
                " from h2.V_WARE_DXDATE a  \n" +
                " where a.scrm_compid =:compId and a.mdm_busno =:busNo and a.last_dxdate >= to_date(:lastDxdate, 'yyyy-MM-dd')";
        String dbSource = getDBSource(mdmStoreInfo.getComId());
        dbSource = dbSource == null ? "" : dbSource;
        NamedParameterJdbcTemplate jdbcTemplate = new NamedParameterJdbcTemplate(getHdJdbcTemplate(dbSource));
        log.info("|HdDataServiceImpl|getDXHdItemInfo|查询动销海典库存数据jdbcTemplate={}", jdbcTemplate);
        List<WareDxDateVO> list = jdbcTemplate.query(sql, paramMap, new BeanPropertyRowMapper<>(WareDxDateVO.class));
        log.info("|HdDataServiceImpl|getDXHdItemInfo|查询动销海典库存数据结束={}", list);
        return list;
    }

    @Override
    public Integer getHdDxGoodsListCount(HdDxParamVO param) {
        log.info("|HdDataServiceImpl|getDXHdItemInfo|查询动销海典库存数据|businessId={},storeId={},lastDxdate={}", param.getBusinessId(),param.getStoreId(), param.getLastDxDate());
        if (param.getBusinessId() == null || param.getStoreId() == null || param.getLastDxDate() == null) {
            return 0;
        }
        Long businessId = param.getBusinessId();
        Long storeId = param.getStoreId();
        String startDxDate = param.getStartDxDate();
        String lastDxDate = param.getLastDxDate();
        MdmDataTransformDTO mdmStoreInfo = getMdmStoreInfo(businessId, storeId);
        log.info("|HdDataServiceImpl|getDXHdItemInfo|查询动销海典库存数据mdmStoreInfo={}", mdmStoreInfo);

        if(CollectionUtils.isEmpty(mdmStoreInfo.getStoreNos()) || StringUtils.isEmpty(mdmStoreInfo.getComId())){
            return 0;
        }

        Map<String, Object> paramMap = Maps.newHashMap();
        String busNo = mdmStoreInfo.getStoreNos().get(0);
        StringBuilder sql = new StringBuilder();
        paramMap.put("compId", mdmStoreInfo.getComId());
        paramMap.put("busNo", busNo);
        paramMap.put("lastDxdate", lastDxDate);
        paramMap.put("startDxdate", startDxDate);
        paramMap.put("lastExpireDate", StringUtils.isBlank(param.getLastExpireDate()) ? null : param.getLastExpireDate());
        paramMap.put("startExpireDate", StringUtils.isBlank(param.getStartExpireDate()) ? null : param.getStartExpireDate());


        if (StringUtils.isNotBlank(param.getLastExpireDate()) && StringUtils.isNotBlank(param.getStartExpireDate())) {
            sql.append("SELECT count(1)");
            sql.append(" FROM h2.t_sale_d a,");
            sql.append(" h2.s_busi s,");
            sql.append(" h2.t_ware t");
            sql.append(" WHERE a.busno = s.busno");
            sql.append(" AND s.COMPID = t.COMPID");
            sql.append(" AND s.mdm_busno = :busNo");
            sql.append(" AND a.WAREID = t.WAREID");
            sql.append(" AND a.ACCDATE BETWEEN TO_DATE(:startDxdate, 'yyyy-mm-dd hh24:mi:ss') AND TO_DATE(:lastDxdate, 'yyyy-mm-dd hh24:mi:ss')");
            sql.append(" AND a.INVALIDATE BETWEEN TO_DATE(:startExpireDate, 'yyyy-mm-dd hh24:mi:ss') AND TO_DATE(:lastExpireDate, 'yyyy-mm-dd hh24:mi:ss')");
        } else {
            sql.append("SELECT count(1)");
            sql.append(" FROM h2.V_WARE_DXDATE_onlysale a ");
            sql.append(" WHERE a.scrm_compid = :compId ");
            sql.append(" AND a.mdm_busno = :busNo");
            sql.append(" AND a.last_dxdate BETWEEN TO_DATE(:startDxdate, 'yyyy-mm-dd hh24:mi:ss') AND to_date(:lastDxdate, 'yyyy-mm-dd hh24:mi:ss')");
        }

        log.info("查询海典动销总数sql：{}", sql);

        String dbSource = getDBSource(mdmStoreInfo.getComId());
        dbSource = dbSource == null ? "" : dbSource;
        NamedParameterJdbcTemplate jdbcTemplate = new NamedParameterJdbcTemplate(getHdJdbcTemplate(dbSource));
        log.info("|HdDataServiceImpl|newGetDXHdItemInfoCount|查询动销海典库存数据总数jdbcTemplate={}", jdbcTemplate);
        Integer count  = jdbcTemplate.queryForObject(sql.toString(), paramMap, Integer.class);
        log.info("|HdDataServiceImpl|getDXHdItemInfo|查询动销海典库存数据总数结束={}",count);
        return count;
    }

    @Override
    public List<WareDxDateVO> getHdDxGoodsList(HdDxParamVO param) {
        log.info("|HdDataServiceImpl|getDXHdItemInfo|查询动销海典库存数据|businessId={},storeId={},lastDxdate={}", param.getBusinessId(), param.getStoreId(),param.getLastDxDate());
        if(param.getBusinessId()==null || param.getStoreId()==null || param.getLastDxDate()==null){
            return new ArrayList<>();
        }

        MdmDataTransformDTO mdmStoreInfo = getMdmStoreInfo(param.getBusinessId(), param.getStoreId());
        log.info("|HdDataServiceImpl|getDXHdItemInfo|查询动销海典库存数据mdmStoreInfo={}", mdmStoreInfo);

        if(CollectionUtils.isEmpty(mdmStoreInfo.getStoreNos()) || StringUtils.isEmpty(mdmStoreInfo.getComId())){
            return new ArrayList<>();
        }

        Map<String, Object> paramMap = Maps.newHashMap();
        String busNo = mdmStoreInfo.getStoreNos().get(0);
        StringBuilder sql = new StringBuilder();
        paramMap.put("compId", mdmStoreInfo.getComId());
        paramMap.put("busNo", busNo);

        paramMap.put("lastDxdate", param.getLastDxDate());
        paramMap.put("startDxdate", param.getStartDxDate());
        paramMap.put("pageStartNum", param.getPageStartNum());
        paramMap.put("pageEndNum", param.getPageEndNum());
        paramMap.put("lastExpireDate", StringUtils.isBlank(param.getLastExpireDate()) ? null : param.getLastExpireDate());
        paramMap.put("startExpireDate", StringUtils.isBlank(param.getStartExpireDate()) ? null : param.getStartExpireDate());
        if (StringUtils.isNotBlank(param.getLastExpireDate()) && StringUtils.isNotBlank(param.getStartExpireDate())) {
            sql.append("SELECT *");
            sql.append(" FROM (SELECT ROWNUM AS rowno,");
            sql.append(" t.warecode warecode");
            sql.append(" FROM h2.t_sale_d a,");
            sql.append(" h2.s_busi s,");
            sql.append(" h2.t_ware t");
            sql.append(" WHERE a.busno = s.busno");
            sql.append(" AND s.COMPID = t.COMPID");
            sql.append(" AND s.mdm_busno = :busNo");
            sql.append(" AND a.WAREID = t.WAREID");
            sql.append(" AND a.ACCDATE BETWEEN TO_DATE(:startDxdate , 'yyyy-mm-dd hh24:mi:ss') AND TO_DATE(:lastDxdate , 'yyyy-mm-dd hh24:mi:ss')");
            sql.append(" AND a.INVALIDATE BETWEEN TO_DATE(:startExpireDate , 'yyyy-mm-dd hh24:mi:ss') AND TO_DATE(:lastExpireDate , 'yyyy-mm-dd hh24:mi:ss')");
            sql.append(" AND ROWNUM <= :pageEndNum) table_data");
            sql.append(" WHERE table_data.rowno >= :pageStartNum");
        } else {
            sql.append("SELECT *");
            sql.append(" FROM (SELECT ROWNUM AS rowno,");
            sql.append(" a.warecode warecode");
            sql.append(" FROM h2.V_WARE_DXDATE_onlysale a");
            sql.append(" WHERE a.scrm_compid = :compId");
            sql.append(" AND a.mdm_busno = :busNo");
            sql.append(" AND a.last_dxdate BETWEEN TO_DATE(:startDxdate, 'yyyy-mm-dd hh24:mi:ss') AND TO_DATE(:lastDxdate, 'yyyy-mm-dd hh24:mi:ss')");
            sql.append(" AND ROWNUM <= :pageEndNum)  table_data");
            sql.append(" WHERE table_data.rowno >= :pageStartNum");
        }
        log.info("分页查询海典动销sql:{}", sql);
        String dbSource = getDBSource(mdmStoreInfo.getComId());
        dbSource = dbSource == null ? "" : dbSource;
        NamedParameterJdbcTemplate jdbcTemplate = new NamedParameterJdbcTemplate(getHdJdbcTemplate(dbSource));
        log.info("|HdDataServiceImpl|getDXHdItemInfo|查询动销海典库存数据jdbcTemplate={}", jdbcTemplate);
        List<WareDxDateVO> list = jdbcTemplate.query(sql.toString(), paramMap, new BeanPropertyRowMapper<>(WareDxDateVO.class));
        log.info("|HdDataServiceImpl|getDXHdItemInfo|查询动销海典库存数据结束={}", list);
        return list;
    }

    @Override
    public List<HdOrderDTO> queryFundViewByUserIdAndBusinessId(Long scrmUserid, Long businessId, Integer scrmCount) {
        MdmDataTransformDTO mdmDataTransformDTO = getMdmStoreInfo(businessId, null);
        Map<String, Object> paramMap = Maps.newHashMap();
        paramMap.put("scrmUserid", scrmUserid);
        paramMap.put("scrmCompid", mdmDataTransformDTO.getComId());
        paramMap.put("scrmCount", scrmCount);
        String sql = "select scrm_compid as scrmCompid," +
            "mdm_busno as mdmBusno," +
            "cashno," +
            "scrm_userid as scrmUserid," +
            "createtime as createTime," +
            "integral_pst as integralPst," +
            "status from " +
            " h2.V_ACC_POINTS_DESTROY " +
            " where scrm_userid =:scrmUserid and scrm_compid = :scrmCompid and ROWNUM <= :scrmCount";
        log.info("queryFundViewByUserIdAndBusinessId-sql:{}, param:{}", sql, paramMap);
        NamedParameterJdbcTemplate jdbcTemplate = new NamedParameterJdbcTemplate(getJdbcTemplate(mdmDataTransformDTO.getComId()));
        List<HdOrderDTO> list = jdbcTemplate.query(sql, paramMap, new BeanPropertyRowMapper<>(HdOrderDTO.class));
        return list;
    }
}
