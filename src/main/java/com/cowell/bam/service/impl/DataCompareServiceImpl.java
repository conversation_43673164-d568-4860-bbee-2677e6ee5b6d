package com.cowell.bam.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.cowell.bam.domain.DiffDataCompareInfo;
import com.cowell.bam.domain.DiffDataCompareInfoExample;
import com.cowell.bam.domain.DiffDataCompareInfoWithBLOBs;
import com.cowell.bam.enums.SyncTypeEnum;
import com.cowell.bam.handler.AbstractChannelHandler;
import com.cowell.bam.repository.mybatis.dao.DiffDataCompareInfoMapper;
import com.cowell.bam.service.DataCompareService;
import com.cowell.bam.service.dto.*;
import com.cowell.bam.web.rest.BaseController;
import com.cowell.bam.web.rest.util.ConstantPool;
import com.cowell.bam.web.rest.util.DateUtil;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.Lists;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.task.AsyncTaskExecutor;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @ProjectName bam
 * @Description:
 * @date 2019/10/08 15:18
 */
@Service
public class DataCompareServiceImpl implements DataCompareService {
    private Logger logger = LoggerFactory.getLogger(DataCompareServiceImpl.class);

    @Autowired
    private DiffDataCompareInfoMapper diffDataCompareInfoMapper;

    @Autowired
    private BaseController baseController;
    /**
     * 所有处理器
     */
    @Autowired
    private List<AbstractChannelHandler> handlerList;

    @Autowired
    @Qualifier("taskExecutorTrace")
    private AsyncTaskExecutor asyncTaskExecutor;

    @Value("comparePriceConfig")
    private String comparePriceConfig;

    @Autowired
    private ThirdService thirdService;

    @Override
    public PageInfo<DiffDataResponseDTO> diffCompareList(Pageable pageable, DiffDataRequestDTO diffDataRequestDTO) {
        List<DiffDataResponseDTO> resultList = Lists.newArrayList();
        com.github.pagehelper.Page<Object> page = PageHelper.startPage(pageable.getPageNumber(), pageable.getPageSize());
        DiffDataCompareInfoExample example = new DiffDataCompareInfoExample();
        DiffDataCompareInfoExample.Criteria criteria = example.createCriteria();
        if (diffDataRequestDTO.getBusinessId() != null) {
            criteria.andBusinessIdEqualTo(diffDataRequestDTO.getBusinessId());
        }
        if (diffDataRequestDTO.getStoreId() != null) {
            criteria.andStoreIdEqualTo(diffDataRequestDTO.getStoreId());
        }
        if (diffDataRequestDTO.getDataType() != null) {
            criteria.andDataTypeEqualTo(diffDataRequestDTO.getDataType());
        }

        if (StringUtils.isNotEmpty(diffDataRequestDTO.getTime())) {
            criteria.andGmtCreateBetween(
                DateUtil.parse(diffDataRequestDTO.getTime() + " 00:00:00",DateUtil.TIME_FORMAT),
                DateUtil.parse(diffDataRequestDTO.getTime() + " 23:59:59",DateUtil.TIME_FORMAT)
            );
        }
        List<DiffDataCompareInfoWithBLOBs> diffList = diffDataCompareInfoMapper.selectByExampleWithBLOBs(example);
        if (CollectionUtils.isEmpty(diffList)) {
            new PageImpl<>(resultList, pageable, 0);
        }
        diffList.forEach(diffDataCompareInfo -> {
            DiffDataResponseDTO dto = new DiffDataResponseDTO();
            org.springframework.beans.BeanUtils.copyProperties(diffDataCompareInfo, dto);
            String typeName = SyncTypeEnum.getName(diffDataCompareInfo.getDataType());
            dto.setTypeName(typeName);
            if (SyncTypeEnum.STOCK.getCode().equals(diffDataCompareInfo.getDataType())) {
                StockDataInfoDTO stockDataInfoDTO = JSONObject.parseObject(diffDataCompareInfo.getOurData(), StockDataInfoDTO.class);
                dto.setBatchNo(stockDataInfoDTO ==null ? "ssbug":stockDataInfoDTO.getBatchNo());
                dto.setBatchCode(stockDataInfoDTO ==null ? "ssbug":stockDataInfoDTO.getBatchCode());
            }
            resultList.add(dto);
        });
        PageInfo pageInfo = new PageInfo<>(resultList);
        pageInfo.setTotal(page.getTotal());
        return pageInfo;
    }



    @Override
    public PageInfo<DiffCollectDataResponseDTO> diffCollect(Pageable pageable, DiffDataRequestDTO diffDataRequestDTO) {
        com.github.pagehelper.Page<Object> page = PageHelper.startPage(pageable.getPageNumber(), pageable.getPageSize());
        diffDataRequestDTO.setGmtCreateStart(DateUtil.parse(diffDataRequestDTO.getTime() + " 00:00:00",DateUtil.TIME_FORMAT));
        diffDataRequestDTO.setGmtCreateEnd(DateUtil.parse(diffDataRequestDTO.getTime() + " 23:59:59",DateUtil.TIME_FORMAT));
        List<DiffCollectDataResponseDTO> responseDTOS = diffDataCompareInfoMapper.selectDiffCollect(diffDataRequestDTO);
        if (CollectionUtils.isEmpty(responseDTOS)) {
            new PageImpl<>(responseDTOS, pageable, 0);
        }
        processBusinessInfo(responseDTOS);
        PageInfo pageInfo = new PageInfo<>(responseDTOS);
        pageInfo.setTotal(page.getTotal());
        return pageInfo;
    }

    @Override
    public List<DiffCollectDataResponseDTO> getDiffCollectList(DiffDataRequestDTO diffDataRequestDTO) {
        diffDataRequestDTO.setGmtCreateStart(DateUtil.parse(diffDataRequestDTO.getTime() + " 00:00:00",DateUtil.TIME_FORMAT));
        diffDataRequestDTO.setGmtCreateEnd(DateUtil.parse(diffDataRequestDTO.getTime() + " 23:59:59",DateUtil.TIME_FORMAT));
        List<DiffCollectDataResponseDTO> responseDTOS = diffDataCompareInfoMapper.selectDiffCollect(diffDataRequestDTO);
        if (CollectionUtils.isEmpty(responseDTOS)) {
            return responseDTOS;
        }
        processBusinessInfo(responseDTOS);
        return responseDTOS;
    }

    @Override
    public List<Long> getTransitDiffBusiness(DiffDataRequestDTO diffDataRequestDTO){
        getDiffParam(diffDataRequestDTO);
        if(Objects.isNull(diffDataRequestDTO.getVersion()) || 0 == diffDataRequestDTO.getVersion()){
            logger.info("geTransittDiffCollectList|版本号缺失|maxVersion:{}。", diffDataRequestDTO.getVersion());
            return Lists.newArrayList();
        }
        if(Objects.nonNull(diffDataRequestDTO.getVersion()) && diffDataRequestDTO.getVersion().equals(ConstantPool.TRANSIT_ALL_VERSION)){
            diffDataRequestDTO.setVersion(null);
        }
        return diffDataCompareInfoMapper.selectTransitDiffBusiness(diffDataRequestDTO);
    }

    @Override
    public List<DiffTransitDataResponseDTO> geTransittDiffCollectList(DiffDataRequestDTO diffDataRequestDTO) {
        List<DiffCollectDataResponseDTO> responseDTOS = diffDataCompareInfoMapper.selectTransitDiffCollect(diffDataRequestDTO);
        if (CollectionUtils.isEmpty(responseDTOS)) {
            return Lists.newArrayList();
        }
        return responseDTOS.stream().filter(v->StringUtils.isNotBlank(v.getThirdData())).map(v->{
            DiffTransitDataResponseDTO responseDTO = new DiffTransitDataResponseDTO();
            responseDTO.setGoodsCode(v.getGoodsCode());
            responseDTO.setBusinessId(v.getBusinessId());
            responseDTO.setStoreId(v.getStoreId());
            if(StringUtils.isNotBlank(v.getThirdData())){
                ThridStockInfoDTO thridStockInfoDTO = JSON.parseObject(v.getThirdData(), ThridStockInfoDTO.class);
                responseDTO.setBusNo(thridStockInfoDTO.getBusNo());
                responseDTO.setCompId(thridStockInfoDTO.getCompId());
                responseDTO.setStoreName(thridStockInfoDTO.getStoreName());
                responseDTO.setReason(v.getReason());
                responseDTO.setHdTransitCount(StringUtils.isNotBlank(thridStockInfoDTO.getTransitStock())?new BigDecimal(thridStockInfoDTO.getTransitStock()).stripTrailingZeros().toPlainString():null);
                if(StringUtils.isNotBlank(v.getOurData())){
                    StockDataInfoDTO stockDataInfoDTO = JSON.parseObject(v.getOurData(), StockDataInfoDTO.class);
                    responseDTO.setZtTransitCount(Objects.nonNull(stockDataInfoDTO.getTransitStock())?stockDataInfoDTO.getTransitStock().stripTrailingZeros().toPlainString():"0");

                    if(Objects.nonNull(responseDTO.getHdTransitCount())){
                        responseDTO.setDifferTransitCount(new BigDecimal(responseDTO.getHdTransitCount()).subtract(new BigDecimal(responseDTO.getZtTransitCount())).stripTrailingZeros().toPlainString());
                    }else{
                        responseDTO.setDifferTransitCount(responseDTO.getZtTransitCount());
                    }
                }else{
                    if(Objects.nonNull(responseDTO.getHdTransitCount())){
                        responseDTO.setDifferTransitCount(responseDTO.getHdTransitCount());
                    }
                }
            }
            return responseDTO;
        }).collect(Collectors.toList());
    }


    @Override
    public List<DiffDataCompareInfoDTO> getDiffDetailList(DiffDataRequestDTO diffDataRequestDTO) {
        diffDataRequestDTO.setGmtCreateStart(DateUtil.parse(diffDataRequestDTO.getTime() + " 00:00:00", DateUtil.TIME_FORMAT));
        diffDataRequestDTO.setGmtCreateEnd(DateUtil.parse(diffDataRequestDTO.getTime() + " 23:59:59",DateUtil.TIME_FORMAT));
        List<DiffDataCompareInfo> diffDataCompareInfos = diffDataCompareInfoMapper.selectDiffDetail(diffDataRequestDTO);
        if (CollectionUtils.isEmpty(diffDataCompareInfos)) {
            return null;
        }
        List<Long> businessIdList = diffDataCompareInfos.stream().map(DiffDataCompareInfo::getBusinessId).collect(Collectors.toList());
        List<CrmStoreDTO> crmStoreDTOList = Lists.newArrayList();
        businessIdList.forEach(businessId->{
            crmStoreDTOList.addAll(thirdService.getCrmStoreInfoByBusinessId(businessId));
        });
        Map<Long, CrmStoreDTO> storeDTOMap = CollectionUtils.isEmpty(crmStoreDTOList)?
            new HashMap<>(): crmStoreDTOList.stream().collect(Collectors.toMap(CrmStoreDTO::getId, b -> b, (a, b) -> a));

        List<BusinessInfoDTO> businessInfoDTOList = thirdService.getAllBusinessInfoByIds(businessIdList);
        Map<Long, BusinessInfoDTO> businessInfoDTOMap = CollectionUtils.isEmpty(businessInfoDTOList)?
            new HashMap<>(): businessInfoDTOList.stream().collect(Collectors.toMap(BusinessInfoDTO::getId, b -> b, (a, b) -> a));
        List<DiffDataCompareInfoDTO> dtos = Lists.newArrayList();
        diffDataCompareInfos.forEach(diffDataCompareInfo -> {
            DiffDataCompareInfoDTO dto = new DiffDataCompareInfoDTO();
            BeanUtils.copyProperties(diffDataCompareInfo,dto);
            if (MapUtils.isNotEmpty(storeDTOMap)) {
                CrmStoreDTO crmStoreDTO = storeDTOMap.get(diffDataCompareInfo.getStoreId());
                if (Objects.nonNull(crmStoreDTO)) {
                    dto.setStoreName(crmStoreDTO.getStoreName());
                }
            }
            if (MapUtils.isNotEmpty(businessInfoDTOMap)) {
                BusinessInfoDTO businessInfoDTO = businessInfoDTOMap.get(diffDataCompareInfo.getBusinessId());
                if (Objects.nonNull(businessInfoDTO)) {
                    dto.setBusinessName(businessInfoDTO.getBusinessName());
                }
            }
            dto.setGmtCreateStr(DateUtil.dateToStr(dto.getGmtCreate(),DateUtil.DATE_FORMAT_BDP));
            dtos.add(dto);
        });
        return dtos;
    }

    public void getDiffParam(DiffDataRequestDTO diffDataRequestDTO){
        diffDataRequestDTO.setGmtCreateStart(DateUtil.parse(diffDataRequestDTO.getTime() + " 00:00:00",DateUtil.TIME_FORMAT));
        diffDataRequestDTO.setGmtCreateEnd(DateUtil.parse(diffDataRequestDTO.getTime() + " 23:59:59",DateUtil.TIME_FORMAT));
        diffDataRequestDTO.setDate(diffDataRequestDTO.getTime().replace("-",""));
        if(Objects.isNull(diffDataRequestDTO.getVersion())){
            Integer maxVersion = diffDataCompareInfoMapper.selectMaxVersionDay(diffDataRequestDTO);
            if(Objects.nonNull(maxVersion) && 0 != maxVersion){
                diffDataRequestDTO.setVersion(maxVersion);
            }
        }
    }

    private void processBusinessInfo(List<DiffCollectDataResponseDTO> responseDTOS) {
        List<Long> businessIdList = responseDTOS.stream().map(DiffCollectDataResponseDTO::getBusinessId).collect(Collectors.toList());
        List<BusinessInfoDTO> businessInfoDTOList = thirdService.getAllBusinessInfoByIds(businessIdList);
        Map<Long, BusinessInfoDTO> bidMap = CollectionUtils.isEmpty(businessInfoDTOList)?
            new HashMap<>(): businessInfoDTOList.stream().collect(Collectors.toMap(BusinessInfoDTO::getId, b -> b, (a, b) -> a));

        responseDTOS.forEach(diffCollectDataResponseDTO -> {
            BusinessInfoDTO businessInfoDTO = bidMap.get(diffCollectDataResponseDTO.getBusinessId());
            if (Objects.nonNull(businessInfoDTO)) {
                diffCollectDataResponseDTO.setBusinessName(businessInfoDTO.getBusinessName());
            }
        });
    }


    @Override
    public StockGoodsBaseDTO diffCompareDetail(Long id) {
        DiffDataCompareInfoWithBLOBs diffDataCompareInfo = diffDataCompareInfoMapper.selectByPrimaryKey(id);
        if (diffDataCompareInfo == null) {
            return null;
        }
        if (SyncTypeEnum.STOCK.getCode().equals(diffDataCompareInfo.getDataType())) {
            StockDataInfoDTO stockDataInfoDTO = JSONObject.parseObject(diffDataCompareInfo.getOurData(), StockDataInfoDTO.class);
            ThridStockInfoDTO thridStockInfoDTO = JSONObject.parseObject(diffDataCompareInfo.getThirdData(), ThridStockInfoDTO.class);
            StockDiffDataDetailResponseDTO detailResponseDTO = new StockDiffDataDetailResponseDTO();
            if (stockDataInfoDTO != null){
                org.springframework.beans.BeanUtils.copyProperties(stockDataInfoDTO, detailResponseDTO);
                detailResponseDTO.setGoodsNo(stockDataInfoDTO.getSkuMerchantCode());
                detailResponseDTO.setBatchNo(stockDataInfoDTO.getBatchNo());
                detailResponseDTO.setBatchCode(stockDataInfoDTO.getBatchCode());
            }
            if (thridStockInfoDTO != null){
                org.springframework.beans.BeanUtils.copyProperties(thridStockInfoDTO,detailResponseDTO);
                detailResponseDTO.setGoodsNo(thridStockInfoDTO.getWareCode());
                detailResponseDTO.setBatchNo(thridStockInfoDTO.getMakeNo());
                detailResponseDTO.setBatchCode(thridStockInfoDTO.getBatchNo());
            }
            detailResponseDTO.setGmtCreate(diffDataCompareInfo.getGmtCreate());
            detailResponseDTO.setGoodsNo(diffDataCompareInfo.getGoodsNo());
            detailResponseDTO.setBusinessId(diffDataCompareInfo.getBusinessId());
            detailResponseDTO.setStoreId(diffDataCompareInfo.getStoreId());
            return detailResponseDTO;
        }else if (SyncTypeEnum.PRICE.getCode().equals(diffDataCompareInfo.getDataType())){
            PriceDataInfoDTO priceDataInfoDTO = JSONObject.parseObject(diffDataCompareInfo.getOurData(), PriceDataInfoDTO.class);
            ThridPriceInfoDTO thridPriceInfoDTO = JSONObject.parseObject(diffDataCompareInfo.getThirdData(), ThridPriceInfoDTO.class);
            PriceDiffDataDetailResponseDTO  diffDataDetailResponseDTO = new PriceDiffDataDetailResponseDTO();
            if (priceDataInfoDTO != null) {
                diffDataDetailResponseDTO.setPrice(priceDataInfoDTO.getPrice());
            }
            if (thridPriceInfoDTO != null) {
                diffDataDetailResponseDTO.setHdPrice(thridPriceInfoDTO.getPrice());
            }
            diffDataDetailResponseDTO.setGoodsNo(diffDataCompareInfo.getGoodsNo());
            diffDataDetailResponseDTO.setGmtCreate(diffDataCompareInfo.getGmtCreate());
            diffDataDetailResponseDTO.setBusinessId(diffDataCompareInfo.getBusinessId());
            diffDataDetailResponseDTO.setStoreId(diffDataCompareInfo.getStoreId());
            return diffDataDetailResponseDTO;
        }
        return null;
    }

    @Override
    public boolean acceptBdpData(Integer type,String comId) {
        //异步进行库存比对
        //executor.execute(()->{
            SyncTypeEnum tyeEnum = SyncTypeEnum.getEnum(type);
            baseController.selectChannelHandler(tyeEnum.getCode(), handlerList).compare(comId);
        //});
        return true;
    }

    @Override
    public Integer deleteDiffData(Long businessId,Integer type,String time) {
        for ( int i=0;i<100;i++){
            DiffDataCompareInfoExample example = new DiffDataCompareInfoExample();
            example.createCriteria().andBusinessIdEqualTo(businessId).andDataTypeEqualTo(type).andGmtCreateBetween(
                    DateUtil.parse(time + " 00:00:00",DateUtil.TIME_FORMAT),
                    DateUtil.parse(time + " 23:59:59",DateUtil.TIME_FORMAT)
            );
            diffDataCompareInfoMapper.deleteByExample(example);
        }
        return 0;
    }

    @Override
    public Integer batchDeleteDiffData(String businessIds, Integer type, String time) {
        logger.info("批量删除比对数据:{} :{} :{}",businessIds,type,time);
        if (StringUtils.isBlank(businessIds)){
            return 0;
        }
        for (String businessIdStr : businessIds.split(",")){

            Long businessIdLng = Long.parseLong(businessIdStr);

            asyncTaskExecutor.execute(()->{
                DiffDataCompareInfoExample example = new DiffDataCompareInfoExample();

                DiffDataCompareInfoExample.Criteria criteria = example.createCriteria();

                if (!businessIdStr.equals("12345")){
                    criteria.andBusinessIdEqualTo(businessIdLng);
                }
                criteria.andDataTypeEqualTo(type == null ? 1 : type);

                if (StringUtils.isNotBlank(time)){
                    criteria.andGmtCreateBetween(
                        DateUtil.parse(time + " 00:00:00",DateUtil.TIME_FORMAT),
                        DateUtil.parse(time + " 23:59:59",DateUtil.TIME_FORMAT)
                    );
                }

                diffDataCompareInfoMapper.deleteByExample(example);

                long count = diffDataCompareInfoMapper.countByExample(example);

                if (count > 0L){

                    this.batchDeleteDiffData(businessIds,type,time);

                }


            });
        }
        return 1;
    }
}
