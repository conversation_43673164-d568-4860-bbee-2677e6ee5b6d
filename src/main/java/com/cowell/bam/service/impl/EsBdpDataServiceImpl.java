package com.cowell.bam.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.cowell.bam.domain.BamLogDO;
import com.cowell.bam.service.EsBdpDataService;
import org.apache.http.message.BasicHeader;
import org.elasticsearch.action.admin.indices.create.CreateIndexRequest;
import org.elasticsearch.action.admin.indices.create.CreateIndexResponse;
import org.elasticsearch.action.bulk.BulkRequest;
import org.elasticsearch.action.bulk.BulkResponse;
import org.elasticsearch.action.index.IndexRequest;
import org.elasticsearch.action.index.IndexResponse;
import org.elasticsearch.client.Response;
import org.elasticsearch.client.RestHighLevelClient;
import org.elasticsearch.common.xcontent.XContentFactory;
import org.elasticsearch.common.xcontent.XContentType;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.sleuth.annotation.NewSpan;
import org.springframework.scheduling.annotation.AsyncResult;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;
import java.util.concurrent.Future;

/**
 * <AUTHOR>
 * @date: 2018/12/28 16:54
 * @description:
 */
@Service
public class EsBdpDataServiceImpl implements EsBdpDataService {
    private Logger log = LoggerFactory.getLogger(EsBdpDataServiceImpl.class);
    @Autowired
    private RestHighLevelClient restClient;
    @Override
    public String save(BamLogDO bamLogDO){
        log.info("EsBdpDataServiceImpl#save开始保存ES {}",bamLogDO.getZGUID());
        try {
            SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd");
            String indexData = simpleDateFormat.format(new Date());
            if (!IsIndexExists("bam-"+indexData)) {
                log.info("EsBdpDataServiceImpl#batchSave索引不存在，准备创建:{}","bam-"+indexData);
                boolean indexCreated = CreateIndex("bam-"+indexData, "bam");
                if (indexCreated) {
                    log.info("EsBdpDataServiceImpl#batchSave创建索引请求已接受");
                } else {
                    log.error("EsBdpDataServiceImpl#batchSave创建索引未成功，待后续请求处理");
                }
            }
            log.info("EsBdpDataServiceImpl#save保存JSON内容:{}",((JSONObject)JSONObject.toJSON(bamLogDO)).toJSONString());
            IndexRequest indexRequest = new IndexRequest("bam-"+indexData, "bam", bamLogDO.getZGUID());
            indexRequest.source(
                XContentFactory.jsonBuilder()
                    .startObject()
                    .field("id",bamLogDO.getId())
                    .field("zGUID", bamLogDO.getZGUID())
                    .field("zTRACEID", bamLogDO.getZTRACEID())
                    .field("zFLOWID", bamLogDO.getZFLOWID())
                    .field("zSTEPID", bamLogDO.getZSTEPID())
                    .field("zSENDSYSID", bamLogDO.getZSENDSYSID())
                    .field("zRECSYSID", bamLogDO.getZRECSYSID())
                    .field("zCOMID", bamLogDO.getZCOMID())
                    .field("zSTOREID", bamLogDO.getZSTOREID())
                    .field("zTIMESTAMP", bamLogDO.getZTIMESTAMP())
                    .field("zBUSINESSDATE", bamLogDO.getZBUSINESSDATE())
                    .field("zITEMCOUNT", bamLogDO.getZITEMCOUNT())
                    .field("zSTATUS", bamLogDO.getZSTATUS())
                    .field("zMESSAG", bamLogDO.getZMESSAG())
                    .field("zEXTEND1", bamLogDO.getZEXTEND1())
                    .field("zEXTEND2", bamLogDO.getZEXTEND2())
                    .field("zEXTEND3", bamLogDO.getZEXTEND3())
                    .field("zEXTEND4", bamLogDO.getZEXTEND4())
                    .field("env", bamLogDO.getEnv())
                    .field("logtime", bamLogDO.getLogtime())
                    .endObject()
            );
            IndexResponse indexResponse = restClient.index(indexRequest);
            return Integer.toString(indexResponse.status().getStatus());
        } catch (Exception e) {
            log.error("EsBdpDataServiceImpl#save保存ES失败",e);
        }
        return "false";
    }

    @Override
    @NewSpan("bam.EsBdpDataServiceImpl.batchSave")
    public Future<Boolean> batchSave(List<BamLogDO> bamLogDOList){
        log.info("EsBdpDataServiceImpl#batchSave开始保存ES {}",bamLogDOList.size());
        Boolean result = true;
        try {
            SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd");
            String indexData = simpleDateFormat.format(new Date());
            if (!IsIndexExists("bam-"+indexData)) {
                log.info("EsBdpDataServiceImpl#batchSave索引不存在，准备创建:{}","bam-"+indexData);
                boolean indexCreated = CreateIndex("bam-"+indexData, "bam");
                if (indexCreated) {
                    log.info("EsBdpDataServiceImpl#batchSave创建索引请求已接受");
                } else {
                    log.error("EsBdpDataServiceImpl#batchSave创建索引未成功，待后续请求处理");
                }
            }
            BulkRequest bulkRequest = new BulkRequest();
            for(BamLogDO bamLogDO:bamLogDOList){
                // 日志优化，暂时关闭此日志
//                log.info("EsBdpDataServiceImpl#batchSave保存JSON内容:{}",((JSONObject)JSONObject.toJSON(bamLogDO)).toJSONString());
                // 创建单doc请求
                IndexRequest indexRequest = new IndexRequest("bam-"+indexData, "bam", bamLogDO.getZGUID());
                indexRequest.source(
                            XContentFactory.jsonBuilder()
                                .startObject()
                                .field("id",bamLogDO.getId())
                                .field("zGUID", bamLogDO.getZGUID())
                                .field("zTRACEID", bamLogDO.getZTRACEID())
                                .field("zFLOWID", bamLogDO.getZFLOWID())
                                .field("zSTEPID", bamLogDO.getZSTEPID())
                                .field("zSENDSYSID", bamLogDO.getZSENDSYSID())
                                .field("zRECSYSID", bamLogDO.getZRECSYSID())
                                .field("zCOMID", bamLogDO.getZCOMID())
                                .field("zSTOREID", bamLogDO.getZSTOREID())
                                .field("zTIMESTAMP", bamLogDO.getZTIMESTAMP())
                                .field("zBUSINESSDATE", bamLogDO.getZBUSINESSDATE())
                                .field("zITEMCOUNT", bamLogDO.getZITEMCOUNT())
                                .field("zSTATUS", bamLogDO.getZSTATUS())
                                .field("zMESSAG", bamLogDO.getZMESSAG())
                                .field("zEXTEND1", bamLogDO.getZEXTEND1())
                                .field("zEXTEND2", bamLogDO.getZEXTEND2())
                                .field("zEXTEND3", bamLogDO.getZEXTEND3())
                                .field("zEXTEND4", bamLogDO.getZEXTEND4())
                                .field("env", bamLogDO.getEnv())
                                .field("logtime", bamLogDO.getLogtime())
                                .endObject()
                );
                bulkRequest.add(indexRequest);
            }
            BulkResponse bulkResponse = restClient.bulk(bulkRequest);
            if (bulkResponse.hasFailures()) {
                result = false;
                log.error("EsBdpDataServiceImpl#batchSave保存ES返回结果失败",result);
            }
        } catch (IOException e) {
            log.error("EsBdpDataServiceImpl#batchSave保存ES失败",e);
        }
        return new AsyncResult<>(result);
    }

    public boolean IsIndexExists(String index) {
        int statusCode = 0;
        try {
            Response response = restClient.getLowLevelClient().performRequest("HEAD", index, new BasicHeader("Content-Type", "application/json"));
            statusCode = response.getStatusLine().getStatusCode();
        } catch (IOException e) {
            log.error("Check ES index existence failed by ", e);
        }
        if (statusCode == 200) {
            return true;
        } else {
            return false;
        }
    }

    public boolean CreateIndex(String indexname, String indextype) {
        // 创建索引请求
        CreateIndexRequest createIndexRequest = new CreateIndexRequest(indexname);
        createIndexRequest.mapping(indextype,"\n" +
            "  {\n" +
            "    \""+indextype+"\": {\n" +
            "      \"properties\": {\n" +
            "        \"env\": {\n" +
            "            \"type\": \"text\"\n" +
            "        },\n" +
            "        \"id\": {\n" +
            "            \"type\": \"long\"\n" +
            "        },\n" +
            "        \"logtime\": {\n" +
            "            \"type\": \"date\"\n" +
            "        },\n" +
            "        \"zBUSINESSDATE\": {\n" +
            "            \"type\": \"text\"\n" +
            "        },\n" +
            "        \"zCOMID\": {\n" +
            "            \"type\": \"text\"\n" +
            "        },\n" +
            "        \"zEXTEND1\": {\n" +
            "            \"type\": \"text\"\n" +
            "        },\n" +
            "        \"zEXTEND2\": {\n" +
            "            \"type\": \"text\"\n" +
            "        },\n" +
            "        \"zEXTEND3\": {\n" +
            "            \"type\": \"text\"\n" +
            "        },\n" +
            "        \"zEXTEND4\": {\n" +
            "            \"type\": \"text\"\n" +
            "        },\n" +
            "        \"zFLOWID\": {\n" +
            "            \"type\": \"integer\"\n" +
            "        },\n" +
            "        \"zGUID\": {\n" +
            "            \"type\": \"text\"\n" +
            "        },\n" +
            "        \"zITEMCOUNT\": {\n" +
            "            \"type\": \"integer\"\n" +
            "        },\n" +
            "        \"zMESSAG\": {\n" +
            "            \"type\": \"text\"\n" +
            "        },\n" +
            "        \"zRECSYSID\": {\n" +
            "            \"type\": \"text\"\n" +
            "        },\n" +
            "        \"zSENDSYSID\": {\n" +
            "            \"type\": \"text\"\n" +
            "        },\n" +
            "        \"zSTATUS\": {\n" +
            "            \"type\": \"integer\"\n" +
            "        },\n" +
            "        \"zSTEPID\": {\n" +
            "            \"type\": \"text\"\n" +
            "        },\n" +
            "        \"zSTOREID\": {\n" +
            "            \"type\": \"text\"\n" +
            "        },\n" +
            "        \"zTIMESTAMP\": {\n" +
            "            \"type\": \"text\"\n" +
            "        },\n" +
            "        \"zTRACEID\": {\n" +
            "            \"type\": \"text\"\n" +
            "        }\n" +
            "      }\n" +
            "    }\n" +
            "  }\n",
            XContentType.JSON);

        try {
            // 创建索引结果
            CreateIndexResponse createIndexResponse = restClient.indices().create(createIndexRequest);
            // 获取结果
            boolean isAcknowledged = createIndexResponse.isAcknowledged();
            boolean isShardsAcknowledged = createIndexResponse.isShardsAcknowledged();
            return isAcknowledged && isShardsAcknowledged;
        } catch (IOException e) {
            log.error("Created index failed", e);
//            e.printStackTrace();
            return false;
        }
    }

}
