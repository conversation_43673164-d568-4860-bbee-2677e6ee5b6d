package com.cowell.bam.service.impl;

import com.cowell.bam.service.BangJianService;
import com.cowell.bam.service.HdDataService;
import com.cowell.bam.service.dto.BangJianViewDTO;
import com.cowell.bam.service.dto.HdOrderDTO;
import com.cowell.bam.web.rest.vo.BangJianViewVO;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.BeanPropertyRowMapper;
import org.springframework.jdbc.core.namedparam.NamedParameterJdbcTemplate;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * <AUTHOR>
 * @ProjectName bam
 * @Description:
 * @date 2021/01/14 11:51
 */
@Service
public class BangJianServiceImpl implements BangJianService {

    private final Logger log = LoggerFactory.getLogger(BangJianServiceImpl.class);


    @Autowired
    private ThirdService thirdService;

    @Autowired
    private HdDataService hdDataService;


    /**
     * 查询邦健视图
     *
     * @return
     */
    @Override
    public List<BangJianViewDTO> queryBangjianInfo(Long businessId, String startTime, String endTime, Integer pageNumber, Integer pageSize) {
        List<BangJianViewDTO> list = Lists.newArrayList();

        if (Objects.isNull(businessId) || Objects.isNull(pageNumber) || Objects.isNull(pageSize)) {
            log.warn("参数不能为空");
            return list;
        }
        BangJianViewVO bangJianViewVO = new BangJianViewVO(businessId, startTime, endTime, pageNumber, pageSize);
        String comIdCur = thirdService.getComId(bangJianViewVO.getBusinessId());
        //String comIdCur = "1";
        if (StringUtils.isBlank(comIdCur)) {
            log.warn("comIdCur未查询到结果，bussiness is {}", bangJianViewVO.getBusinessId());
            return list;
        }
        log.info("BangJianServiceImpl查询comIdCur为 {}", comIdCur);
        if (bangJianViewVO.getPageNumber() < 1) {
            bangJianViewVO.setPageNumber(1);
        }

        Map<String, Object> paramMap = Maps.newHashMap();
        Integer startNum = bangJianViewVO.getPageNumber() * bangJianViewVO.getPageSize() - bangJianViewVO.getPageSize() + 1;
        Integer endNum = bangJianViewVO.getPageNumber() * bangJianViewVO.getPageSize() + 1;
        paramMap.put("compId", comIdCur);
        paramMap.put("startNum", startNum);
        paramMap.put("endNum", endNum);

        //是否是 邦建（英克）
        boolean isBangJianComId = thirdService.isBangJianComId(comIdCur,"bj");
        //boolean isBangJianComId = true;
        if (!isBangJianComId) {
            log.warn("非邦健用户不予处理");
            return list;
        }
        String querySql = "SELECT ROWNUM RN,compid,usebusnos,coupomnum,couponmanagid,payee,usesaleno,usememcardno,usedate FROM zx_erp2gjcouponVerify_v WHERE compid =:compId ";

        if (StringUtils.isNotBlank(bangJianViewVO.getStartTime()) && StringUtils.isNotBlank(bangJianViewVO.getEndTime())) {
            String startDate = bangJianViewVO.getStartTime();
            paramMap.put("startDate", startDate);

            String endDate = bangJianViewVO.getEndTime();
            paramMap.put("endDate", endDate);

            querySql = querySql + " and usedate >= to_date(:startDate , 'yyyy-MM-dd hh24:mi:ss') and usedate <= to_date(:endDate , 'yyyy-MM-dd hh24:mi:ss')";
        }
        String sql = "SELECT V.compid as compId ," +
            "V.usebusnos as useBusNos," +
            "V.coupomnum as couPomNum ," +
            "V.couponmanagid as couponManagId," +
            "V.payee," +
            "V.usesaleno as useSaleNo," +
            "V.usememcardno as useMemCardNo," +
            "V.usedate as useDate FROM (" + querySql + ") V  " +
            "WHERE V.RN >= :startNum AND V.RN < :endNum order by V.couponmanagid";

        log.info("需要执行查询的sql:{}", sql);

        long tt = System.currentTimeMillis();
        NamedParameterJdbcTemplate jdbcTemplate = new NamedParameterJdbcTemplate(hdDataService.getJdbcTemplate(comIdCur));
        list = jdbcTemplate.query(sql, paramMap, new BeanPropertyRowMapper<>(BangJianViewDTO.class));
        log.info("queryBangjianInfo, param:{}， costTime:{}", paramMap, System.currentTimeMillis() - tt);
        if (CollectionUtils.isEmpty(list)) {
            log.info("查询邦健返回结果为空 :{}", bangJianViewVO);
            return new ArrayList<>();
        }
        return list;
    }

    @Override
    public BangJianViewDTO queryOneByBusinessIdAndCode(Long businessId, String couPomNum) {
        BangJianViewDTO bjDTO = new BangJianViewDTO();
        if (Objects.isNull(businessId) || Objects.isNull(couPomNum)) {
            log.warn("参数不能为空");
            return bjDTO;
        }

        String comIdCur = thirdService.getComId(businessId);
        //String comIdCur = "1";
        if (StringUtils.isBlank(comIdCur)) {
            log.warn("comIdCur未查询到结果，bussiness is {}", businessId);
            return bjDTO;
        }
        log.info("queryOneByBusinessIdAndCode查询comIdCur为 {}", comIdCur);

        Map<String, Object> paramMap = Maps.newHashMap();
        paramMap.put("compId", comIdCur);
        paramMap.put("couPomNum", couPomNum);

        //是否是 邦建（英克）
        boolean isBangJianComId = thirdService.isBangJianComId(comIdCur,"bj");
        //boolean isBangJianComId = true;
        if (!isBangJianComId) {
            log.warn("非邦健用户不予处理");
            return bjDTO;
        }

        String sql = "SELECT V.compid as compId ," +
            "V.usebusnos as useBusNos," +
            "V.coupomnum as couPomNum ," +
            "V.couponmanagid as couponManagId," +
            "V.payee," +
            "V.usesaleno as useSaleNo," +
            "V.usememcardno as useMemCardNo," +
            "V.usedate as useDate " +
            " FROM zx_erp2gjcouponVerify_v V" +
            " WHERE V.compid =:compId And V.coupomnum =:couPomNum ";

        log.info("需要执行查询的sql:{}", sql);

        long tt = System.currentTimeMillis();
        NamedParameterJdbcTemplate jdbcTemplate = new NamedParameterJdbcTemplate(hdDataService.getJdbcTemplate(comIdCur));
        bjDTO = jdbcTemplate.queryForObject(sql, paramMap, new BeanPropertyRowMapper<>(BangJianViewDTO.class));
        log.info("queryBangjianInfo, param:{}， costTime:{}", paramMap, System.currentTimeMillis() - tt);
        if (Objects.isNull(bjDTO)) {
            log.info("查询邦健返回结果为空 businessId:{},code:{}", businessId, couPomNum);
            return new BangJianViewDTO();
        }
        return bjDTO;
    }
    /**
     * 查询邦健积分订单视图，校验积分状态
     * @param userId
     * @param bdid
     * @param compId
     * @return
     */
    @Override
    public List<HdOrderDTO> queryBjOrderForCheckFund(Long userId, String bdid, String compId) {
        Map<String, Object> paramMap = Maps.newHashMap();
        paramMap.put("userId", userId);
        paramMap.put("bdid", bdid);
        paramMap.put("compId", compId);

        String sql = "select scrm_compid as scrmCompid," +
            "mdm_busno as mdmBusno," +
            "cashno," +
            "scrm_userid as scrmUserid," +
            "createtime as createTime," +
            "integral_pst as integralPst," +
            "status from " +
            " zx_gjintegral_v " +
            " where scrm_userid =:userId and cashno =:bdid and scrm_compid = :compId";
        log.info("queryBjOrderForCheckFund-sql:{}, param:{}", sql, paramMap);
        NamedParameterJdbcTemplate jdbcTemplate = new NamedParameterJdbcTemplate(hdDataService.getJdbcTemplate(compId));
        List<HdOrderDTO> list = jdbcTemplate.query(sql, paramMap, new BeanPropertyRowMapper<>(HdOrderDTO.class));
        return list;
    }

    /**
     * 分页查询邦健积分订单视图，校验积分状态
     * @param orderCreateTime
     * @param orderEndTime
     * @param dataSource
     * @param pageNumber
     * @param pageSize
     * @return
     */
    @Override
    public List<HdOrderDTO> queryPageBjOrderForCheckFund(String orderCreateTime, String orderEndTime ,String dataSource,Integer pageNumber,Integer pageSize){
        if (pageNumber < 1) {
            pageNumber = 1;
        }
        Map<String, Object> paramMap = Maps.newHashMap();
        paramMap.put("startDate", orderCreateTime);
        paramMap.put("endDate", orderEndTime);
        paramMap.put("startNum", pageNumber * pageSize - pageSize + 1);
        paramMap.put("endNum", pageNumber * pageSize + 1);

        String sql = "select V.scrm_compid as scrmCompid," +
            " V.mdm_busno as mdmBusno," +
            " V.cashno," +
            " V.scrm_userid as scrmUserid," +
            " V.createtime as createTime," +
            " V.integral_pst as integralPst," +
            " V.status from " +
            " (SELECT ROWNUM RN,scrm_compid,mdm_busno,cashno,scrm_userid," +
            " createtime,integral_pst,status from zx_gjintegral_v " +
            " WHERE createtime > to_date(:startDate , 'yyyy-mm-dd hh24:mi:ss') and createtime < to_date(:endDate , 'yyyy-mm-dd hh24:mi:ss') order by createtime) V " +
            " WHERE V.RN >= :startNum AND V.RN <:endNum";

        log.info("queryPageBjOrderForCheckFund-sql:{}, param:{}", sql, paramMap);
        long tt = System.currentTimeMillis();
        NamedParameterJdbcTemplate jdbcTemplate = new NamedParameterJdbcTemplate(hdDataService.getHdJdbcTemplate(dataSource));
        List<HdOrderDTO> list = jdbcTemplate.query(sql, paramMap, new BeanPropertyRowMapper<>(HdOrderDTO.class));
        log.info("queryPageBjOrderForCheckFund-sql:{}, param:{}， costTime:{}", sql, paramMap, System.currentTimeMillis() - tt);
        return list;
    }
}
