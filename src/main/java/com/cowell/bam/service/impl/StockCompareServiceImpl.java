package com.cowell.bam.service.impl;

import com.alibaba.fastjson.JSON;
import com.cowell.bam.config.ApolloConfig;
import com.cowell.bam.domain.CompareContext;
import com.cowell.bam.domain.DiffDataCompareInfo;
import com.cowell.bam.domain.DiffDataCompareInfoWithBLOBs;
import com.cowell.bam.entity.Stock;
import com.cowell.bam.enums.PriceComparisonEnum;
import com.cowell.bam.enums.SyncTypeEnum;
import com.cowell.bam.enums.SysType;
import com.cowell.bam.mq.CompareStockLoadBalanceProducer;
import com.cowell.bam.mq.DeleteCompareStockProducer;
import com.cowell.bam.mq.NoticeSendStock2ThirdPlatformProducer;
import com.cowell.bam.mq.PriceCenterComparePriceProducer;
import com.cowell.bam.mq.message.CompareOneselfMessage;
import com.cowell.bam.mq.message.CompareStockDTO;
import com.cowell.bam.repository.mybatis.dao.DiffDataCompareInfoMapper;
import com.cowell.bam.service.*;
import com.cowell.bam.service.dto.*;
import com.cowell.bam.web.rest.errors.BusinessErrorException;
import com.cowell.bam.web.rest.util.ConstantPool;
import com.cowell.bam.web.rest.util.RedisKeysConstant;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang.StringUtils;
import org.joda.time.LocalDate;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.sleuth.annotation.NewSpan;
import org.springframework.core.task.AsyncTaskExecutor;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.CountDownLatch;
import java.util.stream.Collectors;

import static com.cowell.bam.enums.NotifySyncStockTypeEnum.STOCK_UPDATE;
import static com.cowell.bam.web.rest.util.DateUtil.getPreviousMinutes;

/**
 * <AUTHOR>
 * @date: 2018/12/27 20:31
 * @description:
 */
@Service
public class StockCompareServiceImpl implements StockCompareService {
    private Logger log = LoggerFactory.getLogger(StockCompareServiceImpl.class);

    private final static Integer PARTITION_SIZE = 200;

    @Autowired
    private IThirdPlatformService thirdPlatformService;
    /**
     * 临界区时间大小 上下5分钟
     */
    private static final int CRITICAL_TIME = 5;


    @Autowired
    private ThirdService thirdService;

    @Autowired
    private NoticeSendStock2ThirdPlatformProducer noticeSendStock2ThirdPlatformProducer;

    @Autowired
    private IStockCenterFeignService stockCenterFeignService;
    public static final String BANGJIANDBSTR = "bj";

    public static final String SAPDBSTR = "sap";

    @Autowired
    private DiffDataCompareInfoMapper diffDataCompareInfoMapper;

    public static final String CENTER_NO_DATA_REASON = "海典未推送该库存到库存中台";
    public static final String THIRD_NO_DATA_REASON = "海典不存在该批次库存";
    public static final String DATA_ERROR_REASON = "海典推送库存和中台库存不一致";
    public static final String HD_UPDATEBATCH_REASON = "海典更改批号中台需要删除的数据";
    @Value("${noticeHDSendData}")
    private String noticeHDSendData;

    @Value("${noticeYKSendData}")
    private String noticeYKSendData;

    @Value("${canUpdateBatchStockBusiness:}")
    private String canUpdateBatchStockBusiness;

    @Autowired
    private ApolloConfig apolloConfig;

    @Autowired
    @Qualifier("taskExecutor")
    private AsyncTaskExecutor executor;

    @Autowired
    @Qualifier("compareStockByGoodsExecutor")
    private AsyncTaskExecutor compareStockByGoodsExecutor;

    @Autowired
    private SendCompareService sendCompareService;

    @Autowired
    private HdDataService hdDataService;

    @Autowired
    private DeleteCompareStockProducer deleteCompareStockProducer;

    @Autowired
    private CompareStockLoadBalanceProducer compareStockLoadBalanceProducer;

    @Autowired
    private PriceCenterComparePriceProducer priceCenterComparePriceProducer;

    @Value("${enableDistributeCompare:false}")
    private boolean enableDistributeCompare;

    @Autowired
    private IRedissonCacheService redissonCacheService;

    /**
     * 对比
     * @return
     */
    @Override
    @NewSpan("StockCompareServiceImpl")
    public boolean compare(String comId) {
        log.info("StockCompareServiceImpl|compare|比对连锁{}开始", comId);

        if (StringUtils.isEmpty(comId)) {
            log.warn("StockCompareServiceImpl|compare|comId为空");
            return true;
        }
        if (enableDistributeCompare) {
            return distributeCompare(comId);
        }
        List<MdmStoreBaseDTO> mdmStoreBaseDTOList = thirdService.findMdmStoreByComId(comId);
//        log.info("StockCompareServiceImpl|compare|根据comId:{},查询后返回mdmStoreBaseDTOList:{}", comId, mdmStoreBaseDTOList);
        if (CollectionUtils.isEmpty(mdmStoreBaseDTOList)) {
            log.warn("StockCompareServiceImpl|compare|根据comid获取storeNo返回值为空入参comId:{}", comId);
            return true;
        }
        String date = new LocalDate().toString(ConstantPool.YYYYMMDD);
        HashSet<String> storeNoSet = new HashSet<>();
        try {
            storeNoSet = thirdService.queryStoreApplyDate(date);
            if (CollectionUtils.isEmpty(storeNoSet)) {
                log.error("CompareTransitStockJobService|获取门店请货日期失败 date:{}", date);
            }
            log.info("StockTransitCompareServiceImpl|compareTransit|根据comId:{},查询后返回门店数量：{}", comId, storeNoSet.size());
        }catch (Exception e) {
            log.error("获取门店请货日期失败|失败comId:{}", comId, e);
            throw new RuntimeException("获取门店请货日期失败");
        }
        for (MdmStoreBaseDTO mdmStoreBaseDTO : mdmStoreBaseDTOList) {
            String storeNo = mdmStoreBaseDTO.getStoreNo();
            if (storeNoSet.contains(storeNo)){
                try {
                    compareStock(comId, storeNo);
                } catch (Exception e) {
                   log.error("StockCompareServiceImpl|compare|比对数据失败｜storeNo:{}", storeNo, e);
                }
            }
        }
        log.info("StockCompareServiceImpl|compare|比对连锁{}结束", comId);
        return true;
    }

    @Override
    public boolean distributeCompare(String comId) {
        log.info("StockCompareServiceImpl|distributeCompare|比对连锁{}开始", comId);

        if (StringUtils.isEmpty(comId)) {
            log.warn("StockCompareServiceImpl|distributeCompare|comId为空");
            return true;
        }
        List<MdmStoreBaseDTO> mdmStoreBaseDTOList = thirdService.findMdmStoreByComId(comId);
        if (CollectionUtils.isEmpty(mdmStoreBaseDTOList)) {
            log.warn("StockCompareServiceImpl|distributeCompare|根据comid获取storeNo返回值为空入参comId:{}", comId);
            return true;
        }
        String date = new LocalDate().toString(ConstantPool.YYYYMMDD);
        HashSet<String> storeNoSet;
        try {
            storeNoSet = thirdService.queryStoreApplyDate(date);
            if (CollectionUtils.isEmpty(storeNoSet)) {
                log.error("CompareTransitStockJobService|获取门店请货日期失败 date:{}", date);
            }
            log.info("StockTransitCompareServiceImpl|compareTransit|根据comId:{},查询后返回门店数量：{}", comId, storeNoSet.size());
        }catch (Exception e) {
            log.error("获取门店请货日期失败|失败comId:{}", comId, e);
            throw new RuntimeException("获取门店请货日期失败");
        }
        HashSet<String> finalStoreNoSet = storeNoSet;
        Map<Long,MdmBusinessBaseDTO> businessMap = Maps.newHashMap();
        for (MdmStoreBaseDTO mdmStoreBaseDTO : mdmStoreBaseDTOList) {
            if (!apolloConfig.isCompareStockAllStore()){
                if (!finalStoreNoSet.contains(mdmStoreBaseDTO.getStoreNo())){
                    log.warn("StockCompareServiceImpl|distributeCompare|门店{}未配置请货日期，跳过比对", mdmStoreBaseDTO.getStoreNo());
                    continue;
                }
            }
            MdmBusinessBaseDTO mdmBusinessBaseDTO = businessMap.get(mdmStoreBaseDTO.getBusinessId());
            if (mdmBusinessBaseDTO == null){
                // 对于有些门店做了连锁拆分合并，需要通过连锁ID找到对应的comId。海典的对比视图存储的是管理架构的连锁ID
                mdmBusinessBaseDTO = thirdService.getMdmBusinessByBusinessId(mdmStoreBaseDTO.getBusinessId());
                if (mdmBusinessBaseDTO == null || Objects.isNull(mdmBusinessBaseDTO.getComId())){
                    log.error("StockCompareServiceImpl｜根据门店编码未查询到商家ID|storeNo:{}|businessId:{}", mdmStoreBaseDTO.getStoreNo(),mdmStoreBaseDTO.getBusinessId());
                    continue;
                }
                businessMap.put(mdmStoreBaseDTO.getBusinessId(), mdmBusinessBaseDTO);
            }
            try {
                CompareStockDTO compareStockDTO = new CompareStockDTO();
                compareStockDTO.setComId(mdmBusinessBaseDTO.getComId());
                compareStockDTO.setStoreNo(mdmStoreBaseDTO.getStoreNo());
                compareStockDTO.setBusinessId(mdmBusinessBaseDTO.getBusinessId());
                compareStockDTO.setStoreId(mdmStoreBaseDTO.getStoreId());
                compareStockDTO.setKey(comId + "_" + mdmStoreBaseDTO.getStoreNo());
                compareStockDTO.setTag("storeNo");
                compareStockLoadBalanceProducer.sendMessage(compareStockDTO);
            } catch (Exception e) {
                log.error("StockCompareServiceImpl|compare|比对数据失败｜storeNo:{}", mdmStoreBaseDTO.getStoreNo(), e);
            }
        }
        mdmStoreBaseDTOList.stream().filter(v-> finalStoreNoSet.contains(v.getStoreNo())).forEach(storeBaseDTO -> {

        });
        log.info("StockCompareServiceImpl|distributeCompare|比对连锁{}结束", comId);
        return true;
    }

    @Override
    public boolean comparePos(String comId) {
        log.info("StockCompareServiceImpl|comparePos|比对连锁{}开始", comId);

        if (StringUtils.isEmpty(comId)) {
            log.warn("StockCompareServiceImpl|comparePos|comId为空");
            return true;
        }
        List<MdmStoreBaseDTO> mdmStoreBaseDTOList = thirdService.findMdmStoreByComId(comId);
        log.info("StockCompareServiceImpl|comparePos|根据comId:{},查询后返回mdmStoreBaseDTOList:{}", comId, mdmStoreBaseDTOList);
        if (CollectionUtils.isEmpty(mdmStoreBaseDTOList)) {
            log.warn("StockCompareServiceImpl|comparePos|根据comid获取storeNo返回值为空入参comId:{}", comId);
            return true;
        }
        List<String> storeNos = mdmStoreBaseDTOList.stream().map(MdmStoreBaseDTO::getStoreNo).collect(Collectors.toList());
        for (String storeNo : storeNos) {
            try {
                comparePosStock(comId, storeNo);
            } catch (Exception e) {
                log.error("StockCompareServiceImpl|comparePos|比对数据失败comId:{},storeNo:{}", comId, storeNo, e);
                continue;
            }
        }
        log.info("StockCompareServiceImpl|comparePos|比对连锁{}结束", comId);
        return true;
    }

    @Override
    public boolean comparePosStock(String comId, String storeNo) {
        log.info("StockCompareServiceImpl|comparePosStock|库存比对开始comId:{},storeNo:{}", comId, storeNo);
        //判断调用海典数据库还是邦健数据库
        String dbSource = hdDataService.getDBSource(comId);
        List<Stock> stockList;
        if (org.apache.commons.lang3.StringUtils.isEmpty(dbSource) || !BANGJIANDBSTR.equals(dbSource)) {
            log.info("StockCompareServiceImpl|comparePosStock|查询海典数据库开始:comId:{},storeNo:{}", comId, storeNo);
            stockList = hdDataService.queryHdStockData(comId, storeNo);
            log.info("StockCompareServiceImpl|comparePosStock|查询海典数据库结束result:{}", stockList.size());
        } else {
            log.info("StockCompareServiceImpl|comparePosStock|查询所有英克数据库开始:comId:{},storeNo:{}", comId, storeNo);
            stockList = hdDataService.queryAllBjStockData(comId, storeNo);
            log.info("StockCompareServiceImpl|comparePosStock|查询英克数据库结束result:{}", stockList.size());
        }

        if (CollectionUtils.isEmpty(stockList)) {
            log.warn("StockCompareServiceImpl|comparePosStock|海典推送库存数据为空！！");
            return true;
        }
        String businessIdAndStoreIdStr = thirdService.getBusinessIdAndStoreId(comId, storeNo);
        String[] businessIdAndStoreIdStrArr = businessIdAndStoreIdStr.split(":");
        if (StringUtils.isBlank(businessIdAndStoreIdStrArr[0]) || StringUtils.isBlank(businessIdAndStoreIdStrArr[1])) {
            log.error("根据连锁门店编码、商品编码未查询到连锁ID、门店ID 连锁编码:{} 门店编码:{}", comId, storeNo);
            throw new BusinessErrorException("根据连锁门店编码、商品编码未查询到连锁ID、门店ID");
        }

        Long businessId = Long.parseLong(businessIdAndStoreIdStrArr[0]);
        Long storeId = Long.parseLong(businessIdAndStoreIdStrArr[1]);

        int page = 1, size = 100;
        Set<String> goodsNoSet = new HashSet<>();
        List<StoreGoodsNoDTO> list = null;
        do {
            ResponseEntity<PageInfo<StoreGoodsNoDTO>> response = stockCenterFeignService.getGoodsNoByStoreId(storeId, page, size);
            if (Objects.nonNull(response) && HttpStatus.OK.equals(response.getStatusCode())) {
                list = response.getBody().getList();
                if (CollectionUtils.isNotEmpty(list)) {
                    goodsNoSet.addAll(list.stream().map(StoreGoodsNoDTO::getSkuMerchantCode).collect(Collectors.toSet()));
                }
                page++;
            }
        } while (CollectionUtils.isNotEmpty(list));
        List<String> goodsNoList = new ArrayList<>(goodsNoSet);

        log.info("StockCompareServiceImpl|comparePosStock|comId={},storeNo={},businessId={},storeId={},goodsNoListSize:{}",
            comId, storeNo, businessId, storeId, goodsNoList.size());

        //获取批次库存列表
        List<StockGoodsCountInfo> batchCodeList = getBatchCodeList(businessId, storeId, goodsNoList);

        if (CollectionUtils.isEmpty(batchCodeList)) {
            sendDiffData2Hd(businessId, storeId);
            log.info("StockCompareServiceImpl|comparePosStock|中台该门店无库存,需要英克海典全量推送:businessId:{},storeId:{}", businessId, storeId);
            return true;
        }

        compareStock(stockList, batchCodeList, businessId, storeId, dbSource, "StockCenterComparePos");

        stockList = null;
        return true;
    }

    @Override
    public boolean compareStock(String comId, String storeNo) {
        log.info("StockCompareServiceImpl|compare|库存比对开始comId:{},storeNo:{}", comId, storeNo);
        //判断调用海典数据库还是邦健数据库
        String dbSource = hdDataService.getDBSource(comId);
        List<Stock> stockList;
        if (org.apache.commons.lang3.StringUtils.isEmpty(dbSource) || !BANGJIANDBSTR.equals(dbSource)) {
            log.info("StockCompareServiceImpl|compare|查询海典数据库开始:comId:{},storeNo:{}", comId, storeNo);
            stockList = hdDataService.queryHdStockData(comId, storeNo);
            log.info("StockCompareServiceImpl|compare|查询海典数据库结束|comId={}|storeNo={}|result:{}", comId,storeNo,stockList.size());
        } else {
            log.info("StockCompareServiceImpl|compare|查询所有英克数据库开始:comId:{},storeNo:{}", comId, storeNo);
            stockList = hdDataService.queryAllBjStockData(comId, storeNo);
            log.info("StockCompareServiceImpl|compare|查询英克数据库结束result:{}", stockList.size());
        }

        if (CollectionUtils.isEmpty(stockList)) {
            log.warn("StockCompareServiceImpl|compare|海典推送库存数据为空！！");
            return true;
        }
        MdmStoreBaseDTO mdmStoreByStoreNo = thirdService.getMdmStoreByStoreNo(storeNo);
        if (mdmStoreByStoreNo == null){
            log.error("根据门店编码未查询到门店信息 门店编码:{}", storeNo);
            throw new BusinessErrorException("根据门店编码未查询到门店信息");
        }
        Long businessId = mdmStoreByStoreNo.getBusinessId();
        Long storeId = mdmStoreByStoreNo.getStoreId();
        List<String> goodsNoList = stockList.stream().map(a -> StringUtils.trim(a.getWarecode()))
            .filter(StringUtils::isNotBlank).distinct().collect(Collectors.toList());

        log.info("StockCompareServiceImpl|compare|comId={},storeNo={},businessId={},storeId={},goodsNoListSize:{}",
            comId, storeNo, businessId, storeId, goodsNoList.size());

        //获取批次库存列表
        List<StockGoodsCountInfo> batchCodeList = getBatchCodeList(businessId, storeId, goodsNoList);

        if (CollectionUtils.isEmpty(batchCodeList)) {
            sendDiffData2Hd(businessId, storeId);
            log.info("StockCompareServiceImpl|compare|中台该门店无库存,需要英克海典全量推送:businessId:{},storeId:{}", businessId, storeId);
            return true;
        }

        compareStock(stockList, batchCodeList, businessId, storeId, dbSource, "PosCompareStockCenter");

        stockList = null;
        return true;
    }

    @Override
    public boolean compareStock(CompareStockDTO compareStockDTO) {
        log.info("StockCompareServiceImpl|compareStock|compareStockDTO={}", JSON.toJSONString(compareStockDTO));
        //判断调用海典数据库还是邦健数据库
        String comId = compareStockDTO.getComId();
        String storeNo = compareStockDTO.getStoreNo();
        String dbSource = hdDataService.getDBSource(comId);
        List<Stock> stockList;
        if (org.apache.commons.lang3.StringUtils.isEmpty(dbSource) || !BANGJIANDBSTR.equals(dbSource)) {
            log.info("StockCompareServiceImpl|compareStockDTO|查询海典数据库开始:comId:{},storeNo:{}", comId, storeNo);
            stockList = hdDataService.queryHdStockGoodsData(comId, storeNo);
            log.info("StockCompareServiceImpl|compareStockDTO|查询海典数据库结束result:{}", stockList.size());
        } else {
            log.info("StockCompareServiceImpl|compareStockDTO|查询所有英克数据库开始:comId:{},storeNo:{}", comId, storeNo);
            stockList = hdDataService.queryAllBjStockGoodsData(comId, storeNo);
            log.info("StockCompareServiceImpl|compareStockDTO|查询英克数据库结束result:{}", stockList.size());
        }

        if (CollectionUtils.isEmpty(stockList)) {
            log.warn("StockCompareServiceImpl|compareStockDTO|海典推送库存数据为空！！");
            return true;
        }
        Long businessId = compareStockDTO.getBusinessId();
        Long storeId = compareStockDTO.getStoreId();

        List<List<Stock>> partition = Lists.partition(stockList, 50);

        CountDownLatch countDownLatch = new CountDownLatch(partition.size());
        partition.forEach(stocks -> compareStockByGoodsExecutor.execute(() -> {
            List<String> goodsNoList = stocks.stream().map(a -> StringUtils.trim(a.getWarecode()))
                .filter(StringUtils::isNotBlank).distinct().collect(Collectors.toList());

            log.info("StockCompareServiceImpl|compareStockDTO|comId={},storeNo={},businessId={},storeId={},goodsNoList:{}",
                comId, storeNo, businessId, storeId, goodsNoList);
            doCompareStockByGoodsNoList(dbSource, comId, storeNo, businessId, storeId, goodsNoList);
            countDownLatch.countDown();
        }));
        try {
            countDownLatch.await();
        } catch (Exception e) {
            log.error("StockCompareServiceImpl|compareStockDTO 执行异常, storeNo:{}", storeNo, e);
        }
        log.info("StockCompareServiceImpl|compareStockDTO|库存比对结束");
        return true;
    }

    /**
     * 按请货日门店 挨个对比库存
     * @param comId
     * @param storeNo
     * @return
     */
    @Override
    public boolean compareStockByGoods(String comId, String storeNo) {
        log.info("StockCompareServiceImpl|compareStockByGoods|库存比对开始comId:{},storeNo:{}", comId, storeNo);
        //判断调用海典数据库还是邦健数据库
        String dbSource = hdDataService.getDBSource(comId);
        List<Stock> stockList;
        if (org.apache.commons.lang3.StringUtils.isEmpty(dbSource) || !BANGJIANDBSTR.equals(dbSource)) {
            log.info("StockCompareServiceImpl|compareStockByGoods|查询海典数据库开始:comId:{},storeNo:{}", comId, storeNo);
            stockList = hdDataService.queryHdStockGoodsData(comId, storeNo);
            log.info("StockCompareServiceImpl|compareStockByGoods|查询海典数据库结束result:{}", stockList.size());
        } else {
            log.info("StockCompareServiceImpl|compareStockByGoods|查询所有英克数据库开始:comId:{},storeNo:{}", comId, storeNo);
            stockList = hdDataService.queryAllBjStockGoodsData(comId, storeNo);
            log.info("StockCompareServiceImpl|compareStockByGoods|查询英克数据库结束result:{}", stockList.size());
        }

        if (CollectionUtils.isEmpty(stockList)) {
            log.warn("StockCompareServiceImpl|compareStockByGoods|海典推送库存数据为空！！");
            return true;
        }
        //查询门店对应关系
        String businessIdAndStoreIdStr = thirdService.getBusinessIdAndStoreId(comId, storeNo);
        String[] businessIdAndStoreIdStrArr = businessIdAndStoreIdStr.split(":");
        if (StringUtils.isBlank(businessIdAndStoreIdStrArr[0]) || StringUtils.isBlank(businessIdAndStoreIdStrArr[1])) {
            log.error("根据连锁门店编码、商品编码未查询到连锁ID、门店ID 连锁编码:{} 门店编码:{}", comId, storeNo);
            return true;
        }

        Long businessId = Long.parseLong(businessIdAndStoreIdStrArr[0]);
        Long storeId = Long.parseLong(businessIdAndStoreIdStrArr[1]);

        List<List<Stock>> partition = Lists.partition(stockList, 50);

        CountDownLatch countDownLatch = new CountDownLatch(partition.size());
        partition.forEach(stocks -> compareStockByGoodsExecutor.execute(() -> {
            List<String> goodsNoList = stocks.stream().map(a -> StringUtils.trim(a.getWarecode()))
                .filter(StringUtils::isNotBlank).distinct().collect(Collectors.toList());

            log.info("StockCompareServiceImpl|compareStockByGoods|comId={},storeNo={},businessId={},storeId={},goodsNoList:{}",
                comId, storeNo, businessId, storeId, goodsNoList);
            doCompareStockByGoodsNoList(dbSource, comId, storeNo, businessId, storeId, goodsNoList);
            countDownLatch.countDown();
        }));
        try {
            countDownLatch.await();
        } catch (Exception e) {
            log.error("StockCompareServiceImpl|compareStockByGoods 执行异常, storeNo:{},Exception:{}", storeNo, e);
        }
        log.info("StockCompareServiceImpl|compareStockByGoods|库存比对结束");
        return true;
    }

    /**
     * 统计有库存的商品warecode，放入Set集合
     * @param stockList 库存列表
     * @return 包含所有有库存商品warecode的Set
     */
    public Set<String> getStockedWarecodes(List<Stock> stockList) {
        Set<String> stockedWarecodes = new HashSet<>();

        if (stockList == null || stockList.isEmpty()) {
            return stockedWarecodes;
        }

        // 阈值：0.001
        BigDecimal threshold = new BigDecimal("0.001");
        for (Stock stock : stockList) {
            String warecode = stock.getWarecode();
            // 跳过warecode为空的记录
            if (warecode == null || warecode.trim().isEmpty()) {
                continue;
            }
            // 解析tnum 字段
            BigDecimal tnum = toBigDecimal(stock.getTnum());
            // tnum > 0.001 就认为有库存
            boolean hasStock = tnum.compareTo(threshold) > 0;
            if (hasStock) {
                stockedWarecodes.add(warecode.trim());
            }
        }
        return stockedWarecodes;
    }

    /**
     * 将字符串转换为BigDecimal，处理null和空字符串的情况
     */
    private BigDecimal toBigDecimal(String value) {
        if (value == null || value.trim().isEmpty()) {
            return BigDecimal.ZERO;
        }
        try {
            return new BigDecimal(value.trim());
        } catch (NumberFormatException e) {
            return BigDecimal.ZERO;
        }
    }

    private void doCompareStockByGoodsNoList(String dbSource, String comId, String storeNo, Long businessId, Long storeId, List<String> goodsNoList) {

        List<Stock> stockList = queryPosStockInfo(dbSource, comId, storeNo, goodsNoList);
        if (CollectionUtils.isEmpty(stockList)) {
            log.warn("StockCompareServiceImpl|doCompareStockByGoodsNoList|海典推送库存数据为空！！");
            return;
        }else {
            //有库存的比较价格 已经是分页处理了
            Set<String> stockedWarecodes = getStockedWarecodes(stockList);
            if (!CollectionUtils.isEmpty(stockedWarecodes)){
                CompareOneselfMessage message = new CompareOneselfMessage();
                message.setComId(comId);
                message.setBusNo(storeNo);
                message.setPriceRepair(false);
                message.setIsPriceComparison(PriceComparisonEnum.BAM_COMPARE_STORE_GOODS_PRICE.getCode());
                message.setGoodsNoList(new ArrayList<>(stockedWarecodes));
                priceCenterComparePriceProducer.sendMessageToCompareTaskSplit(message);
                log.info("发送比较价格MQ成功 连锁编码 :{},门店编码 :{} goodsNoList={}", comId, storeNo,goodsNoList);
            }
        }
        //获取批次库存列表
        List<StockGoodsCountInfo> batchCodeList = getBatchCodeList(businessId, storeId, goodsNoList);
        if (CollectionUtils.isEmpty(batchCodeList)) {
            sendDiffData2Hd(businessId, storeId);
            log.info("StockCompareServiceImpl|doCompareStockByGoodsNoList|中台该门店无库存,需要英克海典全量推送:businessId:{},storeId:{}", businessId, storeId);
            return;
        }

        compareStock(stockList, batchCodeList, businessId, storeId, dbSource, "PosCompareStockCenter");
    }

    private List<Stock> queryPosStockInfo(String dbSource, String comId, String storeNo, List<String> goodsNoList) {
        List<Stock> stockList;
        if (org.apache.commons.lang3.StringUtils.isEmpty(dbSource) || !BANGJIANDBSTR.equals(dbSource)) {
            log.info("StockCompareServiceImpl|queryPosStockInfo|查询海典数据库开始:comId:{},storeNo:{}", comId, storeNo);
            stockList = hdDataService.queryHdStockInfo(comId, storeNo, goodsNoList);
            log.info("StockCompareServiceImpl|queryPosStockInfo|查询海典数据库结束result:{}", stockList.size());
        } else {
            log.info("StockCompareServiceImpl|queryPosStockInfo|查询所有英克数据库开始:comId:{},storeNo:{}", comId, storeNo);
            stockList = hdDataService.queryBjStockData(comId, storeNo, goodsNoList);
            log.info("StockCompareServiceImpl|queryPosStockInfo|查询英克数据库结束result:{}", stockList.size());
        }
        return stockList;
    }

    @Override
    public Map<String, Integer> queryYKStoreCount(String comId) {
        Map<String, Integer> totalMap = Maps.newHashMap();
        if (StringUtils.isEmpty(comId)) {
            return totalMap;
        }
        List<MdmStoreBaseDTO> mdmStoreBaseDTOList = thirdService.findMdmStoreByComId(comId);
        if (CollectionUtils.isEmpty(mdmStoreBaseDTOList)) {
            return totalMap;
        }

        List<String> storeNos = mdmStoreBaseDTOList.stream().map(MdmStoreBaseDTO::getStoreNo).collect(Collectors.toList());

        for (String storeNo : storeNos) {
            try {
                totalMap.put(storeNo, hdDataService.queryYKStoreCount(comId, storeNo));
            } catch (Exception e) {
                log.error("StockCompareServiceImpl|queryYKStoreCount|获取门店库存数据失败comId:{},storeNo:{}", comId, storeNo, e);
                continue;
            }
        }
        return totalMap;
    }


    /**
     * 库存比对
     *
     * @param hdStockList
     * @param stockGoodsInfoList
     */
    private void compareStock(List<Stock> hdStockList, List<StockGoodsCountInfo> stockGoodsInfoList, Long businessId, Long storeId, String dbSource, String type) {

        Map<String, StockGoodsCountInfo> storeStockMap = stockGoodsInfoList.stream()
            .collect(Collectors.toMap(info -> info.getSkuMerchantCode() + "_" + StringUtils.lowerCase(info.getBatchNo()) + "_" + info.getBatchCode(), info -> info, (v1, v2) -> v2));

        //需要通知SAP重新推动库存进行更新的物料集合
        List<DiffDataCompareInfoWithBLOBs> notifySendList = new ArrayList<>();

        //存在差异的物料集合(排除临界区时间数据)
        List<DiffDataCompareInfoWithBLOBs> diffDataCompareInfos = new ArrayList<>();

        List<StockGoodsCountInfo> stockGoodsCountInfoList = new ArrayList<>();

        //排除临界时间区间发生库存变动的数据
        Date now = new Date();
        //当前时间5分钟前
        long min = getPreviousMinutes(now, CRITICAL_TIME).getTime();
        //当前时间5分钟后
        long max = getPreviousMinutes(now, 0 - CRITICAL_TIME).getTime();

//        log.info("StockCompareServiceImpl|compareStock|businessId={},storeId={},min={},max={},now={}",
//            businessId, storeId, min, max, now);

        Set<String> executeKey = Sets.newHashSet();

        for (Stock cmallStockRequestDTO : hdStockList) {

            //batchNo批次号 批次号存在空格制表符问题 如:
            //{"batchNo":"9993046546","makeNo":"061101              ","num":"1000","tNum":"1000","wareCode":"1068915"}
            //{"batchNo":"9956003899","makeNo":"20191103\t","num":"2","tNum":"2","wareCode":"1096956"}

            String markNo = StringUtils.trim(cmallStockRequestDTO.getMakeno());
            // 海典生产批号有空格，导致匹配有问题(需要去除掉空格)
            String batchNo = StringUtils.trim(cmallStockRequestDTO.getBatchno());
            String key = cmallStockRequestDTO.getWarecode() + "_" + StringUtils.lowerCase(markNo) + "_" + batchNo;

            if (executeKey.contains(key)) {
                log.info("StockCompareServiceImpl|compareStock|已经对比过此数据|markNo={},key={},海典库存={}",
                    cmallStockRequestDTO.getMakeno(), key, cmallStockRequestDTO);
                continue;
            }

            StockGoodsCountInfo storeStock = storeStockMap.get(key);

//            log.info("StockCompareServiceImpl|compareStock|markNo={},key={},海典库存={},库存中台库存={}",
//                cmallStockRequestDTO.getMakeno(), key, cmallStockRequestDTO, storeStock);


            executeKey.add(key);

            if (storeStock == null) {
                if (new BigDecimal(0.001).compareTo(new BigDecimal(cmallStockRequestDTO.getTnum())) > 0
                    && new BigDecimal(0.001).compareTo(new BigDecimal(cmallStockRequestDTO.getNum())) > 0) {
                    continue;
                }
                log.info("StockCompareServiceImpl|compareStock|库存比对|海典库存:{},库存中心商品库存为空:{}", cmallStockRequestDTO, storeStock);
                DiffDataCompareInfoWithBLOBs diffDataCompareInfo = new DiffDataCompareInfoWithBLOBs();
                diffDataCompareInfo.setReason(CENTER_NO_DATA_REASON);
                diffDataCompareInfo.setStoreId(storeId);
                diffDataCompareInfo.setBusinessId(businessId);
                getDiffDataCompareInfo(cmallStockRequestDTO, storeStock, diffDataCompareInfo);
                notifySendList.add(diffDataCompareInfo);

                //非临界时间内的数据需要放入对比差异记录表
                if (!inCriticalTime(cmallStockRequestDTO, min, max)) {
                    diffDataCompareInfos.add(diffDataCompareInfo);
                }

            } else {
                BigDecimal tnum = new BigDecimal(cmallStockRequestDTO.getTnum()).setScale(3, BigDecimal.ROUND_HALF_UP);
                BigDecimal num = new BigDecimal(cmallStockRequestDTO.getNum()).setScale(3, BigDecimal.ROUND_HALF_UP);
                if (num.compareTo(storeStock.getBuyStock()) != 0 ||
                    tnum.compareTo(storeStock.getStock()) != 0) {
                    DiffDataCompareInfoWithBLOBs diffDataCompareInfo = new DiffDataCompareInfoWithBLOBs();
                    diffDataCompareInfo.setReason(DATA_ERROR_REASON);
                    diffDataCompareInfo.setBusinessId(businessId);
                    diffDataCompareInfo.setStoreId(storeId);
                    getDiffDataCompareInfo(cmallStockRequestDTO, storeStock, diffDataCompareInfo);
                    notifySendList.add(diffDataCompareInfo);

                    //非临界时间内的数据需要放入对比差异记录表
                    if (!inCriticalTime(cmallStockRequestDTO, min, max)) {
                        diffDataCompareInfos.add(diffDataCompareInfo);
                    }
//                    continue;
                }
                //删除比对完的库存
                storeStockMap.remove(key);
            }
        }

        if (MapUtils.isNotEmpty(storeStockMap)) {
            String value = getFirstNumber(getLastNumber(storeId + "", 10), 5);
            long db = Long.parseLong(getFirstNumber(value, 2)) % 4L;
            Long table = Long.parseLong(getLastNumber(value, 3)) % 256;
            for (String key : storeStockMap.keySet()) {
                StockGoodsCountInfo stockGoodsCountInfo = storeStockMap.get(key);
                if ("GaoJiTransitNo".equals(stockGoodsCountInfo.getBatchNo()) && "GaoJiTransitCode".equals(stockGoodsCountInfo.getBatchCode())) {
                    continue;
                }
                //not like '82%' and warecode not like '83%' and warecode not like '84%'
                String skuMerchantCode = stockGoodsCountInfo.getSkuMerchantCode();
                if (skuMerchantCode.startsWith("82") || skuMerchantCode.startsWith("83") || skuMerchantCode.startsWith("84")) {
                    continue;
                }
                if (new BigDecimal(0).compareTo(stockGoodsCountInfo.getStock()) == 0
                    && new BigDecimal(0).compareTo(stockGoodsCountInfo.getBuyStock()) == 0) {
                    continue;
                }

                stockGoodsCountInfoList.add(stockGoodsCountInfo);
                DiffDataCompareInfoWithBLOBs diffDataCompareInfo = new DiffDataCompareInfoWithBLOBs();
                diffDataCompareInfo.setReason(THIRD_NO_DATA_REASON);
                diffDataCompareInfo.setBusinessId(businessId);
                diffDataCompareInfo.setStoreId(storeId);
                getDiffDataCompareInfo(null, storeStockMap.get(key), diffDataCompareInfo);
                notifySendList.add(diffDataCompareInfo);
                diffDataCompareInfos.add(diffDataCompareInfo);
                log.warn("StockCompareServiceImpl|compareStock|海典更改批次后需要删除的商品数据库:{}", JSON.toJSONString(diffDataCompareInfo));
            }
        }
        if (CollectionUtils.isNotEmpty(stockGoodsCountInfoList)) {
            log.debug("stockGoodsCountInfoList:{},dbSource:{}", stockGoodsCountInfoList.size(),dbSource);
            if (!BANGJIANDBSTR.equals(dbSource)) {
                List<List<StockGoodsCountInfo>> partition = Lists.partition(stockGoodsCountInfoList, 100);
                partition.forEach(p -> deleteCompareStockProducer.send(JSON.toJSONString(p)));
            } else if (StringUtils.isNotBlank(canUpdateBatchStockBusiness) && "StockCenterComparePos".equals(type)) {
                // 英克可以删除库存的连锁
                String[] split = canUpdateBatchStockBusiness.split(",");
                List<String> collect = Arrays.stream(split).collect(Collectors.toList());
                if (collect.contains(businessId.toString())) {
                    List<List<StockGoodsCountInfo>> partition = Lists.partition(stockGoodsCountInfoList, 100);
                    partition.forEach(p -> deleteCompareStockProducer.send(JSON.toJSONString(p)));
                }
            }
        }





        //插入存在差异的数据到对比表
        executor.execute(() -> {
            for (DiffDataCompareInfoWithBLOBs diffDataCompareInfo : diffDataCompareInfos) {
                diffDataCompareInfoMapper.insertSelective(diffDataCompareInfo);
            }
        });

        //通知重发存在差异的数据
        executor.execute(() -> {
            for (List<DiffDataCompareInfoWithBLOBs> list : Lists.partition(notifySendList, 500)) {
                send2HD(list);
            }
        });
        log.info("StockCompareServiceImpl|StockCompareServiceImpl|compareStock|库存比对结束");
    }

    /**
     * 是否临界区数据
     *
     * @param hdStock
     * @param min
     * @param max
     * @return
     */
    private boolean inCriticalTime(Stock hdStock, long min, long max) {
        if (Objects.isNull(hdStock.getSyncTime())) {
            return false;
        }
        if (hdStock.getSyncTime().getTime() > min && hdStock.getSyncTime().getTime() < max) {
            return true;
        }
        return false;
    }


    public static String getFirstNumber(String idStr, int length) {
        idStr = idStr.substring(0, length);
        return String.format("%0" + length + "d", Long.valueOf(idStr));
    }

    public static String getLastNumber(String idStr, int length) {
        int size = Math.min(idStr.length(), length);
        idStr = idStr.substring(idStr.length() - size);
        return String.format("%0" + length + "d", Long.valueOf(idStr));
    }

    private void getDiffDataCompareInfo(Stock cmallStockRequestDTO, StockGoodsCountInfo storeStock, DiffDataCompareInfoWithBLOBs diffDataCompareInfo) {

        fillViewStockInfo(diffDataCompareInfo, cmallStockRequestDTO);
        fillDiffBasicInfo(diffDataCompareInfo);
        fillStockGoodsInfo(diffDataCompareInfo, storeStock);
    }

    private void fillDiffBasicInfo(DiffDataCompareInfoWithBLOBs diffDataCompareInfo){
        diffDataCompareInfo.setDataType(SyncTypeEnum.STOCK.getCode());
        diffDataCompareInfo.setStatus(0);
        diffDataCompareInfo.setUpdatedBy("-1");
        diffDataCompareInfo.setCreatedBy("-1");
        diffDataCompareInfo.setGmtCreate(new Date());
        diffDataCompareInfo.setGmtUpdate(new Date());
    }

    private void fillStockGoodsInfo(DiffDataCompareInfoWithBLOBs diffDataCompareInfo, StockGoodsCountInfo storeStock) {
        if (Objects.isNull(storeStock)) {
            return;
        }
        BeanUtils.copyProperties(storeStock, diffDataCompareInfo);
        diffDataCompareInfo.setGoodsNo(storeStock.getSkuMerchantCode());
        StockDataInfoDTO stockDataInfoDTO = new StockDataInfoDTO();
        BeanUtils.copyProperties(storeStock, stockDataInfoDTO);
        stockDataInfoDTO.setSkuMerchantCode(storeStock.getSkuMerchantCode());
        stockDataInfoDTO.setBatchNo(storeStock.getBatchNo());
        stockDataInfoDTO.setBatchCode(storeStock.getBatchCode());
        stockDataInfoDTO.setStock(storeStock.getStock());
        stockDataInfoDTO.setBuyStock(storeStock.getBuyStock());
        diffDataCompareInfo.setOurData(JSON.toJSONString(stockDataInfoDTO));
    }

    private void fillViewStockInfo(DiffDataCompareInfoWithBLOBs diffDataCompareInfo, Stock viewStock) {
        if (Objects.isNull(viewStock)) {
            return;
        }
        ThridStockInfoDTO thridStockInfoDTO = new ThridStockInfoDTO();
        diffDataCompareInfo.setGoodsNo(viewStock.getWarecode());
        BeanUtils.copyProperties(viewStock, thridStockInfoDTO);
        thridStockInfoDTO.setWareCode(viewStock.getWarecode());
        thridStockInfoDTO.setMakeNo(viewStock.getMakeno());
        thridStockInfoDTO.setBatchNo(viewStock.getBatchno());
        thridStockInfoDTO.settNum(viewStock.getTnum());
        thridStockInfoDTO.setNum(viewStock.getNum());
        diffDataCompareInfo.setThirdData(JSON.toJSONString(thridStockInfoDTO));
    }

    private void fillSyncStockInfo(DiffDataCompareInfoWithBLOBs diffDataCompareInfo,CmallStockRequestDTO stockRequest){
        if (Objects.isNull(stockRequest)) {
            return;
        }
        ThridStockInfoDTO thridStockInfoDTO = new ThridStockInfoDTO();
        diffDataCompareInfo.setGoodsNo(stockRequest.getWareCode());
        BeanUtils.copyProperties(stockRequest, thridStockInfoDTO);
        thridStockInfoDTO.setWareCode(stockRequest.getWareCode());
        thridStockInfoDTO.setMakeNo(stockRequest.getMakeNo());
        thridStockInfoDTO.setBatchNo(stockRequest.getBatchNo());
        thridStockInfoDTO.settNum(stockRequest.gettNum());
        thridStockInfoDTO.setNum(stockRequest.getNum());
        diffDataCompareInfo.setThirdData(JSON.toJSONString(thridStockInfoDTO));
    }
    /**
     * 回调海典补偿库存价格数据
     *
     * @return
     */
    @Override
    public void send2HD(List<DiffDataCompareInfoWithBLOBs> diffDataCompareInfos) {
        Map<String, List<DiffDataCompareInfo>> collect = diffDataCompareInfos.stream().collect(Collectors.groupingBy(a -> a.getBusinessId() + "_" + a.getStoreId()));
        for (String bid_storeId : collect.keySet()) {
            List<DiffDataCompareInfo> infos = collect.get(bid_storeId);
            CmallReqDTO cmallStockReqDTO = new CmallReqDTO();
            String[] arr = StringUtils.split(bid_storeId, "_");
            MdmDataTransformDTO mdmDataTransformDTO = thirdService.getMdmDataTransformDTO(Long.valueOf(arr[0]), Long.valueOf(arr[1]));
            if (mdmDataTransformDTO == null || CollectionUtils.isEmpty(mdmDataTransformDTO.getStoreNos())) {
                continue;
            }
            cmallStockReqDTO.setCompId(mdmDataTransformDTO.getComId());
            cmallStockReqDTO.setBusNo(mdmDataTransformDTO.getStoreNos().get(0));
            cmallStockReqDTO.setDetails(sendCompareService.getInfoList(infos));
            CompareDataDTO compareDataDTO = getCompareDataDTO(cmallStockReqDTO);
            sendCompareService.callBackHdCompensate(compareDataDTO);
        }
    }

    private CompareDataDTO getCompareDataDTO(CmallReqDTO cmallStockReqDTO) {
        CompareDataDTO compareDataDTO = new CompareDataDTO();
        compareDataDTO.setBdata(JSON.toJSONString(cmallStockReqDTO));
        compareDataDTO.setComId(cmallStockReqDTO.getCompId());
        compareDataDTO.setBtype(ConstantPool.BTYPE_STOCK);

        //根据comid分配url
        String dbSource = hdDataService.getDBSource(cmallStockReqDTO.getCompId());
        if (org.apache.commons.lang3.StringUtils.isEmpty(dbSource) || !BANGJIANDBSTR.equals(dbSource)) {
            compareDataDTO.setRequestUrl(noticeHDSendData + ConstantPool.STOCK_REQUEST_URL);
        } else {
            compareDataDTO.setRequestUrl(noticeYKSendData + ConstantPool.YK_STOCK_REQUEST_URL);
        }

        compareDataDTO.setServiceDesc("库存对比服务");
        return compareDataDTO;
    }

    /**
     * 回调海典补偿库存价格数据
     *
     * @return
     */
    @Override
    public void sendParam2HD(String comId, String busNo, List<CompareDataInfo> details) {
        CmallReqDTO cmallStockReqDTO = new CmallReqDTO();
        cmallStockReqDTO.setCompId(comId);
        cmallStockReqDTO.setBusNo(busNo);
        cmallStockReqDTO.setDetails(details);
        CompareDataDTO compareDataDTO = new CompareDataDTO();
        compareDataDTO.setBdata(JSON.toJSONString(cmallStockReqDTO));
        compareDataDTO.setComId(cmallStockReqDTO.getCompId());
        compareDataDTO.setBtype(ConstantPool.BTYPE_STOCK);
        List<String> storeNos = Lists.newArrayList();
        storeNos.add(busNo);
        Long storeId = thirdService.queryMdmStoreId(comId, storeNos);
        if (thirdService.isB2cWmsSendStock(storeId)) {
            ThirdPlatformStockDTO thirdPlatformStockDTO = new ThirdPlatformStockDTO();
            thirdPlatformStockDTO.setBusNo(busNo);
            thirdPlatformStockDTO.setCompId(comId);
            thirdPlatformStockDTO.setDetails(Lists.newArrayList());
            thirdPlatformStockDTO.setType(STOCK_UPDATE.getNotifyType());
            if (!CollectionUtils.isEmpty(details)) {
                for (int i = 0; i < details.size(); i++) {
                    ThirdPlatformStockDetailDTO detailDTO = new ThirdPlatformStockDetailDTO();
                    detailDTO.setWareCode(details.get(i).getGoodsNo());
                    detailDTO.setRowNo(i);
                    thirdPlatformStockDTO.getDetails().add(detailDTO);
                }
            }

            //通知发送指定库存数据
            thirdPlatformService.notifySAPSendStockData(thirdPlatformStockDTO);
            return;
        }


        //根据comid分配url
        String dbSource = hdDataService.getDBSource(cmallStockReqDTO.getCompId());
        if (org.apache.commons.lang3.StringUtils.isNotEmpty(dbSource) && BANGJIANDBSTR.equals(dbSource)) {
            compareDataDTO.setRequestUrl(noticeYKSendData + ConstantPool.YK_STOCK_REQUEST_URL);
        } else {
            compareDataDTO.setRequestUrl(noticeHDSendData + ConstantPool.STOCK_REQUEST_URL);
        }
        compareDataDTO.setServiceDesc("库存对比服务");
        sendCompareService.callBackHdCompensate(compareDataDTO);
    }

    @Override
    public List<CompareDataInfo> getCompareDataInfoList(List<String> goodsNoList) {

        List<CompareDataInfo> infoList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(goodsNoList)) {
            for (int i = 0; i < goodsNoList.size(); i++) {
                String goodsNo = goodsNoList.get(i);
                CompareDataInfo info = new CompareDataInfo();
                info.setRowNo(i);
                info.setGoodsNo(goodsNo);
                infoList.add(info);
            }
        }
        return infoList;
    }

    @Override
    public Boolean noticeHdRecorrectStock(RecorrectStockDTO recorrectStockDTO) {
        if (recorrectStockDTO.getBusinessId() == null) {
            throw new BusinessErrorException("连锁id不能为空");
        }
        if (CollectionUtils.isEmpty(recorrectStockDTO.getBusNoList()) && CollectionUtils.isEmpty(recorrectStockDTO.getStoreIdList())) {
            throw new BusinessErrorException("门店id和门店编码不能同时为空");
        }

        if (CollectionUtils.isEmpty(recorrectStockDTO.getGoodsNoList())) {
            throw new BusinessErrorException("商品编码不能为空");
        }
        List<CompareDataInfo> dataInfoList = getCompareDataInfoList(recorrectStockDTO.getGoodsNoList());
        if (CollectionUtils.isEmpty(recorrectStockDTO.getBusNoList())) {
            List<MdmStoreBaseDTO> mdmStoreBaseDTOS = thirdService.findAllStoreByBusinessId(recorrectStockDTO.getBusinessId());
            if (CollectionUtils.isEmpty(mdmStoreBaseDTOS)) {
                throw new BusinessErrorException("连锁id输入错误");
            }
            Map<Long, MdmStoreBaseDTO> mdmStoreMap = mdmStoreBaseDTOS.stream().collect(Collectors.toMap(a -> a.getStoreId(), b -> b, (a, b) -> a));
            List<Long> storeIdList = recorrectStockDTO.getStoreIdList();
            List<String> busNoList = Lists.newArrayList();
            storeIdList.stream().forEach(storeId -> {
                MdmStoreBaseDTO mdmStoreBaseDTO = mdmStoreMap.get(storeId);
                if (mdmStoreBaseDTO == null) {
                    return;
                }
                sendParam2HD(mdmStoreBaseDTO.getComId(), mdmStoreBaseDTO.getStoreNo(), dataInfoList);
                busNoList.add(mdmStoreBaseDTO.getStoreNo());
            });
            recorrectStockDTO.setBusNoList(busNoList);
        } else {
            String comId = thirdService.getComId(recorrectStockDTO.getBusinessId());
            recorrectStockDTO.getBusNoList().stream().forEach(busNo -> {
                sendParam2HD(comId, busNo, dataInfoList);
            });
        }
        //延迟消息发送，更新药剂送库存
        noticeSendStock2ThirdPlatformProducer.send(JSON.toJSONString(recorrectStockDTO));
        return true;
    }

    @Override
    public void sendDiffData2Hd(Long businessId, Long sid) {
        String key = RedisKeysConstant.SEND_DIFF_DATA_2_HD_KEY + sid;
        String s = redissonCacheService.get(key);
        if (StringUtils.isBlank(s)) {
            List<DiffDataCompareInfoWithBLOBs> list = new ArrayList<>();
            DiffDataCompareInfoWithBLOBs diffDataCompareInfoWithBLOB = new DiffDataCompareInfoWithBLOBs();
            diffDataCompareInfoWithBLOB.setBusinessId(businessId);
            diffDataCompareInfoWithBLOB.setStoreId(sid);
            list.add(diffDataCompareInfoWithBLOB);
            send2HD(list);
            redissonCacheService.setStringExpire(key, key, 60 * 60L);
        }

    }


    @Override
    public boolean compareStock(List<CmallStockRequestDTO> stockList) {

        log.info("StockCompareServiceImpl|compareAndNotify|size={}", CollectionUtils.isEmpty(stockList) ? 0 : stockList.size());
        boolean diff = false;
        if (CollectionUtils.isEmpty(stockList)) {
            return diff;
        }
        Map<String, List<CmallStockRequestDTO>> storeStockMap = stockList.stream().collect(Collectors.groupingBy(a -> a.getBusNo()));

        for (String busNo : storeStockMap.keySet()) {
            MdmStoreBaseDTO mdmStore = thirdService.getMdmStoreByStoreNo(busNo);

            if (Objects.isNull(mdmStore) || Objects.isNull(mdmStore.getBusinessId()) || Objects.isNull(mdmStore.getStoreId())) {
                log.info("StockCompareServiceImpl|compareAndNotify|未获取到门店ID|busNo={},storeInfo={}", busNo, mdmStore);
                continue;
            }
            CmallStockRequestDTO cmallStockRequestDTO = storeStockMap.get(busNo).stream().findFirst().get();
            if (StringUtils.isEmpty(cmallStockRequestDTO.getSysType()) ||!SysType.SAPSYS_SYNC.getCode().equals(Integer.valueOf(cmallStockRequestDTO.getSysType()))) {
                continue;
            }
//            if (!thirdService.isB2cWmsSendStock(mdmStore.getStoreId())) {
//                log.info("StockCompareServiceImpl|compareAndNotify|非b2c大仓配送门店|busNo={},storeInfo={}", busNo, mdmStore);
//                continue;
//            }

            log.info("StockCompareServiceImpl|compareAndNotify|门店信息|busNo={},storeInfo={}", busNo, mdmStore);

            List<StockGoodsCountInfo> goodsCountInfoList = getBatchCodeList(mdmStore.getBusinessId(), mdmStore.getStoreId(),
                storeStockMap.get(busNo).stream().map(stock -> StringUtils.trim(stock.getWareCode()))
                    .filter(StringUtils::isNotBlank).distinct().collect(Collectors.toList()));


            if (CollectionUtils.isEmpty(goodsCountInfoList)) {
                log.info("StockCompareServiceImpl|compareAndNotify|中台该门店无库存,需要海典英克推送|businessId={},storeId={}",
                    mdmStore.getBusinessId(), mdmStore.getStoreId());
                diff = true;
                continue;
            }

            CompareContext compareContext = new CompareContext();
            compareContext.setBusinessId(mdmStore.getBusinessId());
            compareContext.setStoreId(mdmStore.getStoreId());
            compareContext.setGoodsCountList(goodsCountInfoList);
            compareContext.setStockRequestList(storeStockMap.get(busNo));

            doCompare(compareContext);

            if (Objects.nonNull(compareContext.isDiff()) && compareContext.isDiff() == true) {
                diff = true;
            }
        }
        return diff;
    }

    /**
     * 对比库存差异
     * @param compareContext
     */
    private void doCompare(CompareContext  compareContext){

        List<CmallStockRequestDTO> stockRequestList = compareContext.getStockRequestList();
        List<StockGoodsCountInfo> goodsCountList = compareContext.getGoodsCountList();

        //物料库存总数(中台)
        Map<String, StockGoodsCountInfo> batchCodeMap = goodsCountList.stream()
            .collect(Collectors.toMap(info -> info.getSkuMerchantCode() + "_" + StringUtils.lowerCase(info.getBatchNo()) + "_" + info.getBatchCode(), info -> info, (v1, v2) -> v2));

        Set<String> executeKey = Sets.newHashSet();


        List<DiffDataCompareInfoWithBLOBs> diffDataCompareInfos = new ArrayList<>();

        //迭代SAP同步库存
        for (CmallStockRequestDTO stockRequest : stockRequestList) {
            String key = stockRequest.getWareCode() + "_" + StringUtils.lowerCase(StringUtils.trim(stockRequest.getMakeNo())) + "_" + stockRequest.getBatchNo();
            if (executeKey.contains(key)) {
                log.info("StockCompareServiceImpl|doCompare|已经对比过此数据|key={},海典库存={}", key, stockRequest);
                continue;
            }

            StockGoodsCountInfo batchCodeInfo = batchCodeMap.get(key);
            executeKey.add(key);

            //库存中心不存在此物料批次库存
            if(Objects.isNull(batchCodeInfo)){

                compareContext.setDiff(true);

                DiffDataCompareInfoWithBLOBs diffDataCompareInfo = new DiffDataCompareInfoWithBLOBs();
                diffDataCompareInfo.setBusinessId(compareContext.getBusinessId());
                diffDataCompareInfo.setStoreId(compareContext.getStoreId());
                diffDataCompareInfo.setReason(CENTER_NO_DATA_REASON);
                fillDiffBasicInfo(diffDataCompareInfo);
                fillSyncStockInfo(diffDataCompareInfo,stockRequest);


                diffDataCompareInfos.add(diffDataCompareInfo);
                continue;
            }

            BigDecimal num = new BigDecimal(stockRequest.getNum()).setScale(3, BigDecimal.ROUND_HALF_UP);
            BigDecimal tnum = new BigDecimal(stockRequest.gettNum()).setScale(3, BigDecimal.ROUND_HALF_UP);

            //批次库存不一致
            if (num.compareTo(batchCodeInfo.getBuyStock()) != 0 ||
                tnum.compareTo(batchCodeInfo.getStock()) != 0) {
                compareContext.setDiff(true);

                DiffDataCompareInfoWithBLOBs diffDataCompareInfo = new DiffDataCompareInfoWithBLOBs();
                diffDataCompareInfo.setReason(DATA_ERROR_REASON);
                diffDataCompareInfo.setBusinessId(compareContext.getBusinessId());
                diffDataCompareInfo.setStoreId(compareContext.getStoreId());
                fillSyncStockInfo(diffDataCompareInfo,stockRequest);
                fillStockGoodsInfo(diffDataCompareInfo,batchCodeInfo);
                fillDiffBasicInfo(diffDataCompareInfo);

                diffDataCompareInfos.add(diffDataCompareInfo);
            }

            batchCodeMap.remove(key);

        }

        //中台有此批次库存　海典无此库存
        if(MapUtils.isNotEmpty(batchCodeMap)){

            for(StockGoodsCountInfo goodsCountInfo:batchCodeMap.values()){
                DiffDataCompareInfoWithBLOBs diffDataCompareInfo = new DiffDataCompareInfoWithBLOBs();

                if (new BigDecimal(0).compareTo(goodsCountInfo.getStock()) == 0
                    && new BigDecimal(0).compareTo(goodsCountInfo.getBuyStock()) == 0) {
                    continue;
                }
                compareContext.setDiff(true);

                fillDiffBasicInfo(diffDataCompareInfo);
                fillStockGoodsInfo(diffDataCompareInfo,goodsCountInfo);
                diffDataCompareInfo.setReason(THIRD_NO_DATA_REASON);
                diffDataCompareInfo.setBusinessId(compareContext.getBusinessId());
                diffDataCompareInfo.setStoreId(compareContext.getStoreId());
                diffDataCompareInfos.add(diffDataCompareInfo);
            }

        }

        //记录存在差异的物料库存信息
        executor.execute(() -> {
            for (DiffDataCompareInfoWithBLOBs diffDataCompareInfo : diffDataCompareInfos) {
                diffDataCompareInfoMapper.insertSelective(diffDataCompareInfo);
            }
        });

    }




    /**
     * 调用库存中心查询物料库存数量
     *
     * @param businessId
     * @param storeId
     * @param goodsNoList
     * @return
     */
    protected List<StockGoodsCountInfo> getBatchCodeList(Long businessId, Long storeId, List<String> goodsNoList) {

        List<StockGoodsCountInfo> goodsCountInfos = Lists.newArrayList();
        if (Objects.isNull(businessId) || Objects.isNull(storeId) || CollectionUtils.isEmpty(goodsNoList)) {
            return goodsCountInfos;
        }

        for (List<String> goodNos : Lists.partition(goodsNoList, PARTITION_SIZE)) {
            StockGoodsQueryParam param = new StockGoodsQueryParam();
            param.setStoreId(storeId);
            param.setBusinessId(businessId);
            param.setSkuMerchantCodes(goodNos);

            log.info("StockCompareServiceImpl|getBatchCodeList|调用库存中台参数|businessId={},storeId={},goodNos={}",
                businessId, storeId, goodNos);

            boolean success = false;
            int i = 0;
            do {
                try {
                    List<StockGoodsCountInfo> goodsCountInfoList = stockCenterFeignService.getCodeList(param).getBody();
                    if (!CollectionUtils.isEmpty(goodsCountInfoList)) {
                        goodsCountInfos.addAll(goodsCountInfoList);
                    }
                    success = true;
                } catch (Exception e) {
                    i++;
                    log.info("StockCompareServiceImpl|getBatchCodeList|调用库存中心查询批次库存异常|times={},param={},errorMsg={}",
                        i, param, e.getMessage());
                    continue;
                }
                if (i > 4) {
                    success = true;
                }
            } while (!success);

        }
        return goodsCountInfos;
    }

    @Override
    public void notifySAPSendStockByStoreNo(String storeNo) {
        MdmStoreBaseDTO mdmStoreBase = thirdService.getMdmStoreByStoreNo(storeNo);
        if (Objects.isNull(mdmStoreBase)) {
            log.info("StockCompareServiceImpl|notifySAPSendStockByStoreNo|门店主数据为空|storeNo={}", storeNo);
            return;
        }
        int page = 1, size = 100;
        long storeId = mdmStoreBase.getStoreId();
        List<StoreGoodsNoDTO> list = null;
        RecorrectStockDTO recorrectStockDTO = new RecorrectStockDTO();
        recorrectStockDTO.setBusinessId(mdmStoreBase.getBusinessId());
        recorrectStockDTO.setComId(mdmStoreBase.getComId());
        recorrectStockDTO.setBusNoList(Collections.singletonList(storeNo));
        recorrectStockDTO.setSyncType(0);
        do {
            ResponseEntity<PageInfo<StoreGoodsNoDTO>> response = stockCenterFeignService.getGoodsNoByStoreId(storeId, page, size);
            if (Objects.nonNull(response) && HttpStatus.OK.equals(response.getStatusCode())) {
                list = response.getBody().getList();
                if (CollectionUtils.isNotEmpty(list)) {
                    recorrectStockDTO.setGoodsNoList(list.stream()
                        .map(StoreGoodsNoDTO::getSkuMerchantCode)
                        .collect(Collectors.toList()));
                    log.info("StockCompareServiceImpl|notifySAPSendStockByStoreNo|通知SAP同步库存信息开始|recorrectStockDTO={}", recorrectStockDTO);
                    noticeHdRecorrectStock(recorrectStockDTO);
                    log.info("StockCompareServiceImpl|notifySAPSendStockByStoreNo|通知SAP同步库存信息成功|recorrectStockDTO={}", recorrectStockDTO);
                }
                page++;
            }
        } while (CollectionUtils.isNotEmpty(list));

    }

}
