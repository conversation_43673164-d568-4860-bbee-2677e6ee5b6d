package com.cowell.bam.service;

import com.codahale.metrics.annotation.Timed;
import org.springframework.cloud.netflix.feign.FeignClient;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;


@FeignClient(name = "oms")
public interface IOmsFeignService {


    @GetMapping(value = {"/api/internal/oms/o2o/config/open/businessId"})
    @Timed
    ResponseEntity<List<Long>> openConfigBusinessIdList();
}
