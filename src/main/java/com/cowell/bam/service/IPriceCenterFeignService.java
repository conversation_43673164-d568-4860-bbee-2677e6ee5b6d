package com.cowell.bam.service;

import com.codahale.metrics.annotation.Timed;
import com.cowell.bam.service.dto.PriceQueryGoodsNoDTO;
import com.cowell.bam.service.dto.PriceQueryParam;
import com.cowell.bam.service.dto.base.CommonResponse;
import org.springframework.cloud.netflix.feign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

/**
 * 类说明
 *
 * @Author: liw
 * @Date: 2020-03-11 15:37
 */
@FeignClient(name = "pricecenter")
public interface IPriceCenterFeignService {

    /**
     * 根据商品编码列表 获取价格
     * @param param
     * @return
     */
    @PostMapping("/api/internal/price/priceStoreDetail/getPriceByStoreIdAndGoodsNoList")
    @Timed
    CommonResponse<List<PriceQueryGoodsNoDTO>> getPriceByStoreIdAndRelateNo(@RequestBody PriceQueryParam param);

    /**
     * 根据商品编码列表 获取价格
     * @param param
     * @return
     */
    @PostMapping("/api/internal/price/priceStoreDetail/getPriceAndMemberPrice")
    @Timed
    CommonResponse<List<PriceQueryGoodsNoDTO>> getPriceAndMemberPrice(@RequestBody PriceQueryParam param);


    /**
     * 分页获取价格
     * @param param
     * @return
     */
    @PostMapping("/api/internal/price/priceStoreDetail/getAllPOSPricePage")
    @Timed
    CommonResponse<List<PriceQueryGoodsNoDTO>> getAllPOSPricePage(@RequestBody PriceQueryParam param);


    @PostMapping("/api/internal/price/priceStoreDetail/getPriceByStoreIdAndGoodsNoList")
    @Timed
    List<PriceQueryGoodsNoDTO> getPriceByStoreIdAndRelateNoLists(@RequestBody PriceQueryParam param);

}
