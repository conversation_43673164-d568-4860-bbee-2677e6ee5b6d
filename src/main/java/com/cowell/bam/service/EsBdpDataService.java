package com.cowell.bam.service;

import com.cowell.bam.domain.BamLogDO;

import java.util.List;
import java.util.concurrent.Future;

/**
 * <AUTHOR>
 * @date: 2018/12/28 16:52
 * @description:
 */
public interface EsBdpDataService {
    /**
     * 存储数据
     * @param bamLogDO
     * @return
     */
    String save(BamLogDO bamLogDO);

    /**
     * 批量插入es
     * @param bamLogDOList
     * @return
     */
    Future<Boolean> batchSave(List<BamLogDO> bamLogDOList);
}
