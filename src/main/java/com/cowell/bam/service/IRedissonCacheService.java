package com.cowell.bam.service;


import org.redisson.api.RLock;

import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * redis 缓存服务接口
 */
public interface IRedissonCacheService {

    /**
     * set操作
     *
     * @param key
     * @param obj
     * @return
     */
    boolean set(String key, Object obj);

    boolean setString(String key, String value);

    boolean setStringExpire(String key, String value, Long seconds);

    /**
     * set操作 有效时间
     *
     * @param key
     * @param obj
     * @return
     */
    boolean setNx(String key, Object obj, long expire);

    boolean setNxString(String key, Object obj, long expire);

    /**
     * 根据key获取对象实体
     *
     * @param key
     * @return
     */
    String get(String key);

    /**
     * 根据key删除
     *
     * @param key
     * @return
     */
    boolean deleteByKey(String... key);

    /**
     * 根据正则key删除
     *
     * @param pattern
     * @return
     */
    boolean deleteByPattern(String pattern);


    /**
     * 根据key 和 泛型获取List
     *
     * @param key
     * @param t
     * @return
     */
    <T> List<T> getList(String key, Class<T> t);

    List<String> getListString(String key);

    /**
     * 根据key和list 放入缓存
     *
     * @param key
     * @param list
     * @return
     */
    <T> boolean setList(String key, List<T> list);


    /**
     * 根据key枷锁
     *
     * @param key
     * @return
     */
    RLock getLock(String key);


    /*=======================================================================================================*/

    /**
     * @param key
     * @param retryTimes 重试次数
     * @return
     */
    RLock getLock(String key, int retryTimes);

    /**
     * @param key
     * @param retryTimes  重试次数
     * @param sleepMillis 时间间隔
     * @return
     */
    RLock getLock(String key, int retryTimes, long sleepMillis);

    /**
     * @param key
     * @param expire 到期时间
     * @return
     */
    RLock getLock(String key, long expire);

    /**
     * @param key
     * @param expire     到期时间
     * @param retryTimes 重试次数
     * @return
     */
    RLock getLock(String key, long expire, int retryTimes);

    /**
     * @param key
     * @param expire      持锁时间,单位毫秒
     * @param retryTimes  重试次数
     * @param sleepMillis 重试的间隔时间
     * @return
     */
    RLock getLock(String key, long expire, int retryTimes, long sleepMillis);

    /*=======================================================================================================*/



    /**
     * setMap
     *
     * @param key
     * @param paramMap
     */
    <T> void setMap(String key, Map<String, T> paramMap);

    /**
     * 获取Map
     *
     * @param key
     * @return
     */
    <T> Map<String, T> getMap(String key, Class<T> t);


    /**
     * 自增key后获取
     *
     * @param key
     * @return
     */
    Long incr(String key);

    /**
     * 获取后自增
     *
     * @param key
     * @return
     */
    Long getAndIncr(String key);

    /**
     * 通过类型获取key的value
     *
     * @param key
     * @param t
     * @param <T>
     * @return
     */
    <T> T typeGet(String key, Class<T> t);

    /**
     * 向set里新增
     *
     * @param key
     * @param value
     * @return
     */
    boolean sadd(String key, String value);

    /**
     * 获取set
     *
     * @param key
     * @return
     */
    Set<String> getSet(String key);

    /**
     * spop
     *
     * @param key
     * @return
     */
    String spop(String key);

    /**
     * scard
     *
     * @param key
     * @return
     */
    int scard(String key);

    /**
     * lpush list
     *
     * @param key
     * @param value
     */
    void lpush(String key, String value);

    /**
     * rpop list
     *
     * @param key
     * @return
     */
    String rpop(String key);

    /**
     * 自增并设置过期时间
     *
     * @param key
     * @param time
     * @return
     */
    Long incrAndExpire(String key, long time);


    void unLock(RLock lock);

    /**
     * 版本号
     * @param dataType
     * @return
     */
    Integer versionIncr(Integer dataType, String date);
}
