package com.cowell.bam.service;

import com.cowell.bam.domain.DiffDataCompareInfoWithBLOBs;
import com.cowell.bam.entity.ItemPrice;
import com.cowell.bam.service.dto.ItemPriceResponse;

import java.util.List;

/**
 * <AUTHOR>
 * @date: 2018/12/29 11:29
 * @description:  对比service 父类
 */

public interface PriceCompareService {

    /**
     * 对比
     * @param
     * @return
     */
    boolean compare(String comId);


    void send2HD(List<DiffDataCompareInfoWithBLOBs> diffDataCompareInfos);

    /**
     * 对比价格 异常信息
     * @param storeItemPriceList
     * @param itemList
     * @param businessId
     * @param storeId
     * @param version
     */
    void checkStoreItemPrice(List<ItemPrice> storeItemPriceList, List<ItemPriceResponse> itemList, Long businessId, Long storeId, Integer version);
}
