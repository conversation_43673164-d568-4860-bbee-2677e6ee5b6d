package com.cowell.bam.service;

import com.cowell.bam.entity.GoodsCounterDO;
import com.cowell.bam.entity.ItemPrice;
import com.cowell.bam.entity.Stock;
import com.cowell.bam.entity.WareDxDateVO;
import com.cowell.bam.service.dto.HdOrderDTO;
import com.cowell.bam.service.dto.MdmDataTransformDTO;
import com.cowell.bam.service.dto.PosFundDailyDTO;
import com.cowell.bam.service.dto.PosFundDetailDTO;

import com.cowell.bam.web.rest.vo.ApiParamVo;
import com.cowell.bam.web.rest.vo.HdDxParamVO;
import org.springframework.jdbc.core.JdbcTemplate;

import java.util.List;

/**
 * <AUTHOR>
 * @ProjectName bam
 * @Description:
 * @date 2019/10/25 11:51
 */
public interface HdDataService {
    Integer queryHdData(String comid);

    List<Stock> queryHdTransitStockData(String comId, String storeNo);

    List<Stock> queryHdStockData(String comId, String storeNo);

    List<Stock> queryHdStockGoodsData(String comId, String storeNo);

    List<Stock> queryBjStockData(String comId, String storeNo);

    String getDBSource(String comId);

    List<Stock> queryBjStockData(Long businessId, Long storeId, List<String> goodsNos);

    List<Stock> queryHdStockData(Long businessId, Long storeId, List<String> goodsNos);

    List<ItemPrice> queryPriceByComIdAndBusNo(String comId, String busNo);

    List<ItemPrice> queryPriceGoodsNoByComIdAndBusNo(String comId, String busNo);

    List<ItemPrice> queryPriceByComIdAndBusNoAndDate(String comId, String busNo,String queryDate);

    List<ItemPrice> queryPriceByBusinessIdAndStoreIdAndGoods(Long businessId, Long storeId,List<String> goodsNos);

    List<ItemPrice> queryPriceByComIdAndBusNoAndGoods(String comId, String busNo,List<String> goodsNos);

    List<ItemPrice> queryPriceCount(Long businessId, Long storeId,String comId,String busNo);

    List<ItemPrice> queryPriceCountByData(Long businessId, Long storeId,String comId,String busNo,String queryDate);

    List<HdOrderDTO> queryHdOrderInfo(String orderCreateTime, String orderEndTime,String dataSource, Integer pageNumber,Integer pageSize);

    List<HdOrderDTO> queryHdOrderInfo(String orderCreateTime, String orderEndTime,String dataSource, Integer status, Integer pageNumber,Integer pageSize);

    List<HdOrderDTO> queryHdOrderViewByScrmUserIdAndcashnoAndScrmCompid(Long scrmUserid,String cashno,String scrmCompid);

    List<PosFundDailyDTO> queryFundAccount(String compId,String fundDate,Integer pageNo,Integer pageSize);

    List<PosFundDetailDTO> queryFundDetails(String compId,String fundDate,String erpCode);

    String getOracleTime(String compId,String fundDate,String erpCode);

    /**
     * 获取英克门店商品数量
     * @param comId
     * @param storeNo
     * @return
     */
    Integer queryYKStoreCount(String comId,String storeNo);

    /**
     * 查询英克门店下所有商品
     * @param comId
     * @param storeNo
     * @return
     */
    List<Stock> queryAllBjStockData(String comId, String storeNo);
    List<Stock> queryAllBjStockGoodsData(String comId, String storeNo);

    /**
     * 查询英克门店下所有商品
     * @param comId
     * @param storeNo
     * @param goodsNos
     * @return
     */
    List<Stock> queryBjStockData(String comId, String storeNo, List<String> goodsNos);

    /**
     * 获取海典物料数据信息
     * @param comId
     * @param busNo
     * @param goodsNos
     * @return
     */
    List<Stock> queryHdStockInfo(String comId, String busNo, List<String> goodsNos);

    /**
     * 获取物料数据信息
     * @param comId
     * @param busNo
     * @param goodsNos
     * @return
     */
    List<Stock> queryPosStockInfo(String comId, String busNo, List<String> goodsNos);

    /**
     * 查询货位信息
     * @return
     */
    List<GoodsCounterDO> queryGoodsCounterInfo(ApiParamVo apiParamVo);

     MdmDataTransformDTO getMdmStoreInfo(Long businessId, Long storeId);

     JdbcTemplate getJdbcTemplate(String comid);

     JdbcTemplate getHdJdbcTemplate(String dataSource);

        /**
         * 查询动销海典数据
         */
    List<WareDxDateVO> getDXHdItemInfo(Long businessId, Long storeId, String lastDxdate, String startDxDate, List<String> goodsNos);

    List<HdOrderDTO> queryFundViewByUserIdAndBusinessId(Long scrmUserid, Long businessId, Integer scrmCount);

    /**
     * 功    能：销售和退货库存变动
     * 作    者：郜玉皓
     * 时    间：2023-06-06
     */
    List<WareDxDateVO> getHdDxGoodsList(HdDxParamVO param);

    /**
     * 功    能：销售和退货库存变动总条数
     * 作    者：郜玉皓
     * 时    间：2023-06-08
     */
    Integer getHdDxGoodsListCount(HdDxParamVO param);


}
