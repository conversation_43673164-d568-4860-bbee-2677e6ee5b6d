package com.cowell.bam.service;

import com.cowell.bam.domain.DiffDataCompareInfoWithBLOBs;
import com.cowell.bam.mq.message.CompareStockDTO;
import com.cowell.bam.service.dto.CmallStockRequestDTO;
import com.cowell.bam.service.dto.CompareDataInfo;
import com.cowell.bam.service.dto.RecorrectStockDTO;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date: 2018/12/29 11:29
 * @description:  对比service 父类
 */

public interface StockCompareService {

    /**
     * 对比
     * @param
     * @return
     */
    boolean compare(String comId);

    boolean distributeCompare(String comId);

    /**
     * 对比
     * @param
     * @return
     */
    boolean comparePos(String comId);

    /**
     * 对比某一个门店下的货品库存
     * @param comId
     * @param storeNo
     * @return
     */
    boolean comparePosStock(String comId, String storeNo);

    void send2HD(List<DiffDataCompareInfoWithBLOBs> diffDataCompareInfos);

    void sendParam2HD(String comId, String busNo, List<CompareDataInfo> details);

    List<CompareDataInfo> getCompareDataInfoList(List<String> goodsNoList);

    Boolean noticeHdRecorrectStock(RecorrectStockDTO recorrectStockDTO);

    void sendDiffData2Hd(@RequestParam("businessId") Long businessId, Long sid);

    /**
     * 对比某一个门店下的货品库存
     * @param comId
     * @param storeNo
     * @return
     */
    boolean compareStock(String comId, String storeNo);

    boolean compareStock(CompareStockDTO compareStockDTO);

    boolean compareStockByGoods(String comId, String storeNo);

    /**
     * 获取连锁下各个门店商品总数
     * @param comId
     */
    Map<String,Integer> queryYKStoreCount(String comId);


    /**
     * 对比库存以及发送更新库存指令
     * @param stockRequestDTOS
     */
    boolean compareStock(List<CmallStockRequestDTO> stockRequestDTOS);

    void notifySAPSendStockByStoreNo(String storeNo);

}
