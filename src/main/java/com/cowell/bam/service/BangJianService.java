package com.cowell.bam.service;

import com.cowell.bam.service.dto.BangJianViewDTO;
import com.cowell.bam.service.dto.HdOrderDTO;

import java.util.List;

public interface BangJianService {
    /**
     * 查询邦健视图
     *
     * @return
     */
    List<BangJianViewDTO> queryBangjianInfo(Long businessId, String startTime, String endTime, Integer pageNumber, Integer pageSize);

    /**
     * 查询邦健单条数据
     *
     * @return
     */
    BangJianViewDTO queryOneByBusinessIdAndCode(Long businessId, String code);

    /**
     * 查询邦健积分订单视图，校验积分状态
     * @param userId
     * @param bdid
     * @param compId
     * @return
     */
    List<HdOrderDTO> queryBjOrderForCheckFund(Long userId,String bdid, String compId);

    /**
     * 分页查询邦健积分订单视图，校验积分状态
     * @param orderCreateTime
     * @param orderEndTime
     * @param dataSource
     * @param pageNumber
     * @param pageSize
     * @return
     */
    List<HdOrderDTO> queryPageBjOrderForCheckFund(String orderCreateTime, String orderEndTime ,String dataSource,Integer pageNumber,Integer pageSize);

}
