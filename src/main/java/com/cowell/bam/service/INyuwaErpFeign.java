package com.cowell.bam.service;

import com.codahale.metrics.annotation.Timed;
import com.cowell.bam.service.dto.StoreApplyDateDTO;
import com.cowell.bam.service.dto.base.AmisCommonResponse;
import com.cowell.bam.service.dto.base.AmisListResponse;
import org.springframework.cloud.netflix.feign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestParam;

/**
 * <AUTHOR>
 * @date 2023/1/9 16:19
 */
@FeignClient(name = "nyuwa-erp")
public interface INyuwaErpFeign {

    @GetMapping("/api/intranet/mdd/218/store_apply_date/list")
    @Timed
    AmisCommonResponse<AmisListResponse<StoreApplyDateDTO>> queryStoreApplyDateResult(@RequestParam("apply_date") String applyDate, @RequestParam("columns") String columns);
}
