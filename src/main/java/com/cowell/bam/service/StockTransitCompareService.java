package com.cowell.bam.service;

import com.cowell.bam.service.dto.DiffDataRequestDTO;
import com.cowell.bam.service.dto.EmailTransitStockDTO;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/11/1 17:00
 */
public interface StockTransitCompareService {

    /**
     * 对比在途库存
     * @param
     * @return
     */
    boolean compareTransit(String comId, Integer version, String date);

    /**
     * 按照门店比对
     * @param comId
     * @param storeNo
     * @return
     */
    boolean compareTransitByStoreNo(String comId, String storeNo);

    /**
     * 按照连锁比对
     * @param comId
     */
    void compareTransitByCom(String comId);

    /**
     * 发送差异
     */
    void sendDifferEmailAndFile(DiffDataRequestDTO diffDataRequestDTO, String date, String toUsers, List<Long> businessIdList);

    /**
     * 按照连锁发送邮件
     * @param date
     * @param toUsers
     */
    void sendDifferEmmailByCom(DiffDataRequestDTO diffDataRequestDTO, String date, String toUsers);

    /**
     * 批量发邮件
     * @param messageDTO
     */
    void sendBatchDifferEmmailByCom(EmailTransitStockDTO messageDTO);

    /**
     * 发送在途给海典
     * @param businessId
     * @param sid
     * @param transitStock
     */
    void sendDiffData2Hd(Long businessId, Long sid, Integer transitStock);
}
