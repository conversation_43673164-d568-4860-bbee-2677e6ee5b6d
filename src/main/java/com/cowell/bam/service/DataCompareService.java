package com.cowell.bam.service;

import com.cowell.bam.service.dto.*;
import com.github.pagehelper.PageInfo;
import org.springframework.data.domain.Pageable;

import java.util.List;

/**
 * <AUTHOR>
 * @ProjectName bam
 * @Description:  数据比对
 * @date 2019/10/08 15:18
 */
public interface DataCompareService {


    PageInfo<DiffDataResponseDTO> diffCompareList(Pageable pageable, DiffDataRequestDTO diffDataRequestDTO);

    PageInfo<DiffCollectDataResponseDTO> diffCollect(Pageable pageable, DiffDataRequestDTO diffDataRequestDTO);

    List<DiffCollectDataResponseDTO> getDiffCollectList(DiffDataRequestDTO diffDataRequestDTO);

    List<Long> getTransitDiffBusiness(DiffDataRequestDTO diffDataRequestDTO);

    List<DiffTransitDataResponseDTO> geTransittDiffCollectList(DiffDataRequestDTO diffDataRequestDTO);

    List<DiffDataCompareInfoDTO> getDiffDetailList(DiffDataRequestDTO diffDataRequestDTO);

    StockGoodsBaseDTO diffCompareDetail(Long id);

    boolean acceptBdpData(Integer type,String comId);

    Integer deleteDiffData(Long businessId,Integer type,String time);

    Integer batchDeleteDiffData(String businessIds,Integer type,String time);

}
