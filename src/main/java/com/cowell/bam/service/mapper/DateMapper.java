package com.cowell.bam.service.mapper;

import com.cowell.bam.web.rest.util.DateUtil;
import org.mapstruct.Mapper;

import java.text.SimpleDateFormat;
import java.time.Instant;
import java.time.ZonedDateTime;
import java.util.Date;

@Mapper(componentModel = "spring", uses = {})
public class DateMapper {

    final static SimpleDateFormat format =  new SimpleDateFormat( "yyyy-MM-dd hh:mm:ss" );
    public String asString(ZonedDateTime date) {

        return DateUtil.getStringByZonedDateTime(date);
    }

    public ZonedDateTime asDate(String date) {
        try {
            return DateUtil.getZonedDateTimeByString(date);
        }
        catch ( Exception e ) {
            throw new RuntimeException( e );
        }
    }

    public String instantToString(Instant instant){
        return DateUtil.conventDateStrByDate(instant);
    }

    public Instant stringToInstant(String instant){
        return DateUtil.stringToInstant(instant);
    }

    public String dateToString(Date date){
        return DateUtil.getStringByDate(date);
    }
    public Date stringToDate(String date){
        return DateUtil.getDateByString(date);
    }
}
