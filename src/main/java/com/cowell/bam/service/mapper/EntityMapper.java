package com.cowell.bam.service.mapper;

import com.github.pagehelper.PageInfo;

import java.util.List;
import java.util.Map;

/**
 * Contract for a generic dto to entity mapper.
 *
 * @param <D> - DTO type parameter.
 * @param <E> - Entity type parameter.
 */

public interface EntityMapper<D, E> {

    E toEntity(D dto);

    D toDto(E entity);

    List <E> toEntity(List<D> dtoList);

    List <D> toDto(List<E> entityList);

    PageInfo<E> toEntity(PageInfo<D> dtoPage);
    PageInfo<D> toDto(PageInfo<E> entityPage);

    Map<String,E> toEntity(Map<String, D> dtoMap);
    Map<String,D> toDto(Map<String, E> entityMap);

}
