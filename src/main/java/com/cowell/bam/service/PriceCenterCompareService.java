package com.cowell.bam.service;

import com.cowell.bam.entity.ItemPrice;
import com.cowell.bam.enums.PriceComparisonEnum;
import com.cowell.bam.enums.SyncTypeEnum;
import com.cowell.bam.mq.message.CompareOneselfMessage;
import com.cowell.bam.service.dto.ItemPriceResponse;
import com.cowell.bam.service.dto.MdmStoreBaseDTO;
import com.cowell.bam.service.dto.PricePushDTO;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

/**
 * 类说明 价格中心对比 接口
 *
 * @Author: liw
 * @Date: 2020-05-31 00:48
 */
public interface PriceCenterCompareService {

    /**
     * 通过 商品编码 进行 价格对比
     * @param businessId 连锁编码
     * @param storeId 门店编码
     * @param goodsNos 商品编码
     */
    void priceCenterCompareFromGoods(Long businessId, Long storeId, List<String> goodsNos);

    /**
     * 通过 商品编码 进行 价格对比
     * @param comId 连锁编码
     * @param busNo 门店编码
     * @param goodsNos 商品编码
     */
    void priceCenterCompareFromGoods(String comId, String busNo, List<String> goodsNos);
    /**
     * 通过 门店编码 进行 价格对比
     * @param comId 连锁编码
     * @param busNo 门店编码
     */
    void priceCenterCompareFromBusNo(String comId,String busNo);
    /**
     * 通过 门店编码 进行 价格对比
     * @param comId 连锁编码
     * @param busNo 门店编码
     */
    void priceCenterCompareFromBusNoByDate(String comId,String busNo,String queryDate);
    /**
     * 通过 连锁编码 进行 价格对比
     * @param comId 连锁编码
     */
    void priceCenterCompareFromComId(String comId);

    /**
     * 通过 连锁编码 进行 中台价格与POS价格对比
     * @param comId 连锁编码
     */
    void priceCenterComparePosFromComId(String comId);

    /**
     * 根据连锁编码导出差异数据
     * @param comId 连锁编码
     */
    void exportByComId(String comId);

    /**
     * 根据连锁编码导出差异数据
     * @param comId 连锁编码
     */
    void exportByComIdDistribute(String comId, boolean priceRepair);

    void doCompareStorePrice(MdmStoreBaseDTO storeBaseDTO, boolean priceRepair);

    void exportCompareDataByVersion(String comId, Long businessId, Integer version, SyncTypeEnum syncTypeEnum);

    /**
     * 对比价格
     * @param storeItemPriceList
     * @param itemList
     * @param businessId
     * @param storeId
     */
    void doComparePrice(List<ItemPrice> storeItemPriceList, List<ItemPriceResponse> itemList, Long businessId, Long storeId);

    /**
     * 对比价格
     * @param storeItemPriceList
     * @param itemList
     * @param businessId
     * @param storeId
     * @param pos
     */
    void doComparePrice(List<ItemPrice> storeItemPriceList, List<ItemPriceResponse> itemList, Long businessId, Long storeId, boolean pos);

    void doComparePrice(List<ItemPrice> storeItemPriceList, List<ItemPriceResponse> itemList, Long businessId, Long storeId, boolean pos, List<PricePushDTO> needRetweetPosItemList);

    /**
     * 通知价格中台
     * @param storeItemPriceList
     * @param needRetweetPosItemList
     * @param pos
     */
    void noticePriceCenter(List<ItemPrice> storeItemPriceList, List<PricePushDTO> needRetweetPosItemList, boolean pos);

    /**
     * 获取开通的连锁ID
     * @return
     */
    List<String> openConfigBusinessIdList();

    void priceCenterFromComId(String comId,String busNo);

    void priceCenterFromBusNo(String comId, String busNo);

    void posFromBusNo(CompareOneselfMessage message);

    void noticePriceCenterTwo(List<ItemPrice> storeItemPriceList);

    void comparePriceByBusinessIdAndStoreIdAndGoodsNo(Long businessId, Long storeId, String goodsNos);

    /**
     * 根据门店和连锁对比价格特价标记
     * @param comId 连锁编码
     * @param busNo 门店编码
     */
    void comparePriceSpecialFlag(String comId, String busNo);

    /**
     * 根据门店、连锁、商品对比价格特价标记
     * @param comId 连锁编码
     * @param busNo 门店编码
     * @param goodsNos 商品编码
     */
    void comparePriceSpecialFlag(String comId, String busNo, List<String> goodsNos);

    /**
     * 根据连锁对比价格特价标记
     * @param comId 连锁编码
     */
    void comparePriceSpecialFlag(String comId);

}
