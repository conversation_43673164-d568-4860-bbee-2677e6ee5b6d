package com.cowell.bam.service.dto;

import io.swagger.annotations.ApiModelProperty;

import java.math.BigDecimal;

public class StockDataInfoDTO extends StockGoodsBaseDTO{


    @ApiModelProperty(name = "skuMerchantCode", value = "商品编号")
    private String skuMerchantCode;
    //批号拥有字段
    @ApiModelProperty(name = "batchNo", value = "生成批号")
    private String batchNo;
    @ApiModelProperty(name = "batchCode", value = "批次")
    private String batchCode;
    @ApiModelProperty(name = "stock", value = "总库存")
    private BigDecimal stock;
    @ApiModelProperty(name = "buyStock", value = "可卖库存")
    private BigDecimal buyStock;
    @ApiModelProperty(name = "transitStock", value = "在途库存")
    private BigDecimal transitStock;
    /**
     * 门店名称
     */
    private String storeName;

    public String getStoreName() {
        return storeName;
    }

    public void setStoreName(String storeName) {
        this.storeName = storeName;
    }

    public BigDecimal getTransitStock() {
        return transitStock;
    }

    public void setTransitStock(BigDecimal transitStock) {
        this.transitStock = transitStock;
    }

    public String getSkuMerchantCode() {
        return skuMerchantCode;
    }

    public void setSkuMerchantCode(String skuMerchantCode) {
        this.skuMerchantCode = skuMerchantCode;
    }

    public String getBatchNo() {
        return batchNo;
    }

    public void setBatchNo(String batchNo) {
        this.batchNo = batchNo;
    }

    public String getBatchCode() {
        return batchCode;
    }

    public void setBatchCode(String batchCode) {
        this.batchCode = batchCode;
    }

    public BigDecimal getStock() {
        return stock;
    }

    public void setStock(BigDecimal stock) {
        this.stock = stock;
    }

    public BigDecimal getBuyStock() {
        return buyStock;
    }

    public void setBuyStock(BigDecimal buyStock) {
        this.buyStock = buyStock;
    }

    @Override
    public String toString() {
        return "StockDataInfoDTO{" +
            "skuMerchantCode='" + skuMerchantCode + '\'' +
            ", batchNo='" + batchNo + '\'' +
            ", batchCode='" + batchCode + '\'' +
            ", stock=" + stock +
            ", buyStock=" + buyStock +
            '}';
    }
}
