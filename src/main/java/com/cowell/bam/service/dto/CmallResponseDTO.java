package com.cowell.bam.service.dto;

import com.alibaba.fastjson.JSONObject;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;

public class CmallResponseDTO implements Serializable {

    @ApiModelProperty(name = "业务双方确定的业务数据JSON格式")
    JSONObject bdata;

    public JSONObject getBdata() {
        return bdata;
    }

    public void setBdata(JSONObject bdata) {
        this.bdata = bdata;
    }

    @Override
    public String toString() {
        return "CmallResponseDTO{" +
            "bdata=" + bdata +
            '}';
    }
}
