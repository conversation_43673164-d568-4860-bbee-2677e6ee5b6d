package com.cowell.bam.service.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import org.apache.commons.lang3.builder.ToStringBuilder;

import java.util.Date;

/**
 * <AUTHOR>
 * @ProjectName bam
 * @Description:
 * @date 2019/10/08 15:21
 */
public class PriceDiffDataDetailResponseDTO extends StockGoodsBaseDTO{

    private String goodsNo;

    @ApiModelProperty(name = "海典零售价")
    String hdPrice;
    @ApiModelProperty(name = "高济零售价")
    String price;

    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss" ,timezone = "GMT+8")
    private Date gmtCreate;

    public String getGoodsNo() {
        return goodsNo;
    }

    public void setGoodsNo(String goodsNo) {
        this.goodsNo = goodsNo;
    }

    public String getPrice() {
        return price;
    }

    public void setPrice(String price) {
        this.price = price;
    }

    public String getHdPrice() {
        return hdPrice;
    }

    public void setHdPrice(String hdPrice) {
        this.hdPrice = hdPrice;
    }

    public Date getGmtCreate() {
        return gmtCreate;
    }

    public void setGmtCreate(Date gmtCreate) {
        this.gmtCreate = gmtCreate;
    }

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this);
    }
}
