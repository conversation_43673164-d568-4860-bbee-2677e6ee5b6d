package com.cowell.bam.service.dto;

import com.alibaba.fastjson.JSONArray;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;

public class CmallRequestDTO implements Serializable {
    @ApiModelProperty(name = "唯一ID")
    String bguid;
    @ApiModelProperty(name = "枚举全部类型分类")
    int btype;
    @ApiModelProperty(name = "源系统")
    int bsource;
    @ApiModelProperty(name = "目标系统")
    int bdestination;
    @ApiModelProperty(name = "消息发送时间格式：YYYY-MM-DD HH:mm:ss")
    String bdatetime;
    @ApiModelProperty(name = "消息处理状态")
    int bstatus;
    @ApiModelProperty(name = "回调地址")
    String bcallback;
    @ApiModelProperty(name = "版本号")
    String bversion;
    @ApiModelProperty(name = "hash值")
    String bdatahash;
    @ApiModelProperty(name = "业务查询使用的关键字段 JSON格式")
    JSONArray bkeys;
    @ApiModelProperty(name = "业务双方确定的业务数据JSON格式")
    JSONArray bdata;

    public String getBguid() {
        return bguid;
    }

    public void setBguid(String bguid) {
        this.bguid = bguid;
    }

    public int getBtype() {
        return btype;
    }

    public void setBtype(int btype) {
        this.btype = btype;
    }

    public int getBsource() {
        return bsource;
    }

    public void setBsource(int bsource) {
        this.bsource = bsource;
    }

    public int getBdestination() {
        return bdestination;
    }

    public void setBdestination(int bdestination) {
        this.bdestination = bdestination;
    }

    public String getBdatetime() {
        return bdatetime;
    }

    public void setBdatetime(String bdatetime) {
        this.bdatetime = bdatetime;
    }

    public int getBstatus() {
        return bstatus;
    }

    public void setBstatus(int bstatus) {
        this.bstatus = bstatus;
    }

    public String getBcallback() {
        return bcallback;
    }

    public void setBcallback(String bcallback) {
        this.bcallback = bcallback;
    }

    public String getBversion() {
        return bversion;
    }

    public void setBversion(String bversion) {
        this.bversion = bversion;
    }

    public JSONArray getBkeys() {
        return bkeys;
    }

    public void setBkeys(JSONArray bkeys) {
        this.bkeys = bkeys;
    }

    public JSONArray getBdata() {
        return bdata;
    }

    public void setBdata(JSONArray bdata) {
        this.bdata = bdata;
    }

    public String getBdatahash() {
        return bdatahash;
    }

    public void setBdatahash(String bdatahash) {
        this.bdatahash = bdatahash;
    }

    @Override
    public String toString() {
        return "CmallRequestDTO{" +
            "bguid='" + bguid + '\'' +
            ", btype=" + btype +
            ", bsource=" + bsource +
            ", bdestination=" + bdestination +
            ", bdatetime='" + bdatetime + '\'' +
            ", bstatus=" + bstatus +
            ", bcallback='" + bcallback + '\'' +
            ", bversion='" + bversion + '\'' +
            ", bdatahash='" + bdatahash + '\'' +
            ", bkeys=" + bkeys +
            ", bdata=" + bdata +
            '}';
    }
}
