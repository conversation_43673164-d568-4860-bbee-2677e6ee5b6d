package com.cowell.bam.service.dto;

import io.swagger.annotations.ApiModelProperty;


public class ThridPriceInfoDTO {
    @ApiModelProperty(value = "商品编码")
    private String skuMerchantCode;

    @ApiModelProperty(name = "价格")
    private String price;

    private String hyprice;

    private String type;


    public String getSkuMerchantCode() {
        return skuMerchantCode;
    }

    public void setSkuMerchantCode(String skuMerchantCode) {
        this.skuMerchantCode = skuMerchantCode;
    }

    public String getPrice() {
        return price;
    }

    public void setPrice(String price) {
        this.price = price;
    }

    public String getHyprice() {
        return hyprice;
    }

    public void setHyprice(String hyprice) {
        this.hyprice = hyprice;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    @Override
    public String toString() {
        return "ThridPriceInfoDTO{" +
            "skuMerchantCode='" + skuMerchantCode + '\'' +
            ", price='" + price + '\'' +
            ", hyprice='" + hyprice + '\'' +
            ", type='" + type + '\'' +
            '}';
    }
}
