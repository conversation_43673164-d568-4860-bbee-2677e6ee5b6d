package com.cowell.bam.service.dto.base;
import java.io.Serializable;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.locks.ReentrantReadWriteLock;

public abstract class SuperDTO implements Cloneable, Serializable
{
  private static final long serialVersionUID = 1386231098909087720L;
  private static transient Map<Class, String[]> map = new HashMap();
  private static transient ReentrantReadWriteLock rwl = new ReentrantReadWriteLock();
@Override
  public Object clone()
  {
    SuperDTO vo = null;
    try {
      vo = (SuperDTO)getClass().newInstance();
    }
    catch (Exception e) {
    }
    String[] fieldNames = getAttributeNames();
    if (fieldNames != null) {
      for (int i = 0; i < fieldNames.length; ) {
        try {
          vo.setAttributeValue(fieldNames[i], getAttributeValue(fieldNames[i]));
        }
        catch (Exception ex)
        {
        }
        ++i;
      }

    }

    return vo;
  }

  public boolean equalsContent(Object obj)
  {
    if (obj == this) {
        return true;
    }
    if (obj == null) {
        return false;
    }
    if (obj.getClass() != getClass()) {
        return false;
    }
    return equalsContent((SuperDTO)obj, getAttributeNames());
  }

  public boolean equalsContent(SuperDTO vo, String[] fieldnames)
  {
    if ((fieldnames == null) || (vo == null)) {
        return false;
    }

    String[] arr$ = fieldnames; int len$ = arr$.length; for (int i$ = 0; i$ < len$; ++i$) { String field = arr$[i$];
      if (!(isAttributeEquals(getAttributeValue(field), vo.getAttributeValue(field))))
      {
        return false; }
    }
    return true;
  }

  public String[] getAttributeNames()
  {
    rwl.readLock().lock();
    try {
      String[] arrayOfString = getAttributeAry();

      return arrayOfString;
    }
    finally
    {
      rwl.readLock().unlock();
    }
  }

  private String[] getAttributeAry() {
    String[] arys = (String[])map.get(getClass());
    if (arys == null) {
      rwl.readLock().unlock();
      rwl.writeLock().lock();
      try {
        arys = (String[])map.get(getClass());
        if (arys == null)
        {
          Set set = new HashSet();
          String[] strAry = BeanHelper.getInstance().getPropertiesAry(this);

          String[] arr$ = strAry; int len$ = arr$.length; for (int i$ = 0; i$ < len$; ++i$) { String str = arr$[i$];
           if ((!(str.equals("status"))) && (!(str.equals("dirty"))))
            {
              set.add(str); }
          }
          arys = (String[])set.toArray(new String[set.size()]);
          map.put(getClass(), arys);
        }
      } finally {
        rwl.readLock().lock();
        rwl.writeLock().unlock();
      }
    }
    return arys;
  }

  public Object getAttributeValue(String attributeName)
  {
    if ((attributeName == null) || (attributeName.length() == 0)) {
        return null;
    }
    return BeanHelper.getProperty(this, attributeName.toUpperCase());
  }





  private boolean isAttributeEquals(Object attrOld, Object attrNew)
  {
    if (attrOld == attrNew) {
        return true;
    }
    if ((attrOld == null) || (attrNew == null)) {
        return false;
    }

    return attrOld.equals(attrNew);
  }


  public void setAttributeValue(String attributeName, Object value)
  {
    if ((attributeName == null) || (attributeName.length() == 0)) {
        return;
    }
    BeanHelper.setProperty(this, attributeName.toUpperCase(), value);
  }






}
