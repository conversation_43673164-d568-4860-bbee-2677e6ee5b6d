package com.cowell.bam.service.dto;

import java.math.BigDecimal;

public class StockBatchCodeCostDTO {

    private Long businessId;

    private Long storeId;

    private String skuMerchantCode;

    private String batchCode;

    private String batchNo;

    private Long cost;

    private BigDecimal pieceUnit;

    private BigDecimal stock;

    private BigDecimal buyStock;

    private BigDecimal pieceBuyStock;

    private BigDecimal waittingAreaStock;

    private BigDecimal pieceWaittingAreaStock;

    private BigDecimal unqualifiedAreaStock;

    private BigDecimal pieceUnqualifiedAreaStock;

    private BigDecimal waitStock;

    private BigDecimal transitStock;

    public Long getBusinessId() {
        return businessId;
    }

    public void setBusinessId(Long businessId) {
        this.businessId = businessId;
    }

    public Long getStoreId() {
        return storeId;
    }

    public void setStoreId(Long storeId) {
        this.storeId = storeId;
    }

    public String getSkuMerchantCode() {
        return skuMerchantCode;
    }

    public void setSkuMerchantCode(String skuMerchantCode) {
        this.skuMerchantCode = skuMerchantCode;
    }

    public String getBatchCode() {
        return batchCode;
    }

    public void setBatchCode(String batchCode) {
        this.batchCode = batchCode;
    }

    public String getBatchNo() {
        return batchNo;
    }

    public void setBatchNo(String batchNo) {
        this.batchNo = batchNo;
    }

    public Long getCost() {
        return cost;
    }

    public void setCost(Long cost) {
        this.cost = cost;
    }

    public BigDecimal getPieceUnit() {
        return pieceUnit;
    }

    public void setPieceUnit(BigDecimal pieceUnit) {
        this.pieceUnit = pieceUnit;
    }

    public BigDecimal getStock() {
        return stock;
    }

    public void setStock(BigDecimal stock) {
        this.stock = stock;
    }

    public BigDecimal getBuyStock() {
        return buyStock;
    }

    public void setBuyStock(BigDecimal buyStock) {
        this.buyStock = buyStock;
    }

    public BigDecimal getPieceBuyStock() {
        return pieceBuyStock;
    }

    public void setPieceBuyStock(BigDecimal pieceBuyStock) {
        this.pieceBuyStock = pieceBuyStock;
    }

    public BigDecimal getWaittingAreaStock() {
        return waittingAreaStock;
    }

    public void setWaittingAreaStock(BigDecimal waittingAreaStock) {
        this.waittingAreaStock = waittingAreaStock;
    }

    public BigDecimal getPieceWaittingAreaStock() {
        return pieceWaittingAreaStock;
    }

    public void setPieceWaittingAreaStock(BigDecimal pieceWaittingAreaStock) {
        this.pieceWaittingAreaStock = pieceWaittingAreaStock;
    }

    public BigDecimal getUnqualifiedAreaStock() {
        return unqualifiedAreaStock;
    }

    public void setUnqualifiedAreaStock(BigDecimal unqualifiedAreaStock) {
        this.unqualifiedAreaStock = unqualifiedAreaStock;
    }

    public BigDecimal getPieceUnqualifiedAreaStock() {
        return pieceUnqualifiedAreaStock;
    }

    public void setPieceUnqualifiedAreaStock(BigDecimal pieceUnqualifiedAreaStock) {
        this.pieceUnqualifiedAreaStock = pieceUnqualifiedAreaStock;
    }

    public BigDecimal getWaitStock() {
        return waitStock;
    }

    public void setWaitStock(BigDecimal waitStock) {
        this.waitStock = waitStock;
    }

    public BigDecimal getTransitStock() {
        return transitStock;
    }

    public void setTransitStock(BigDecimal transitStock) {
        this.transitStock = transitStock;
    }

    @Override
    public String toString() {
        return "StockBatchCodeCostDTO{" +
            "businessId=" + businessId +
            ", storeId=" + storeId +
            ", skuMerchantCode='" + skuMerchantCode + '\'' +
            ", batchCode='" + batchCode + '\'' +
            ", batchNo='" + batchNo + '\'' +
            ", cost=" + cost +
            ", pieceUnit=" + pieceUnit +
            ", stock=" + stock +
            ", buyStock=" + buyStock +
            ", pieceBuyStock=" + pieceBuyStock +
            ", waittingAreaStock=" + waittingAreaStock +
            ", pieceWaittingAreaStock=" + pieceWaittingAreaStock +
            ", unqualifiedAreaStock=" + unqualifiedAreaStock +
            ", pieceUnqualifiedAreaStock=" + pieceUnqualifiedAreaStock +
            ", waitStock=" + waitStock +
            ", transitStock=" + transitStock +
            '}';
    }
}
