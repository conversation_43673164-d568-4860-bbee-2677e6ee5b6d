package com.cowell.bam.service.dto.base;
/**
 * @Authur:lbw
 * @Date:2018/6/11
 * @Time:上午9:52
 * @Description:
 */


import com.cowell.bam.enums.ReturnCodeEnum;

import java.io.Serializable;

/**
 *
 *
 *<AUTHOR>
 *@date 2018/6/11 上午9:52
 */
public class CommonResponse<T> implements Serializable {
    /**
     * code 0 success
     */
    int code;

    String message;

    T result;

    public CommonResponse() {
        this.code = 0 ;
    }

    public CommonResponse(T result) {
        this.result = result;
        this.code = ReturnCodeEnum.SUCCESS.getCode() ;
        this.message = ReturnCodeEnum.SUCCESS.getMessage();
    }


    public CommonResponse(int code, String message, T result) {
        this.code = code;
        this.message = message;
        this.result = result;
    }


    public CommonResponse(int code, String message) {
        this.code = code;
        this.message = message;
    }

    public CommonResponse(ReturnCodeEnum returnCodeEnum) {
        this.code = returnCodeEnum.getCode();
        this.message = returnCodeEnum.getMessage();
    }

    public CommonResponse(ReturnCodeEnum returnCodeEnum, T result) {
        this.code = returnCodeEnum.getCode();
        this.message = returnCodeEnum.getMessage();
        this.result = result;
    }

    public int getCode() {
        return code;
    }

    public void setCode(int code) {
        this.code = code;
    }

    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }

    public T getResult() {
        return result;
    }

    public void setResult(T result) {
        this.result = result;
    }

    @Override
    public String toString() {
        return "CommonResponse{" +
                "code=" + code +
                ", message='" + message + '\'' +
                ", result=" + result +
                '}';
    }
}
