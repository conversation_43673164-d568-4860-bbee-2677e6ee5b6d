package com.cowell.bam.service.dto;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2022/10/30 23:04
 */
public class CompareTransitMessageDTO implements Serializable {

    /**
     * 公司编码
     */
    private String comId;

    /**
     * 版本，对比批次
     */
    private Integer version;

    /**
     * 当天日期
     */
    private String date;

    public CompareTransitMessageDTO(String comId, Integer version, String date) {
        this.comId = comId;
        this.version = version;
        this.date = date;
    }

    @Override
    public String toString() {
        return "CompareTransitMessageDTO{" +
            "comId='" + comId + '\'' +
            ", version=" + version +
            ", date=" + date +
            '}';
    }

    public String getDate() {
        return date;
    }

    public void setDate(String date) {
        this.date = date;
    }

    public String getComId() {
        return comId;
    }

    public void setComId(String comId) {
        this.comId = comId;
    }

    public Integer getVersion() {
        return version;
    }

    public void setVersion(Integer version) {
        this.version = version;
    }
}
