package com.cowell.bam.service.dto;

import org.apache.commons.lang3.builder.ToStringBuilder;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR> <EMAIL>
 * @date : 2020/07/23 14:17
 * @description :
 */
public class PosFundDetailDTO implements Serializable {

    private static final long serialVersionUID = -6769114383221591383L;

    /**
     * 企业ID
     */
    private String compid;

    /**
     * 卡号
     */
    private String memcardno;

    /**
     * 积分变动值
     */
    private String fundval;

    /**
     * 积分变动时间
     */
    private String funddatetime;

    /**
     * 手机号
     */
    private String phone;

    /**
     * userid
     */
    private Long userid;

    /**
     * 唯一编号
     */
    private String bdid;

    /**
     * 积分变动原因
     */
    private String notes;

    //上面和海典积分对账是一样的，下面fromUserId，fromErpCode，mergeDate三个是华南的英克独有的
    /**
     * 合并卡的UserId
     */
    private String fromUserId;

    /**
     * 合并卡的ErpCode
     */
    private String fromErpCode;

    /**
     * 合卡时间
     */
    private Date mergeDate;

    public String getCompid() {
        return compid;
    }

    public void setCompid(String compid) {
        this.compid = compid;
    }

    public String getMemcardno() {
        return memcardno;
    }

    public void setMemcardno(String memcardno) {
        this.memcardno = memcardno;
    }

    public String getFundval() {
        return fundval;
    }

    public void setFundval(String fundval) {
        this.fundval = fundval;
    }

    public String getFunddatetime() {
        return funddatetime;
    }

    public void setFunddatetime(String funddatetime) {
        this.funddatetime = funddatetime;
    }

    public String getPhone() {
        return phone;
    }

    public void setPhone(String phone) {
        this.phone = phone;
    }

    public Long getUserid() {
        return userid;
    }

    public void setUserid(Long userid) {
        this.userid = userid;
    }

    public String getBdid() {
        return bdid;
    }

    public void setBdid(String bdid) {
        this.bdid = bdid;
    }

    public String getNotes() {
        return notes;
    }

    public void setNotes(String notes) {
        this.notes = notes;
    }

    public String getFromUserId() {
        return fromUserId;
    }

    public void setFromUserId(String fromUserId) {
        this.fromUserId = fromUserId;
    }

    public String getFromErpCode() {
        return fromErpCode;
    }

    public void setFromErpCode(String fromErpCode) {
        this.fromErpCode = fromErpCode;
    }

    public Date getMergeDate() {
        return mergeDate;
    }

    public void setMergeDate(Date mergeDate) {
        this.mergeDate = mergeDate;
    }

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this);
    }
}
