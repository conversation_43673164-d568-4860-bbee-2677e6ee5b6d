package com.cowell.bam.service.dto;

import com.fasterxml.jackson.annotation.JsonFormat;

import java.util.Date;

/**
 * <AUTHOR>
 * @ProjectName bam
 * @Description:
 * @date 2019/10/08 15:21
 */
public class DiffCollectDataResponseDTO {

    private Long businessId;
    private Long storeId;
    private String goodsCode;
    private Long count;
    private String businessName;
    private String typeName;
    private String reason;
    /**
     * 数据类型 0-库存 1-价格 2-订单 3-积分
     */
    private Integer dataType;

    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss" ,timezone = "GMT+8")
    private Date gmtCreate;

    private String thirdData;

    private String ourData;

    public String getReason() {
        return reason;
    }

    public void setReason(String reason) {
        this.reason = reason;
    }

    public String getGoodsCode() {
        return goodsCode;
    }

    public void setGoodsCode(String goodsCode) {
        this.goodsCode = goodsCode;
    }

    public Long getStoreId() {
        return storeId;
    }

    public void setStoreId(Long storeId) {
        this.storeId = storeId;
    }

    public String getThirdData() {
        return thirdData;
    }

    public void setThirdData(String thirdData) {
        this.thirdData = thirdData;
    }

    public String getOurData() {
        return ourData;
    }

    public void setOurData(String ourData) {
        this.ourData = ourData;
    }

    public Long getBusinessId() {
        return businessId;
    }

    public void setBusinessId(Long businessId) {
        this.businessId = businessId;
    }

    public Long getCount() {
        return count;
    }

    public void setCount(Long count) {
        this.count = count;
    }

    public String getBusinessName() {
        return businessName;
    }

    public void setBusinessName(String businessName) {
        this.businessName = businessName;
    }

    public String getTypeName() {
        return typeName;
    }

    public void setTypeName(String typeName) {
        this.typeName = typeName;
    }

    public Integer getDataType() {
        return dataType;
    }

    public void setDataType(Integer dataType) {
        this.dataType = dataType;
    }

    public Date getGmtCreate() {
        return gmtCreate;
    }

    public void setGmtCreate(Date gmtCreate) {
        this.gmtCreate = gmtCreate;
    }

    @Override
    public String toString() {
        return "DiffCollectDataResponseDTO{" +
            "businessId=" + businessId +
            ", count=" + count +
            ", businessName='" + businessName + '\'' +
            ", typeName='" + typeName + '\'' +
            ", dataType=" + dataType +
            ", gmtCreate=" + gmtCreate +
            '}';
    }
}
