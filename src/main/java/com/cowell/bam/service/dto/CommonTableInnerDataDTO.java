package com.cowell.bam.service.dto;

import org.apache.commons.lang.builder.ToStringBuilder;

/**
 * <AUTHOR>
 * @Description: Table内部数据
 * @date 2018-10-17 14:43
 */

public class CommonTableInnerDataDTO<K,v> {

    /**
     * 唯一ID
     */
    public String bguid;

    /**
     * 枚举全部类型分类
     */
    public Integer btype;

    /**
     * 回调地址
     */
    public String bcallback;

    /**
     * 发送数据的系统编号
     */
    public Integer bsource;

    /**
     * 接收数据的系统编号
     */
    public Integer bdestination;

    /**
     * 格式YYYY-MM-DD HH:mm:ss
     */
    public String bdatetime;

    /**
     * 1已发送,2已接受,3发送失败,4处理中,5处理成功,6处理异常
     */
    public String bstatus;


    /**
     * 接口版本号
     */
    public String bversion;
    /**
     * 数据重复校验字符串
     */
    public String bdatahash;

    /**
     * JSON格式
     */
    public K bkeys;

    /**
     * JSON格式。发送时传业务数据，接收时传处理结果
     */
    public v bdata;

    public String getBguid() {
        return bguid;
    }

    public void setBguid(String bguid) {
        this.bguid = bguid;
    }

    public Integer getBtype() {
        return btype;
    }

    public void setBtype(Integer btype) {
        this.btype = btype;
    }

    public String getBcallback() {
        return bcallback;
    }

    public void setBcallback(String bcallback) {
        this.bcallback = bcallback;
    }

    public Integer getBsource() {
        return bsource;
    }

    public void setBsource(Integer bsource) {
        this.bsource = bsource;
    }

    public Integer getBdestination() {
        return bdestination;
    }

    public void setBdestination(Integer bdestination) {
        this.bdestination = bdestination;
    }

    public String getBdatetime() {
        return bdatetime;
    }

    public void setBdatetime(String bdatetime) {
        this.bdatetime = bdatetime;
    }

    public String getBstatus() {
        return bstatus;
    }

    public void setBstatus(String bstatus) {
        this.bstatus = bstatus;
    }

    public String getBversion() {
        return bversion;
    }

    public void setBversion(String bversion) {
        this.bversion = bversion;
    }

    public String getBdatahash() {
        return bdatahash;
    }

    public void setBdatahash(String bdatahash) {
        this.bdatahash = bdatahash;
    }

    public K getBkeys() {
        return bkeys;
    }

    public void setBkeys(K bkeys) {
        this.bkeys = bkeys;
    }

    public v getBdata() {
        return bdata;
    }

    public void setBdata(v bdata) {
        this.bdata = bdata;
    }

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this);
    }
}
