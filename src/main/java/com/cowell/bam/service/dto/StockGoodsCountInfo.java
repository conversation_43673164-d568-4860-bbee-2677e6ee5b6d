package com.cowell.bam.service.dto;

import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;
import java.math.BigDecimal;

public class StockGoodsCountInfo extends StockGoodsBaseDTO implements Serializable {


    @ApiModelProperty(name = "skuMerchantCode", value = "商品编号")
    private String skuMerchantCode;
    @ApiModelProperty(name = "stock", value = "总库存")
    private BigDecimal stock;
    @ApiModelProperty(name = "buyStock", value = "可卖库存")
    private BigDecimal buyStock;
    @ApiModelProperty(name = "transitStock", value = "在途库存")
    private BigDecimal transitStock;
    @ApiModelProperty(name = "unit", value = "存库单位")
    private String unit;
    //批号拥有字段
    @ApiModelProperty(name = "batchNo", value = "生成批号")
    private String batchNo;
    @ApiModelProperty(name = "batchCode", value = "批次")
    private String batchCode;
    @ApiModelProperty(name = "waittingAreaStock", value = "待验收区库存")
    private BigDecimal waittingAreaStock;
    @ApiModelProperty(name = "unqualifiedAreaStock", value = "非合格品区库存")
    private BigDecimal unqualifiedAreaStock;

    @ApiModelProperty(name = "pieceBuyStock", value = "拆零可卖库存(合格品区小库存)")
    private BigDecimal pieceBuyStock;
    @ApiModelProperty(name = "waitStock", value = "待出库库存")
    private BigDecimal waitStock;
    @ApiModelProperty(name = "produceCompany", value = "生产企业")
    private String produceCompany;

    /**
     * 门店名称
     */
    private String storeName;


    /**
     * 有效类型(0 不管控 ，1 近效期销售提醒 ，2 近效期禁止销售)
     */
    private Integer expDateType;

    public String getStoreName() {
        return storeName;
    }

    public void setStoreName(String storeName) {
        this.storeName = storeName;
    }

    public BigDecimal getTransitStock() {
        return transitStock;
    }

    public void setTransitStock(BigDecimal transitStock) {
        this.transitStock = transitStock;
    }

    public BigDecimal getPieceBuyStock() {
        return pieceBuyStock;
    }

    public void setPieceBuyStock(BigDecimal pieceBuyStock) {
        this.pieceBuyStock = pieceBuyStock;
    }

    public BigDecimal getWaitStock() {
        return waitStock;
    }

    public void setWaitStock(BigDecimal waitStock) {
        this.waitStock = waitStock;
    }

    public String getSkuMerchantCode() {
        return skuMerchantCode;
    }

    public void setSkuMerchantCode(String skuMerchantCode) {
        this.skuMerchantCode = skuMerchantCode;
    }

    public BigDecimal getStock() {
        return stock;
    }

    public void setStock(BigDecimal stock) {
        this.stock = stock;
    }

    public BigDecimal getBuyStock() {
        return buyStock;
    }

    public void setBuyStock(BigDecimal buyStock) {
        this.buyStock = buyStock;
    }

    public String getUnit() {
        return unit;
    }

    public void setUnit(String unit) {
        this.unit = unit;
    }

    public String getBatchNo() {
        return batchNo;
    }

    public void setBatchNo(String batchNo) {
        this.batchNo = batchNo;
    }

    public String getBatchCode() {
        return batchCode;
    }

    public void setBatchCode(String batchCode) {
        this.batchCode = batchCode;
    }

    public BigDecimal getWaittingAreaStock() {
        return waittingAreaStock;
    }

    public void setWaittingAreaStock(BigDecimal waittingAreaStock) {
        this.waittingAreaStock = waittingAreaStock;
    }

    public BigDecimal getUnqualifiedAreaStock() {
        return unqualifiedAreaStock;
    }

    public void setUnqualifiedAreaStock(BigDecimal unqualifiedAreaStock) {
        this.unqualifiedAreaStock = unqualifiedAreaStock;
    }

    public String getProduceCompany() {
        return produceCompany;
    }

    public void setProduceCompany(String produceCompany) {
        this.produceCompany = produceCompany;
    }

    public Integer getExpDateType() {
        return expDateType;
    }

    public void setExpDateType(Integer expDateType) {
        this.expDateType = expDateType;
    }

    @Override
    public String toString() {
        return "StockGoodsCountInfo{" +
            "skuMerchantCode='" + skuMerchantCode + '\'' +
            ", stock=" + stock +
            ", buyStock=" + buyStock +
            ", unit='" + unit + '\'' +
            ", batchNo='" + batchNo + '\'' +
            ", batchCode='" + batchCode + '\'' +
            ", waittingAreaStock=" + waittingAreaStock +
            ", unqualifiedAreaStock=" + unqualifiedAreaStock +
            ", pieceBuyStock=" + pieceBuyStock +
            ", waitStock=" + waitStock +
            ", produceCompany='" + produceCompany + '\'' +
            ", transitStock=" + transitStock +
            '}';
    }
}
