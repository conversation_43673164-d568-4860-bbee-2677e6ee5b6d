package com.cowell.bam.service.dto.base;

import java.beans.BeanInfo;
import java.beans.IntrospectionException;
import java.beans.Introspector;
import java.beans.PropertyDescriptor;
import java.lang.reflect.Method;
import java.util.*;
import java.util.concurrent.locks.ReentrantReadWriteLock;


public class BeanHelper
{
  protected static final Object[] NULL_ARGUMENTS = new Object[0];
  private static Map<String, Map<Integer, Map<String, Method>>> cache = new HashMap();
  private static BeanHelper bhelp = new BeanHelper();
  private static final int GETID = 0;
  private static final int SETID = 1;
  ReentrantReadWriteLock rwl = new ReentrantReadWriteLock();

  public static BeanHelper getInstance()
  {
    return bhelp;
  }

  protected static PropertyDescriptor[] getPropertyDescriptor(Class beanCls)
  {
    BeanInfo beanInfo;
    try
    {
      beanInfo = Introspector.getBeanInfo(beanCls);
      return beanInfo.getPropertyDescriptors();
    } catch (IntrospectionException e) {
      throw new RuntimeException("Failed to instrospect bean: " + beanCls, e);
    }
  }

  public static List<String> getPropertys(Object bean)
  {
    return Arrays.asList(getInstance().getPropertiesAry(bean));
  }

  public String[] getPropertiesAry(Object bean)
  {
    Map cMethod = null;
    this.rwl.readLock().lock();
    try {
      cMethod = cacheMethod(bean.getClass());
    } finally {
      this.rwl.readLock().unlock();
    }
    String[] retProps = (String[])((Map)cMethod.get(Integer.valueOf(1))).keySet().toArray(new String[0]);

    return retProps;
  }

  public static Object getProperty(Object bean, String propertyName) {
    Method method;
    try {
      method = getInstance().getMethod(bean, propertyName, false);
      if (method == null) {
          return null;
      }
      return method.invoke(bean, NULL_ARGUMENTS);
    } catch (Exception e) {
      String errStr = "Failed to get property: " + propertyName;
      throw new RuntimeException(errStr, e);
    }
  }

  public static Method getMethod(Object bean, String propertyName) {
    return getInstance().getMethod(bean, propertyName, true);
  }

  private Method getMethod(Object bean, String propertyName, boolean isSetMethod)
  {
    Method method = null;
    this.rwl.readLock().lock();
    Map cMethod = null;
    try {
      cMethod = cacheMethod(bean.getClass());
    } finally {
      this.rwl.readLock().unlock();
    }
    if (isSetMethod) {
        method = (Method) ((Map) cMethod.get(Integer.valueOf(1))).get(propertyName);
    }
    else {
        method = (Method) ((Map) cMethod.get(Integer.valueOf(0))).get(propertyName);
    }
    return method;
  }

  private Map<Integer, Map<String, Method>> cacheMethod(Class beanCls) {
    String key = beanCls.getName();
    Map cMethod = (Map)cache.get(key);
    if (cMethod == null) {
      this.rwl.readLock().unlock();
      this.rwl.writeLock().lock();
      try {
        cMethod = (Map)cache.get(key);
        if (cMethod == null) {
          cMethod = new HashMap();
          Map getMap = new HashMap();
          Map setMap = new HashMap();
          cMethod.put(Integer.valueOf(0), getMap);
          cMethod.put(Integer.valueOf(1), setMap);
          cache.put(key, cMethod);
          PropertyDescriptor[] pdescriptor = getPropertyDescriptor(beanCls);
          PropertyDescriptor[] arr$ = pdescriptor; int len$ = arr$.length; for (int i$ = 0; i$ < len$; ++i$) { PropertyDescriptor pd = arr$[i$];
            if (pd.getReadMethod() != null) {
                getMap.put(pd.getName().toUpperCase(), pd.getReadMethod());
            }
            if (pd.getWriteMethod() != null) {
                setMap.put(pd.getName().toUpperCase(), pd.getWriteMethod());
            }
          }
        }
      }
      finally {
        this.rwl.readLock().lock();
        this.rwl.writeLock().unlock();
      }
    }
    return cMethod;
  }

  public static void invokeMethod(Object bean, Method method, Object value) {
    try {
      if (method == null) {
          return;
      }
      Object[] arguments = { value };
      method.invoke(bean, arguments);
    } catch (Exception e) {
      String errStr = "Failed to set property: " + method.getName();

      throw new RuntimeException(errStr, e);
    }
  }

  public static void setProperty(Object bean, String propertyName, Object value) {
    Method method;
    try {
      method = getInstance().getMethod(bean, propertyName, true);
      if (method == null) {
          return;
      }
      Object[] arguments = { value };
      method.invoke(bean, arguments);
    } catch (Exception e) {
      String errStr = "Failed to set property: " + propertyName + " on bean: " + bean.getClass().getName() + " with value:" + value;

      throw new RuntimeException(errStr, e);
    }
  }


}
