package com.cowell.bam.service.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
@Data
public class ItemPriceResponse implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 连锁id
     */
    private Long businessId;
    /**
     * 门店id
     */
    private Long storeId;

    /**
     * 连锁下商品唯一id
     */
    private String goodsNo;

    /**
     * 商品价格
     */
    private String price;

    /**
     * 商品拆零价格
     */
    private String piecePrice;

    /**
     * 商品会员价格
     */
    private String memberPrice;

    /**
     * 商品会员拆零价格
     */
    private String memberPiecePrice;

    /**
     * 是否有价格 0 否 1 是
     */
    private Integer isHasPrice;

    /**
     * 是否有拆零价格 0 否 1 是
     */
    private Integer isHasPiecePrice;

    /**
     * 是否有会员价格 0 否 1 是
     */
    private Integer isHasMemberPrice;

    /**
     * 是否有会员拆零价格 0 否 1 是
     */
    private Integer isHasMemberPiecePrice;

    @ApiModelProperty("是否特价  0 否 1 是")
    private Integer isSpecial;

    @ApiModelProperty("商品会员价格是否特价  0 否 1 是")
    private Integer memberPriceIsSpecial;

    @ApiModelProperty("是否特价  0 否 1 是")
    private Integer isPieceSpecial;

    @ApiModelProperty("商品会员价格是否特价  0 否 1 是")
    private Integer memberPiecePriceIsSpecial;
}
