package com.cowell.bam.service.dto;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * bam
 * 2020/9/14 14:22
 * 第三方平台库存信息
 *
 * <AUTHOR>
 * @since
 **/
@Data
public class ThirdPlatformStockDTO implements Serializable {

    /**
     * 企业编号
     */
    private String compId;

    /**
     * 门店编号（主数据编码，dc编码）
     */
    private String busNo;

    /**
     * 通知SAP消息类型
     * 6073：库存核对
     * 6074：库存更新
     */
    private String type;

    /**
     * 企业编号
     */
    private List<ThirdPlatformStockDetailDTO> details;

    @Override
    public String toString() {
        return "ThirdPlatformStockDTO{" +
            "compId='" + compId + '\'' +
            ", busNo='" + busNo + '\'' +
            ", type='" + type + '\'' +
            ", details=" + details +
            '}';
    }
}
