package com.cowell.bam.service.dto;

import io.swagger.annotations.ApiModelProperty;

/**
 * 获取特定商品所有价格
 *
 * @author: liubw
 * @date 2018/10/30 4:12 PM
 */
public class PriceQueryDetailParam {

    @ApiModelProperty("spuId")
    private Long spuId;

    @ApiModelProperty("商品编码")
    private String goodsNo;


    public Long getSpuId() {
        return spuId;
    }

    public void setSpuId(Long spuId) {
        this.spuId = spuId;
    }

    public String getGoodsNo() {
        return goodsNo;
    }

    public void setGoodsNo(String goodsNo) {
        this.goodsNo = goodsNo;
    }

    @Override
    public String toString() {
        return "PriceQueryDetailParam{" +
            ", spuId=" + spuId +
            ", goodsNo=" + goodsNo +
            '}';
    }
}
