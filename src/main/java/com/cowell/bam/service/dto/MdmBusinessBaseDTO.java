package com.cowell.bam.service.dto;

import lombok.Getter;
import lombok.Setter;
import org.apache.commons.lang.builder.ToStringBuilder;

import java.io.Serializable;
import java.time.ZonedDateTime;

/**
 * <AUTHOR> <EMAIL>
 * @date : 2018/10/12 16:52
 * @description : Mdm连锁企业映射
 */
@Setter
@Getter
public class MdmBusinessBaseDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    private Long id;

    /**
     * 高济连锁企业id
     */
    private Long businessId;

    /**
     * 连锁企业第三方编码
     */
    private String comId;

    /**
     * 适用于连锁合并场景下 一个busId映射多个comId 以逗号分割开
     */
    private String comIds;

    /**
     * 连锁企业名称
     */
    private String comName;

    /**
     * 状态 ：-1 删除，1 正常
     */
    private Integer status;

    /**
     * 创建时间
     */
    private ZonedDateTime gmtCreate;

    /**
     * 更新时间
     */
    private ZonedDateTime gmtUpdate;

    /**
     * 版本号
     */
    private Integer version;

    /**
     * 可扩展字段
     */
    private String extend;

    /**
     * 所属合并连锁id
     */
    private Long belongTo;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getBusinessId() {
        return businessId;
    }

    public void setBusinessId(Long businessId) {
        this.businessId = businessId;
    }

    public String getComId() {
        return comId;
    }

    public void setComId(String comId) {
        this.comId = comId;
    }

    public String getComName() {
        return comName;
    }

    public void setComName(String comName) {
        this.comName = comName;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public ZonedDateTime getGmtCreate() {
        return gmtCreate;
    }

    public void setGmtCreate(ZonedDateTime gmtCreate) {
        this.gmtCreate = gmtCreate;
    }

    public ZonedDateTime getGmtUpdate() {
        return gmtUpdate;
    }

    public void setGmtUpdate(ZonedDateTime gmtUpdate) {
        this.gmtUpdate = gmtUpdate;
    }

    public Integer getVersion() {
        return version;
    }

    public void setVersion(Integer version) {
        this.version = version;
    }

    public String getExtend() {
        return extend;
    }

    public void setExtend(String extend) {
        this.extend = extend;
    }

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this);
    }

}
