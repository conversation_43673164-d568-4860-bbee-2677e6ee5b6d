package com.cowell.bam.service.dto;

import java.io.Serializable;
import java.math.BigDecimal;

public class StockBatchCodeCost implements Serializable {
    private Long id;

    private Long businessId;

    private Long storeId;

    private String skuMerchantCode;

    private String batchCode;

    private String batchNo;

    private Long cost;

    private BigDecimal pieceUnit;

    private BigDecimal stock;

    private BigDecimal buyStock;

    private BigDecimal pieceBuyStock;

    private BigDecimal waittingAreaStock;

    private BigDecimal pieceWaittingAreaStock;

    private BigDecimal unqualifiedAreaStock;

    private BigDecimal pieceUnqualifiedAreaStock;

    private BigDecimal waitStock;

    private BigDecimal transitStock;


    private static final long serialVersionUID = 1L;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getBusinessId() {
        return businessId;
    }

    public void setBusinessId(Long businessId) {
        this.businessId = businessId;
    }

    public Long getStoreId() {
        return storeId;
    }

    public void setStoreId(Long storeId) {
        this.storeId = storeId;
    }

    public String getSkuMerchantCode() {
        return skuMerchantCode;
    }

    public void setSkuMerchantCode(String skuMerchantCode) {
        this.skuMerchantCode = skuMerchantCode == null ? null : skuMerchantCode.trim();
    }

    public String getBatchCode() {
        return batchCode;
    }

    public void setBatchCode(String batchCode) {
        this.batchCode = batchCode == null ? null : batchCode.trim();
    }

    public String getBatchNo() {
        return batchNo;
    }

    public void setBatchNo(String batchNo) {
        this.batchNo = batchNo == null ? null : batchNo.trim();
    }

    public Long getCost() {
        return cost;
    }

    public void setCost(Long cost) {
        this.cost = cost;
    }

    public BigDecimal getPieceUnit() {
        return pieceUnit;
    }

    public void setPieceUnit(BigDecimal pieceUnit) {
        this.pieceUnit = pieceUnit;
    }

    public BigDecimal getStock() {
        return stock;
    }

    public void setStock(BigDecimal stock) {
        this.stock = stock;
    }

    public BigDecimal getBuyStock() {
        return buyStock;
    }

    public void setBuyStock(BigDecimal buyStock) {
        this.buyStock = buyStock;
    }

    public BigDecimal getPieceBuyStock() {
        return pieceBuyStock;
    }

    public void setPieceBuyStock(BigDecimal pieceBuyStock) {
        this.pieceBuyStock = pieceBuyStock;
    }

    public BigDecimal getWaittingAreaStock() {
        return waittingAreaStock;
    }

    public void setWaittingAreaStock(BigDecimal waittingAreaStock) {
        this.waittingAreaStock = waittingAreaStock;
    }

    public BigDecimal getPieceWaittingAreaStock() {
        return pieceWaittingAreaStock;
    }

    public void setPieceWaittingAreaStock(BigDecimal pieceWaittingAreaStock) {
        this.pieceWaittingAreaStock = pieceWaittingAreaStock;
    }

    public BigDecimal getUnqualifiedAreaStock() {
        return unqualifiedAreaStock;
    }

    public void setUnqualifiedAreaStock(BigDecimal unqualifiedAreaStock) {
        this.unqualifiedAreaStock = unqualifiedAreaStock;
    }

    public BigDecimal getPieceUnqualifiedAreaStock() {
        return pieceUnqualifiedAreaStock;
    }

    public void setPieceUnqualifiedAreaStock(BigDecimal pieceUnqualifiedAreaStock) {
        this.pieceUnqualifiedAreaStock = pieceUnqualifiedAreaStock;
    }

    public BigDecimal getWaitStock() {
        return waitStock;
    }

    public void setWaitStock(BigDecimal waitStock) {
        this.waitStock = waitStock;
    }

    public BigDecimal getTransitStock() {
        return transitStock;
    }

    public void setTransitStock(BigDecimal transitStock) {
        this.transitStock = transitStock;
    }


    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", businessId=").append(businessId);
        sb.append(", storeId=").append(storeId);
        sb.append(", skuMerchantCode=").append(skuMerchantCode);
        sb.append(", batchCode=").append(batchCode);
        sb.append(", batchNo=").append(batchNo);
        sb.append(", cost=").append(cost);
        sb.append(", pieceUnit=").append(pieceUnit);
        sb.append(", stock=").append(stock);
        sb.append(", buyStock=").append(buyStock);
        sb.append(", pieceBuyStock=").append(pieceBuyStock);
        sb.append(", waittingAreaStock=").append(waittingAreaStock);
        sb.append(", pieceWaittingAreaStock=").append(pieceWaittingAreaStock);
        sb.append(", unqualifiedAreaStock=").append(unqualifiedAreaStock);
        sb.append(", pieceUnqualifiedAreaStock=").append(pieceUnqualifiedAreaStock);
        sb.append(", waitStock=").append(waitStock);
        sb.append(", transitStock=").append(transitStock);
        sb.append(", serialVersionUID=").append(serialVersionUID);
        sb.append("]");
        return sb.toString();
    }
}
