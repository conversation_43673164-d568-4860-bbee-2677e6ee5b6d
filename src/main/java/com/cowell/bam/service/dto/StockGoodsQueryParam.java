package com.cowell.bam.service.dto;

import io.swagger.annotations.ApiModelProperty;

import javax.validation.constraints.NotNull;
import java.util.List;


public class StockGoodsQueryParam extends StockGoodsBaseDTO {
    @ApiModelProperty(name = "skuMerchantCode", value = "商品编码", required = true)
    @NotNull(message = "商品编码不能为空")
    private List<String> skuMerchantCodes;

    @ApiModelProperty(value = "是否查询在有效期内空则为查所有 true则为查有效期内")
    private boolean validity = false;

    @ApiModelProperty(value = "是否过滤库存为0的商品")
    private boolean filterZero = false;

    public List<String> getSkuMerchantCodes() {
        return skuMerchantCodes;
    }

    public void setSkuMerchantCodes(List<String> skuMerchantCodes) {
        this.skuMerchantCodes = skuMerchantCodes;
    }

    public boolean isValidity() {
        return validity;
    }

    public void setValidity(boolean validity) {
        this.validity = validity;
    }

    public boolean isFilterZero() {
        return filterZero;
    }

    public void setFilterZero(boolean filterZero) {
        this.filterZero = filterZero;
    }

    @Override
    public String toString() {
        return "StockGoodsQueryParam{" +
            "skuMerchantCodes=" + skuMerchantCodes +
            ", validity=" + validity +
            ", filterZero=" + filterZero +
            '}';
    }
}
