package com.cowell.bam.service.dto;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * price_store_detail
 * <AUTHOR>
public class PriceStoreDetail implements Serializable {
    private Long id;

    /**
     * 门店id
     */
    private Long storeId;

    /**
     * 门店名称
     */
    private String storeName;

    /**
     * spuId
     */
    private Long spuId;

    /**
     * 商品编码
     */
    private String goodsNo;

    /**
     * 唯一标志码
     */
    private String priceTypeCode;

    /**
     * 价格类型id
     */
    private Long priceTypeId;

    /**
     * 价格类型名称
     */
    private String priceTypeName;

    /**
     * 价格
     */
    private BigDecimal price;

    /**
     * 授权机构id(调价的商品是谁授权的)
     */
    private Long authOrgId;

    /**
     * 授权机构名(调价的商品是谁授权的)
     */
    private String authOrgName;

    /**
     * 授权机构级别(调价的商品是谁授权的)
     */
    private Byte authOrgLevel;

    /**
     * 调价权限
     */
    private Long orgId;

    /**
     * 调价权限名称
     */
    private String orgName;

    /**
     * 所属级别(1集团,2平台,3连锁,4门店)
     */
    private Byte level;

    /**
     * 连锁机构ID
     */
    private Long businessId;

    /**
     * 连锁名称
     */
    private String businessName;

    /**
     * 平台机构ID
     */
    private Long platformId;

    /**
     * 平台名称
     */
    private String platformName;

    /**
     * 调价单号
     */
    private String adjustCode;

    /**
     * 调价单明细ID
     */
    private Long adjustDetailId;

    /**
     * 定价目录ID
     */
    private Long orgGoodsId;

    /**
     * 通用名
     */
    private String curName;

    /**
     * 条形码
     */
    private String barCode;

    /**
     * 商品助记码
     */
    private String opCode;

    private Byte status;

    /**
     * 创建时间
     */
    private Date gmtCreate;

    /**
     * 更新时间
     */
    private Date gmtUpdate;

    /**
     * 扩展字段
     */
    private String extend;

    private Integer version;

    /**
     * 创建人ID
     */
    private Long createdBy;

    /**
     * 更新人ID
     */
    private Long updatedBy;

    /**
     * 最后操作人名称
     */
    private String updatedByName;

    /**
     * 是否维价(0是-1否)
     */
    private Byte isMaintain;

    /**
     * 备注
     */
    private String comment;

    private static final long serialVersionUID = 1L;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getStoreId() {
        return storeId;
    }

    public void setStoreId(Long storeId) {
        this.storeId = storeId;
    }

    public String getStoreName() {
        return storeName;
    }

    public void setStoreName(String storeName) {
        this.storeName = storeName;
    }

    public Long getSpuId() {
        return spuId;
    }

    public void setSpuId(Long spuId) {
        this.spuId = spuId;
    }

    public String getGoodsNo() {
        return goodsNo;
    }

    public void setGoodsNo(String goodsNo) {
        this.goodsNo = goodsNo;
    }

    public String getPriceTypeCode() {
        return priceTypeCode;
    }

    public void setPriceTypeCode(String priceTypeCode) {
        this.priceTypeCode = priceTypeCode;
    }

    public Long getPriceTypeId() {
        return priceTypeId;
    }

    public void setPriceTypeId(Long priceTypeId) {
        this.priceTypeId = priceTypeId;
    }

    public String getPriceTypeName() {
        return priceTypeName;
    }

    public void setPriceTypeName(String priceTypeName) {
        this.priceTypeName = priceTypeName;
    }

    public BigDecimal getPrice() {
        return price;
    }

    public void setPrice(BigDecimal price) {
        this.price = price;
    }

    public Long getAuthOrgId() {
        return authOrgId;
    }

    public void setAuthOrgId(Long authOrgId) {
        this.authOrgId = authOrgId;
    }

    public String getAuthOrgName() {
        return authOrgName;
    }

    public void setAuthOrgName(String authOrgName) {
        this.authOrgName = authOrgName;
    }

    public Byte getAuthOrgLevel() {
        return authOrgLevel;
    }

    public void setAuthOrgLevel(Byte authOrgLevel) {
        this.authOrgLevel = authOrgLevel;
    }

    public Long getOrgId() {
        return orgId;
    }

    public void setOrgId(Long orgId) {
        this.orgId = orgId;
    }

    public String getOrgName() {
        return orgName;
    }

    public void setOrgName(String orgName) {
        this.orgName = orgName;
    }

    public Byte getLevel() {
        return level;
    }

    public void setLevel(Byte level) {
        this.level = level;
    }

    public Long getBusinessId() {
        return businessId;
    }

    public void setBusinessId(Long businessId) {
        this.businessId = businessId;
    }

    public String getBusinessName() {
        return businessName;
    }

    public void setBusinessName(String businessName) {
        this.businessName = businessName;
    }

    public Long getPlatformId() {
        return platformId;
    }

    public void setPlatformId(Long platformId) {
        this.platformId = platformId;
    }

    public String getPlatformName() {
        return platformName;
    }

    public void setPlatformName(String platformName) {
        this.platformName = platformName;
    }

    public String getAdjustCode() {
        return adjustCode;
    }

    public void setAdjustCode(String adjustCode) {
        this.adjustCode = adjustCode;
    }

    public Long getAdjustDetailId() {
        return adjustDetailId;
    }

    public void setAdjustDetailId(Long adjustDetailId) {
        this.adjustDetailId = adjustDetailId;
    }

    public Long getOrgGoodsId() {
        return orgGoodsId;
    }

    public void setOrgGoodsId(Long orgGoodsId) {
        this.orgGoodsId = orgGoodsId;
    }

    public String getCurName() {
        return curName;
    }

    public void setCurName(String curName) {
        this.curName = curName;
    }

    public String getBarCode() {
        return barCode;
    }

    public void setBarCode(String barCode) {
        this.barCode = barCode;
    }

    public String getOpCode() {
        return opCode;
    }

    public void setOpCode(String opCode) {
        this.opCode = opCode;
    }

    public Byte getStatus() {
        return status;
    }

    public void setStatus(Byte status) {
        this.status = status;
    }

    public Date getGmtCreate() {
        return gmtCreate;
    }

    public void setGmtCreate(Date gmtCreate) {
        this.gmtCreate = gmtCreate;
    }

    public Date getGmtUpdate() {
        return gmtUpdate;
    }

    public void setGmtUpdate(Date gmtUpdate) {
        this.gmtUpdate = gmtUpdate;
    }

    public String getExtend() {
        return extend;
    }

    public void setExtend(String extend) {
        this.extend = extend;
    }

    public Integer getVersion() {
        return version;
    }

    public void setVersion(Integer version) {
        this.version = version;
    }

    public Long getCreatedBy() {
        return createdBy;
    }

    public void setCreatedBy(Long createdBy) {
        this.createdBy = createdBy;
    }

    public Long getUpdatedBy() {
        return updatedBy;
    }

    public void setUpdatedBy(Long updatedBy) {
        this.updatedBy = updatedBy;
    }

    public String getUpdatedByName() {
        return updatedByName;
    }

    public void setUpdatedByName(String updatedByName) {
        this.updatedByName = updatedByName;
    }

    public Byte getIsMaintain() {
        return isMaintain;
    }

    public void setIsMaintain(Byte isMaintain) {
        this.isMaintain = isMaintain;
    }

    public String getComment() {
        return comment;
    }

    public void setComment(String comment) {
        this.comment = comment;
    }

    @Override
    public boolean equals(Object that) {
        if (this == that) {
            return true;
        }
        if (that == null) {
            return false;
        }
        if (getClass() != that.getClass()) {
            return false;
        }
        PriceStoreDetail other = (PriceStoreDetail) that;
        return (this.getId() == null ? other.getId() == null : this.getId().equals(other.getId()))
            && (this.getStoreId() == null ? other.getStoreId() == null : this.getStoreId().equals(other.getStoreId()))
            && (this.getStoreName() == null ? other.getStoreName() == null : this.getStoreName().equals(other.getStoreName()))
            && (this.getSpuId() == null ? other.getSpuId() == null : this.getSpuId().equals(other.getSpuId()))
            && (this.getGoodsNo() == null ? other.getGoodsNo() == null : this.getGoodsNo().equals(other.getGoodsNo()))
            && (this.getPriceTypeCode() == null ? other.getPriceTypeCode() == null : this.getPriceTypeCode().equals(other.getPriceTypeCode()))
            && (this.getPriceTypeId() == null ? other.getPriceTypeId() == null : this.getPriceTypeId().equals(other.getPriceTypeId()))
            && (this.getPriceTypeName() == null ? other.getPriceTypeName() == null : this.getPriceTypeName().equals(other.getPriceTypeName()))
            && (this.getPrice() == null ? other.getPrice() == null : this.getPrice().equals(other.getPrice()))
            && (this.getAuthOrgId() == null ? other.getAuthOrgId() == null : this.getAuthOrgId().equals(other.getAuthOrgId()))
            && (this.getAuthOrgName() == null ? other.getAuthOrgName() == null : this.getAuthOrgName().equals(other.getAuthOrgName()))
            && (this.getAuthOrgLevel() == null ? other.getAuthOrgLevel() == null : this.getAuthOrgLevel().equals(other.getAuthOrgLevel()))
            && (this.getOrgId() == null ? other.getOrgId() == null : this.getOrgId().equals(other.getOrgId()))
            && (this.getOrgName() == null ? other.getOrgName() == null : this.getOrgName().equals(other.getOrgName()))
            && (this.getLevel() == null ? other.getLevel() == null : this.getLevel().equals(other.getLevel()))
            && (this.getBusinessId() == null ? other.getBusinessId() == null : this.getBusinessId().equals(other.getBusinessId()))
            && (this.getBusinessName() == null ? other.getBusinessName() == null : this.getBusinessName().equals(other.getBusinessName()))
            && (this.getPlatformId() == null ? other.getPlatformId() == null : this.getPlatformId().equals(other.getPlatformId()))
            && (this.getPlatformName() == null ? other.getPlatformName() == null : this.getPlatformName().equals(other.getPlatformName()))
            && (this.getAdjustCode() == null ? other.getAdjustCode() == null : this.getAdjustCode().equals(other.getAdjustCode()))
            && (this.getAdjustDetailId() == null ? other.getAdjustDetailId() == null : this.getAdjustDetailId().equals(other.getAdjustDetailId()))
            && (this.getOrgGoodsId() == null ? other.getOrgGoodsId() == null : this.getOrgGoodsId().equals(other.getOrgGoodsId()))
            && (this.getCurName() == null ? other.getCurName() == null : this.getCurName().equals(other.getCurName()))
            && (this.getBarCode() == null ? other.getBarCode() == null : this.getBarCode().equals(other.getBarCode()))
            && (this.getOpCode() == null ? other.getOpCode() == null : this.getOpCode().equals(other.getOpCode()))
            && (this.getStatus() == null ? other.getStatus() == null : this.getStatus().equals(other.getStatus()))
            && (this.getGmtCreate() == null ? other.getGmtCreate() == null : this.getGmtCreate().equals(other.getGmtCreate()))
            && (this.getGmtUpdate() == null ? other.getGmtUpdate() == null : this.getGmtUpdate().equals(other.getGmtUpdate()))
            && (this.getExtend() == null ? other.getExtend() == null : this.getExtend().equals(other.getExtend()))
            && (this.getVersion() == null ? other.getVersion() == null : this.getVersion().equals(other.getVersion()))
            && (this.getCreatedBy() == null ? other.getCreatedBy() == null : this.getCreatedBy().equals(other.getCreatedBy()))
            && (this.getUpdatedBy() == null ? other.getUpdatedBy() == null : this.getUpdatedBy().equals(other.getUpdatedBy()))
            && (this.getUpdatedByName() == null ? other.getUpdatedByName() == null : this.getUpdatedByName().equals(other.getUpdatedByName()))
            && (this.getIsMaintain() == null ? other.getIsMaintain() == null : this.getIsMaintain().equals(other.getIsMaintain()))
            && (this.getComment() == null ? other.getComment() == null : this.getComment().equals(other.getComment()));
    }

    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((getId() == null) ? 0 : getId().hashCode());
        result = prime * result + ((getStoreId() == null) ? 0 : getStoreId().hashCode());
        result = prime * result + ((getStoreName() == null) ? 0 : getStoreName().hashCode());
        result = prime * result + ((getSpuId() == null) ? 0 : getSpuId().hashCode());
        result = prime * result + ((getGoodsNo() == null) ? 0 : getGoodsNo().hashCode());
        result = prime * result + ((getPriceTypeCode() == null) ? 0 : getPriceTypeCode().hashCode());
        result = prime * result + ((getPriceTypeId() == null) ? 0 : getPriceTypeId().hashCode());
        result = prime * result + ((getPriceTypeName() == null) ? 0 : getPriceTypeName().hashCode());
        result = prime * result + ((getPrice() == null) ? 0 : getPrice().hashCode());
        result = prime * result + ((getAuthOrgId() == null) ? 0 : getAuthOrgId().hashCode());
        result = prime * result + ((getAuthOrgName() == null) ? 0 : getAuthOrgName().hashCode());
        result = prime * result + ((getAuthOrgLevel() == null) ? 0 : getAuthOrgLevel().hashCode());
        result = prime * result + ((getOrgId() == null) ? 0 : getOrgId().hashCode());
        result = prime * result + ((getOrgName() == null) ? 0 : getOrgName().hashCode());
        result = prime * result + ((getLevel() == null) ? 0 : getLevel().hashCode());
        result = prime * result + ((getBusinessId() == null) ? 0 : getBusinessId().hashCode());
        result = prime * result + ((getBusinessName() == null) ? 0 : getBusinessName().hashCode());
        result = prime * result + ((getPlatformId() == null) ? 0 : getPlatformId().hashCode());
        result = prime * result + ((getPlatformName() == null) ? 0 : getPlatformName().hashCode());
        result = prime * result + ((getAdjustCode() == null) ? 0 : getAdjustCode().hashCode());
        result = prime * result + ((getAdjustDetailId() == null) ? 0 : getAdjustDetailId().hashCode());
        result = prime * result + ((getOrgGoodsId() == null) ? 0 : getOrgGoodsId().hashCode());
        result = prime * result + ((getCurName() == null) ? 0 : getCurName().hashCode());
        result = prime * result + ((getBarCode() == null) ? 0 : getBarCode().hashCode());
        result = prime * result + ((getOpCode() == null) ? 0 : getOpCode().hashCode());
        result = prime * result + ((getStatus() == null) ? 0 : getStatus().hashCode());
        result = prime * result + ((getGmtCreate() == null) ? 0 : getGmtCreate().hashCode());
        result = prime * result + ((getGmtUpdate() == null) ? 0 : getGmtUpdate().hashCode());
        result = prime * result + ((getExtend() == null) ? 0 : getExtend().hashCode());
        result = prime * result + ((getVersion() == null) ? 0 : getVersion().hashCode());
        result = prime * result + ((getCreatedBy() == null) ? 0 : getCreatedBy().hashCode());
        result = prime * result + ((getUpdatedBy() == null) ? 0 : getUpdatedBy().hashCode());
        result = prime * result + ((getUpdatedByName() == null) ? 0 : getUpdatedByName().hashCode());
        result = prime * result + ((getIsMaintain() == null) ? 0 : getIsMaintain().hashCode());
        result = prime * result + ((getComment() == null) ? 0 : getComment().hashCode());
        return result;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", storeId=").append(storeId);
        sb.append(", storeName=").append(storeName);
        sb.append(", spuId=").append(spuId);
        sb.append(", goodsNo=").append(goodsNo);
        sb.append(", priceTypeCode=").append(priceTypeCode);
        sb.append(", priceTypeId=").append(priceTypeId);
        sb.append(", priceTypeName=").append(priceTypeName);
        sb.append(", price=").append(price);
        sb.append(", authOrgId=").append(authOrgId);
        sb.append(", authOrgName=").append(authOrgName);
        sb.append(", authOrgLevel=").append(authOrgLevel);
        sb.append(", orgId=").append(orgId);
        sb.append(", orgName=").append(orgName);
        sb.append(", level=").append(level);
        sb.append(", businessId=").append(businessId);
        sb.append(", businessName=").append(businessName);
        sb.append(", platformId=").append(platformId);
        sb.append(", platformName=").append(platformName);
        sb.append(", adjustCode=").append(adjustCode);
        sb.append(", adjustDetailId=").append(adjustDetailId);
        sb.append(", orgGoodsId=").append(orgGoodsId);
        sb.append(", curName=").append(curName);
        sb.append(", barCode=").append(barCode);
        sb.append(", opCode=").append(opCode);
        sb.append(", status=").append(status);
        sb.append(", gmtCreate=").append(gmtCreate);
        sb.append(", gmtUpdate=").append(gmtUpdate);
        sb.append(", extend=").append(extend);
        sb.append(", version=").append(version);
        sb.append(", createdBy=").append(createdBy);
        sb.append(", updatedBy=").append(updatedBy);
        sb.append(", updatedByName=").append(updatedByName);
        sb.append(", isMaintain=").append(isMaintain);
        sb.append(", comment=").append(comment);
        sb.append(", serialVersionUID=").append(serialVersionUID);
        sb.append("]");
        return sb.toString();
    }
}
