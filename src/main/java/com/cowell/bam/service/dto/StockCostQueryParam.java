package com.cowell.bam.service.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;

@Data
public class StockCostQueryParam implements Serializable {


    @ApiModelProperty(name = "storeId", value = "门店id", required = true)
    @NotNull(message = "门店id不能为空")
    private Long storeId;

    @ApiModelProperty(name = "skuMerchantCode", value = "商品编码", required = true)
    @NotNull(message = "商品编码不能为空")
    private List<String> skuMerchantCodeList;

}
