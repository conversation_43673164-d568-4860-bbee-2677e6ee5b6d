package com.cowell.bam.service.dto;

import lombok.Getter;
import lombok.Setter;
import org.apache.commons.lang.builder.ToStringBuilder;

/**
 * <AUTHOR>
 * @Description: 统一发送erp请求的参数
 * @date 2018-10-14 14:37
 */
@Setter
@Getter
public class SendRequestBodyDTO {

    /**
     * 操作数据主键id
     */
    private Long dataPrimaryId;

    /**
     * 调用第三方erp请求需要传的请求body体
     */
    private String requestBody;

    private String userName;

    private String passWord;

    private String requestUrl;

    /**
     * 请求模式(POST or GET)
     */
    private String requestMode;

    /**
     * 请求来源
     */
    private String source;

    /**
     * 发送目标
     */
    private String destination;

    /**
     * 重试任务的类型
     */
    private String retrySystem;

    /**
     * 经过metaq重试失败之后在库里插入一条失败的记录，同时生成的主键id
     */
    private Long retryDataPrimaryId;

    /**
     * 接口服务描述
     */
    private String serviceDesc;

    /**
     * 应用名称
     */
    private String appName;

    /**
     * erp类型
     */
    private String erpType;

    /**
     * 具体操作
     */
    private String action;

    /**
     * 请求数据类型(syncPrice,syncStock,syncProduct,syncStore)
     */
    private String dataType;


    /**
     * 连锁id
     */
    private Long businessId;

    /**
     * 业务类型数据
     */
    private Integer type;


    /**
     * 限流
     */
    private Integer limit;

    /**
     * 请求的bguid
     */
    private String requestBguid;

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    public Long getDataPrimaryId() {
        return dataPrimaryId;
    }

    public void setDataPrimaryId(Long dataPrimaryId) {
        this.dataPrimaryId = dataPrimaryId;
    }

    public String getRequestBody() {
        return requestBody;
    }

    public void setRequestBody(String requestBody) {
        this.requestBody = requestBody;
    }

    public String getUserName() {
        return userName;
    }

    public void setUserName(String userName) {
        this.userName = userName;
    }

    public String getPassWord() {
        return passWord;
    }

    public void setPassWord(String passWord) {
        this.passWord = passWord;
    }

    public String getRequestUrl() {
        return requestUrl;
    }

    public void setRequestUrl(String requestUrl) {
        this.requestUrl = requestUrl;
    }

    public String getRequestMode() {
        return requestMode;
    }

    public void setRequestMode(String requestMode) {
        this.requestMode = requestMode;
    }

    public String getSource() {
        return source;
    }

    public void setSource(String source) {
        this.source = source;
    }

    public String getDestination() {
        return destination;
    }

    public void setDestination(String destination) {
        this.destination = destination;
    }

    public String getRetrySystem() {
        return retrySystem;
    }

    public void setRetrySystem(String retrySystem) {
        this.retrySystem = retrySystem;
    }

    public Long getRetryDataPrimaryId() {
        return retryDataPrimaryId;
    }

    public void setRetryDataPrimaryId(Long retryDataPrimaryId) {
        this.retryDataPrimaryId = retryDataPrimaryId;
    }

    public String getServiceDesc() {
        return serviceDesc;
    }

    public void setServiceDesc(String serviceDesc) {
        this.serviceDesc = serviceDesc;
    }

    public String getAppName() {
        return appName;
    }

    public void setAppName(String appName) {
        this.appName = appName;
    }

    public String getErpType() {
        return erpType;
    }

    public void setErpType(String erpType) {
        this.erpType = erpType;
    }

    public String getAction() {
        return action;
    }

    public void setAction(String action) {
        this.action = action;
    }

    public String getDataType() {
        return dataType;
    }

    public void setDataType(String dataType) {
        this.dataType = dataType;
    }

    public Integer getLimit() {
        return limit;
    }

    public void setLimit(Integer limit) {
        this.limit = limit;
    }

    public Long getBusinessId() {
        return businessId;
    }

    public void setBusinessId(Long businessId) {
        this.businessId = businessId;
    }

    public String getRequestBguid() {
        return requestBguid;
    }

    public void setRequestBguid(String requestBguid) {
        this.requestBguid = requestBguid;
    }

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this);
    }
}
