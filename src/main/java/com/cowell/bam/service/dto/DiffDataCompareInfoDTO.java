package com.cowell.bam.service.dto;

import java.io.Serializable;
import java.util.Date;

public class DiffDataCompareInfoDTO implements Serializable {
    private Long id;

    private Long businessId;

    private String businessName;

    private Long storeId;

    private String storeName;

    private String goodsNo;

    private String thirdData;

    private String ourData;

    private Long userId;

    private Integer dataType;

    private String reason;

    private Integer status;

    private Integer version;

    private String createdBy;


    private String gmtCreateStr;

    private Date gmtCreate;

    private String updatedBy;

    private Date gmtUpdate;

    private String extend;

    private static final long serialVersionUID = 1L;

    public String getGmtCreateStr() {
        return gmtCreateStr;
    }

    public void setGmtCreateStr(String gmtCreateStr) {
        this.gmtCreateStr = gmtCreateStr;
    }

    public String getThirdData() {
        return thirdData;
    }

    public void setThirdData(String thirdData) {
        this.thirdData = thirdData;
    }

    public String getOurData() {
        return ourData;
    }

    public void setOurData(String ourData) {
        this.ourData = ourData;
    }

    public String getBusinessName() {
        return businessName;
    }

    public void setBusinessName(String businessName) {
        this.businessName = businessName;
    }

    public String getStoreName() {
        return storeName;
    }

    public void setStoreName(String storeName) {
        this.storeName = storeName;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getBusinessId() {
        return businessId;
    }

    public void setBusinessId(Long businessId) {
        this.businessId = businessId;
    }

    public Long getStoreId() {
        return storeId;
    }

    public void setStoreId(Long storeId) {
        this.storeId = storeId;
    }

    public String getGoodsNo() {
        return goodsNo;
    }

    public void setGoodsNo(String goodsNo) {
        this.goodsNo = goodsNo == null ? null : goodsNo.trim();
    }

    public Long getUserId() {
        return userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }

    public Integer getDataType() {
        return dataType;
    }

    public void setDataType(Integer dataType) {
        this.dataType = dataType;
    }

    public String getReason() {
        return reason;
    }

    public void setReason(String reason) {
        this.reason = reason == null ? null : reason.trim();
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public Integer getVersion() {
        return version;
    }

    public void setVersion(Integer version) {
        this.version = version;
    }

    public String getCreatedBy() {
        return createdBy;
    }

    public void setCreatedBy(String createdBy) {
        this.createdBy = createdBy == null ? null : createdBy.trim();
    }

    public Date getGmtCreate() {
        return gmtCreate;
    }

    public void setGmtCreate(Date gmtCreate) {
        this.gmtCreate = gmtCreate;
    }

    public String getUpdatedBy() {
        return updatedBy;
    }

    public void setUpdatedBy(String updatedBy) {
        this.updatedBy = updatedBy == null ? null : updatedBy.trim();
    }

    public Date getGmtUpdate() {
        return gmtUpdate;
    }

    public void setGmtUpdate(Date gmtUpdate) {
        this.gmtUpdate = gmtUpdate;
    }

    public String getExtend() {
        return extend;
    }

    public void setExtend(String extend) {
        this.extend = extend == null ? null : extend.trim();
    }

    @Override
    public String toString() {
        return "DiffDataCompareInfoDTO{" +
            "id=" + id +
            ", businessId=" + businessId +
            ", businessName='" + businessName + '\'' +
            ", storeId=" + storeId +
            ", storeName='" + storeName + '\'' +
            ", goodsNo='" + goodsNo + '\'' +
            ", thirdData='" + thirdData + '\'' +
            ", ourData='" + ourData + '\'' +
            ", userId=" + userId +
            ", dataType=" + dataType +
            ", reason='" + reason + '\'' +
            ", status=" + status +
            ", version=" + version +
            ", createdBy='" + createdBy + '\'' +
            ", gmtCreate=" + gmtCreate +
            ", updatedBy='" + updatedBy + '\'' +
            ", gmtUpdate=" + gmtUpdate +
            ", extend='" + extend + '\'' +
            '}';
    }
}
