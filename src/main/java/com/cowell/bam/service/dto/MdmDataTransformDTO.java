package com.cowell.bam.service.dto;

import java.util.List;

/**
 * @description : MDM数据转换
 */
public class MdmDataTransformDTO {

    /**
     * 高济连锁企业编号
     */
    private String businessId;

    /**
     * 高济连锁门店id
     */
    private List<Long> storeIds;

    /**
     * MDM连锁企业编号
     */
    private String comId;

    /**
     * MDM门店编号
     */
    private List<String> storeNos;

    /**
     * 类型：1-连锁企业 2-门店
     */
    private Integer dataType;

    /**
     * 转换类型：1-高济转为MDM 2-MDM转为门店
     */
    private Integer transFormType;

    public String getBusinessId() {
        return businessId;
    }

    public void setBusinessId(String businessId) {
        this.businessId = businessId;
    }

    public List<Long> getStoreIds() {
        return storeIds;
    }

    public void setStoreIds(List<Long> storeIds) {
        this.storeIds = storeIds;
    }

    public String getComId() {
        return comId;
    }

    public void setComId(String comId) {
        this.comId = comId;
    }

    public List<String> getStoreNos() {
        return storeNos;
    }

    public void setStoreNos(List<String> storeNos) {
        this.storeNos = storeNos;
    }

    public Integer getDataType() {
        return dataType;
    }

    public void setDataType(Integer dataType) {
        this.dataType = dataType;
    }

    public Integer getTransFormType() {
        return transFormType;
    }

    public void setTransFormType(Integer transFormType) {
        this.transFormType = transFormType;
    }

    @Override
    public String toString() {
        return "MdmDataTransformDTO{" +
                "businessId=" + businessId +
                ", storeIds=" + storeIds +
                ", comId='" + comId + '\'' +
                ", storeNos=" + storeNos +
                ", dataType=" + dataType +
                ", transFormType=" + transFormType +
                '}';
    }
}
