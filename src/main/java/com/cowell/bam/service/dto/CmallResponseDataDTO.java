package com.cowell.bam.service.dto;

import java.io.Serializable;

public class CmallResponseDataDTO implements Serializable {

    private String compid;
    private String message;
    private String code;
    private String respDate;

    public String getCompid() {
        return compid;
    }

    public void setCompid(String compid) {
        this.compid = compid;
    }

    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getRespDate() {
        return respDate;
    }

    public void setRespDate(String respDate) {
        this.respDate = respDate;
    }

    @Override
    public String toString() {
        return "CmallResponseDataDTO{" +
            "compid='" + compid + '\'' +
            ", message='" + message + '\'' +
            ", code='" + code + '\'' +
            ", respDate='" + respDate + '\'' +
            '}';
    }
}
