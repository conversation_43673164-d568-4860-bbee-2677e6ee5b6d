package com.cowell.bam.service.dto;


import com.alibaba.fastjson.JSONObject;
import javax.validation.constraints.Max;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.io.Serializable;
import java.util.Objects;

/**
 * A DTO for the BusinessInfo entity.
 */
public class BusinessInfoDTO  {

    private Long id;

    @NotNull
    @Size(max = 128)
    private String businessName;

    @Size(max = 32)
    private String shortName;

    @Size(max = 16)
    private String organCode;

    @Size(max = 3000)
    private String extend;

    /**
     * 企业激活会员数
     */
    private Long activeCount;

    @Max(value = 999999999)
    private Integer version;

    private Integer status;

    /**
     * 当前连锁下门店数
     * */
    private Integer storeNum;
    /**
     * 当前连锁下截至目前会员数
     * */
    private Long memberNum;
    /**
     * 连锁logo地址
     * */
    private String logoUrl;
    /**
     * 类型（0：连锁；1：企业）
     */
    private Integer businessType;
    /**
     * 企业所属连锁
     */
    private Long belongTo;


    public Long getBelongTo() {
        return belongTo;
    }

    public void setBelongTo(Long belongTo) {
        this.belongTo = belongTo;
    }



    public Integer getBusinessType() {
        return businessType;
    }

    public void analysisExtend() {

    }

    public void setBusinessType(Integer businessType) {
        this.businessType = businessType;
    }

    public Integer getStoreNum() {
        return storeNum;
    }

    public void setStoreNum(Integer storeNum) {
        this.storeNum = storeNum;
    }

    public Long getMemberNum() {
        return memberNum;
    }

    public void setMemberNum(Long memberNum) {
        this.memberNum = memberNum;
    }

    public String getLogoUrl() {
        return logoUrl;
    }

    public Long getActiveCount() {
        return activeCount;
    }

    public void setActiveCount(Long activeCount) {
        this.activeCount = activeCount;
    }

    public void setLogoUrl(String logoUrl) {
        this.logoUrl = logoUrl;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getBusinessName() {
        return businessName;
    }

    public void setBusinessName(String businessName) {
        this.businessName = businessName;
    }

    public String getShortName() {
        return shortName;
    }

    public void setShortName(String shortName) {
        this.shortName = shortName;
    }

    public String getOrganCode() {
        return organCode;
    }

    public void setOrganCode(String organCode) {
        this.organCode = organCode;
    }

    public String getExtend() {
        return extend;
    }



    public Integer getVersion() {
        return version;
    }

    public void setVersion(Integer version) {
        this.version = version;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }

        BusinessInfoDTO businessInfoDTO = (BusinessInfoDTO) o;
        if(businessInfoDTO.getId() == null || getId() == null) {
            return false;
        }
        return Objects.equals(getId(), businessInfoDTO.getId());
    }

    @Override
    public int hashCode() {
        return Objects.hashCode(getId());
    }



    @Override
    public String toString() {
        return "BusinessInfoDTO{" +
            "id=" + getId() +
            ", businessName='" + getBusinessName() + "'" +
            ", shortName='" + getShortName() + "'" +
            ", organCode='" + getOrganCode() + "'" +
            ", extend='" + getExtend() + "'" +
            ", version=" + getVersion() +
                ", activeCount='" + getActiveCount() + "'" +
                ", businessType='" + getBusinessType() + "'" +
                ", belongTo='" + getBelongTo() + "'" +
                ", storeNum='" + getStoreNum() + "'" +
                ", MemberNum='" + getMemberNum() + "'" +
                ", logoUrl='" + getLogoUrl() + "'" +
            "}";
    }
}
