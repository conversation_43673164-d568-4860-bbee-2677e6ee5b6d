package com.cowell.bam.service.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class BangJianViewDTO {
    @ApiModelProperty(name = "企业编号")
    private String compId;
    @ApiModelProperty(name = "使用人门店编码")
    private String useBusNos;
    @ApiModelProperty(name = "券码")
    private String couPomNum;
    @ApiModelProperty(name = "bj优惠券id")
    private String couponManagId;
    @ApiModelProperty(name = "店员id")
    private String payee;
    @ApiModelProperty(name = "使用单号")
    private String useSaleNo;
    @ApiModelProperty(name = "会员卡号")
    private String useMemCardNo;
    @ApiModelProperty(name = "使用时间")
    private String useDate;
}
