package com.cowell.bam.service.dto;

import lombok.Data;

import java.io.Serializable;

/**
 * bam
 * 2020/9/14 14:24
 *
 * <AUTHOR>
 * @since
 **/
@Data
public class ThirdPlatformStockDetailDTO implements Serializable {

    /**
     * 明细行号（一单每条明细唯一）
     */
    private Integer rowNo;

    /**
     * 商品sku（MDM商品编码）
     */
    private String wareCode;

    @Override
    public String toString() {
        return "ThirdPlatformStockDetailDTO{" +
            "rowNo=" + rowNo +
            ", wareCode='" + wareCode + '\'' +
            '}';
    }
}
