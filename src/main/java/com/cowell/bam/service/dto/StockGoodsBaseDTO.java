package com.cowell.bam.service.dto;

import io.swagger.annotations.ApiModelProperty;

import javax.validation.constraints.NotNull;


public class StockGoodsBaseDTO {
    @ApiModelProperty(name = "businessId", value = "连锁id", required = true)
    @NotNull(message = "连锁ID不能为空")
    private Long businessId;

    @ApiModelProperty(name = "storeId", value = "门店id", required = true)
    @NotNull(message = "门店id不能为空")
    private Long storeId;

    public Long getBusinessId() {
        return businessId;
    }

    public void setBusinessId(Long businessId) {
        this.businessId = businessId;
    }

    public Long getStoreId() {
        return storeId;
    }

    public void setStoreId(Long storeId) {
        this.storeId = storeId;
    }

    @Override
    public String toString() {
        return "StockGoodsBaseDTO{" +
            "businessId=" + businessId +
            ", storeId=" + storeId +
            '}';
    }
}
