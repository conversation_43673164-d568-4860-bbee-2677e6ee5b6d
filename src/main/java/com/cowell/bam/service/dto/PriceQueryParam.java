package com.cowell.bam.service.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.util.List;

/**
 * 获取特定商品所有价格
 *
 * @author: liubw
 * @date 2018/10/30 4:12 PM
 */
@Setter
@Getter
@ToString
public class PriceQueryParam {

    @ApiModelProperty("连锁ID")
    private Long businessId;

    @ApiModelProperty("门店ID")
    private Long storeId;

    private List<PriceQueryDetailParam> queryNoList;
    private String adjustCode;

    @ApiModelProperty("商品编码")
    private String goodsNo;

    @ApiModelProperty("商品价格类型编码")
    private String priceTypeCode;

    @ApiModelProperty("商品编码集合")
    private List<String> goodsNoList;

    @ApiModelProperty("价格渠道")
    private Integer channelId = 0;

    @ApiModelProperty("当前页")
    private Integer page = 0;

    @ApiModelProperty("每页数量")
    private Integer pageSize = 50;

}
