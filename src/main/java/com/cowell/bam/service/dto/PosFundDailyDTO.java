package com.cowell.bam.service.dto;

import org.apache.commons.lang3.builder.ToStringBuilder;

import java.io.Serializable;

/**
 * <AUTHOR> <EMAIL>
 * @date : 2020/07/23 14:17
 * @description :
 */
public class PosFundDailyDTO implements Serializable {

    private static final long serialVersionUID = -6769114383221591383L;

    /**
     * 企业ID
     */
    private String compid;

    /**
     * 卡号
     */
    private String memcardno;

    /**
     * 对账日期
     */
    private String lasttime;

    /**
     * 用户ID
     */
    private Long userid;

    /**
     * 手机号
     */
    private String phone;

    /**
     * 累计增加
     */
    private String fundaddtotal;

    /**
     * 累计减少
     */
    private String fundminustotal;

    public String getCompid() {
        return compid;
    }

    public void setCompid(String compid) {
        this.compid = compid;
    }

    public String getMemcardno() {
        return memcardno;
    }

    public void setMemcardno(String memcardno) {
        this.memcardno = memcardno;
    }

    public String getLasttime() {
        return lasttime;
    }

    public void setLasttime(String lasttime) {
        this.lasttime = lasttime;
    }

    public Long getUserid() {
        return userid;
    }

    public void setUserid(Long userid) {
        this.userid = userid;
    }

    public String getPhone() {
        return phone;
    }

    public void setPhone(String phone) {
        this.phone = phone;
    }

    public String getFundaddtotal() {
        return fundaddtotal;
    }

    public void setFundaddtotal(String fundaddtotal) {
        this.fundaddtotal = fundaddtotal;
    }

    public String getFundminustotal() {
        return fundminustotal;
    }

    public void setFundminustotal(String fundminustotal) {
        this.fundminustotal = fundminustotal;
    }

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this);
    }
}
