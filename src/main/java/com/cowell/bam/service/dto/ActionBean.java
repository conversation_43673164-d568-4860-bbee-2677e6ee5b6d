package com.cowell.bam.service.dto;

import lombok.Data;

import java.io.Serializable;

/**
 * bam
 * 2020/9/14 13:55
 *
 * <AUTHOR>
 * @since
 **/
@Data
public class ActionBean implements Serializable {
    /**
     * url : http://api-stage-internal.gaojihealth.cn/message-bus/api/noauth/external/RESTAdapter/v1_0/DLC/SP
     * methodType : POST
     * action : createPosPurPg
     * limit : 50
     * isAuth : y
     * requetMode : 1
     * erpType : sap
     * desc : 推送店采订单
     * btype : 5001
     * bsource : 603
     * bdestination : 101
     * bDataType : json
     * businessHandlerType : sapRfcHandler
     */
    /**
     * mb　url
     */
    private String url;
    /**
     * 请求类型
     */
    private String methodType;
    /**
     * 请求业务
     */
    private String action;

    private int limit;
    private String isAuth;
    private String requetMode;
    private String erpType;
    private String desc;
    private String btype;
    private String bsource;
    private String bdestination;
    private String bDataType;
    private String businessHandlerType;

}
