package com.cowell.bam.service.dto;

import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2022/5/9 10:20
 */
@Data
public class PricePushDTO {
    /**
     * 连锁id
     */
    private Long businessId;
    /**
     * 门店id
     */
    private Long storeId;

    /**
     * 连锁下商品唯一id
     */
    private String goodsNo;

    /**
     * 商品价格
     */
    private BigDecimal price;

    /**
     * POS价格
     */
    private BigDecimal posPrice;

    /**
     * 价格类型
     */
    private String priceTypeCode;

    /**
     * 价格渠道
     */
    private Integer channelId;

    /**
     * 原因
     */
    private String reason;

    /**
     * 是否特价 0否   1是
     */
    private Integer isSpecial;

    /**
     * 门店编码 目前导出前设置
     */
    private String storeNo;
}
