package com.cowell.bam.service.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

public class ThridStockInfoDTO{
    @ApiModelProperty(name = "企业编号")
    String compId;
    @ApiModelProperty(name = "门店编码")
    String busNo;
    @ApiModelProperty(name = "商品编码")
    String wareCode;
    @ApiModelProperty(name = "批号")
    String makeNo;
    @ApiModelProperty(name = "生产批次")
    String batchNo;

    @ApiModelProperty(name = "总数量")
    String tNum;
    @ApiModelProperty(name = "可卖数量")
    String num;
    @ApiModelProperty(name = "transitStock", value = "在途库存")
    private String transitStock;

    /**
     * 门店名称
     */
    private String storeName;

    public String getStoreName() {
        return storeName;
    }

    public void setStoreName(String storeName) {
        this.storeName = storeName;
    }

    public String getTransitStock() {
        return transitStock;
    }

    public void setTransitStock(String transitStock) {
        this.transitStock = transitStock;
    }

    public String getCompId() {
        return compId;
    }

    public void setCompId(String compId) {
        this.compId = compId;
    }

    public String getBusNo() {
        return busNo;
    }

    public void setBusNo(String busNo) {
        this.busNo = busNo;
    }

    public String getWareCode() {
        return wareCode;
    }

    public void setWareCode(String wareCode) {
        this.wareCode = wareCode;
    }

    public String getMakeNo() {
        return makeNo;
    }

    public void setMakeNo(String makeNo) {
        this.makeNo = makeNo;
    }

    public String getBatchNo() {
        return batchNo;
    }

    public void setBatchNo(String batchNo) {
        this.batchNo = batchNo;
    }

    public String gettNum() {
        return tNum;
    }

    public void settNum(String tNum) {
        this.tNum = tNum;
    }

    public String getNum() {
        return num;
    }

    public void setNum(String num) {
        this.num = num;
    }

    @Override
    public String toString() {
        return "ThridStockInfoDTO{" +
            "compId='" + compId + '\'' +
            ", busNo='" + busNo + '\'' +
            ", wareCode='" + wareCode + '\'' +
            ", makeNo='" + makeNo + '\'' +
            ", batchNo='" + batchNo + '\'' +
            ", tNum='" + tNum + '\'' +
            ", num='" + num + '\'' +
            '}';
    }
}
