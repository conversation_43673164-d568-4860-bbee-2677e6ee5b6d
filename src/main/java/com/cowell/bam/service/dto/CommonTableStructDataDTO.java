package com.cowell.bam.service.dto;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonSetter;
import org.apache.commons.lang.builder.ToStringBuilder;

import java.util.List;

/**
 * <AUTHOR>
 * @Description erp推送数据统一接收dto
 * @date 28-9-25 4;49
 */

public class CommonTableStructDataDTO<K, V> {

    private List<CommonTableInnerDataDTO<K, V>> Table;

    @JSONField(name = "Table")
    public List<CommonTableInnerDataDTO<K, V>> getTable() {
        return Table;
    }

    @JsonSetter(value = "Table")
    public void setTable(List<CommonTableInnerDataDTO<K, V>> table) {
        Table = table;
    }



    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this);
    }


}
