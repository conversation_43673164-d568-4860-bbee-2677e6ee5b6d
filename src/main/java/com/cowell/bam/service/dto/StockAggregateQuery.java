package com.cowell.bam.service.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/8/25 11:20
 */
@Data
public class StockAggregateQuery implements Serializable {

    private static final long serialVersionUID = -6522610650625844137L;

    @ApiModelProperty("页码")
    private int page = 1;

    @ApiModelProperty("每页条数")
    private int size = 10;

    @ApiModelProperty(value = "生产批号")
    private String batchNo;

    @ApiModelProperty(value = "批次号")
    private String batchCode;

    @ApiModelProperty(value = "库存地类型")
    private String location;

    @ApiModelProperty("商品编码")
    private List<String> goodsNos;

    @ApiModelProperty("商品编码")
    @JsonProperty("GOODSNOS")
    private List<String> goodsNosUppercase;

    @ApiModelProperty(value = "连锁id")
    private Long businessId;

    @ApiModelProperty(value = "门店id")
    private Long storeId;

    @ApiModelProperty(value = "MDM连锁编码")
    @JsonProperty("COMID")
    private String comId;

    @ApiModelProperty(value = "MDM门店编码")
    @JsonProperty("STORENO")
    private String storeNo;


}
