package com.cowell.bam.service.dto;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @Description: 调用海典，英克，sap，统一请求体封装
 * @date 2018-09-10 16:43
 */
@Data
public class RequestBodyDTO implements Serializable {

    /**
     * app各个业务方应用名称
     */
    private String appName;


    /**
     * 各个业务方请求操作例如订单的叫(addOrder)
     */
    private String action;

    /**
     * 请求类型(hd,yk,sap)
     */
    private String erpType;

    /**
     *请求类型
     */
    private String methodType;


    /**
     *是否需要的登录
     */
    private String isAuth;


    /**
     * 海典，英克，sap，请求url
     */
    private String requestUrl;

    /**
     * 回调地址
     */
    private String bcallback;

    /**
     * 数据重复校验字符串
     * 使用data字段里的JSON计算MD5值填充此字段。发送系统生成MD5，
     * 接收系统单纯校验MD5是否重复，不用再生成MD5与发送系统MD5比对。
     */
    private String bdatahash;

    /**
     * 业务查询使用的关键字段
     * JSON格式
     */
    private String bkeys;

    /**
     * 业务双方确定的业务数据JSON结构
     * JSON格式。发送时传业务数据，接收时传处理结果
     */
    private String bdata;

    private String businessId;

    private String bguid;

    /**
     * 批量数据
     */
    private List<String> bdatas;

    /**
     * 内部请求参数
     */
    private InnerRequestParamDTO innerRequestParamDTO;

    /**
     * 批量数据
     */
    private List<String> bkeyss;


}
