package com.cowell.bam.service.dto;

import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;

public class CmallStockRequestDTO implements Serializable {
    @ApiModelProperty(name = "企业编号")
    String compId;
    @ApiModelProperty(name = "门店编码")
    String busNo;
    @ApiModelProperty(name = "商品编码")
    String wareCode;
    @ApiModelProperty(name = "批号")
    String makeNo;
    @ApiModelProperty(name = "生产批次")
    String batchNo;

    @ApiModelProperty(name = "总数量")
    String tNum;
    @ApiModelProperty(name = "可卖数量")
    String num;
    @ApiModelProperty(name = "拆零数量")
    String pieceNum;
    @ApiModelProperty(name = "消息发送时间 格式：YYYY-MM-DD HH:mm:ss")
    String syncDate;

    @ApiModelProperty(name = "生产日期")
    private String produceDate;

    @ApiModelProperty(name = "有效期")
    private String expireDate;

    @ApiModelProperty(name = "单位")
    private String unit;

    @ApiModelProperty(name = "合格品区库存")
    String qualifiedStock;
    @ApiModelProperty(name = "合格品区医保库存")
    String medicalStock;

    @ApiModelProperty(name = "合格品区医保锁定库存")
    String medicalLockStock;

    @ApiModelProperty(name = "合格品区门市库存")
    String retailStock;

    @ApiModelProperty(name = "合格品区门市锁定库存")
    String retailLockStock;

    @ApiModelProperty(name = "非合格品区库存")
    String unqualifiedStock;
    @ApiModelProperty(name = "待验收区库存")
    String checkStock;
    @ApiModelProperty(name = "合格品区锁定库存")
    String qualifiedLockStock;
    @ApiModelProperty(name = "非合格品区锁定库存")
    String unQualifiedLockStock;

    //兼容老数据
    @ApiModelProperty(name = "消息发送时间格式：YYYY-MM-DD HH:mm:ss")
    String bdatetime;

    private boolean sync2ThirdPlatform = true;

    /**
     * 来源系统  100 海典  200 英克  300 sap
     */
    private String sysType;

    public String getSysType() {
        return sysType;
    }

    public void setSysType(String sysType) {
        this.sysType = sysType;
    }

    public String getCompId() {
        return compId;
    }

    public void setCompId(String compId) {
        this.compId = compId;
    }

    public String getBusNo() {
        return busNo;
    }

    public void setBusNo(String busNo) {
        this.busNo = busNo;
    }

    public String getWareCode() {
        return wareCode;
    }

    public void setWareCode(String wareCode) {
        this.wareCode = wareCode;
    }

    public String getMakeNo() {
        return makeNo;
    }

    public void setMakeNo(String makeNo) {
        this.makeNo = makeNo;
    }

    public String getBatchNo() {
        return batchNo;
    }

    public void setBatchNo(String batchNo) {
        this.batchNo = batchNo;
    }

    public String gettNum() {
        return tNum;
    }

    public void settNum(String tNum) {
        this.tNum = tNum;
    }

    public String getNum() {
        return num;
    }

    public void setNum(String num) {
        this.num = num;
    }

    public String getPieceNum() {
        return pieceNum;
    }

    public void setPieceNum(String pieceNum) {
        this.pieceNum = pieceNum;
    }

    public String getSyncDate() {
        return syncDate;
    }

    public void setSyncDate(String syncDate) {
        this.syncDate = syncDate;
    }

    public String getProduceDate() {
        return produceDate;
    }

    public void setProduceDate(String produceDate) {
        this.produceDate = produceDate;
    }

    public String getExpireDate() {
        return expireDate;
    }

    public void setExpireDate(String expireDate) {
        this.expireDate = expireDate;
    }

    public String getUnit() {
        return unit;
    }

    public void setUnit(String unit) {
        this.unit = unit;
    }

    public String getQualifiedStock() {
        return qualifiedStock;
    }

    public void setQualifiedStock(String qualifiedStock) {
        this.qualifiedStock = qualifiedStock;
    }

    public String getMedicalStock() {
        return medicalStock;
    }

    public void setMedicalStock(String medicalStock) {
        this.medicalStock = medicalStock;
    }

    public String getMedicalLockStock() {
        return medicalLockStock;
    }

    public void setMedicalLockStock(String medicalLockStock) {
        this.medicalLockStock = medicalLockStock;
    }

    public String getRetailStock() {
        return retailStock;
    }

    public void setRetailStock(String retailStock) {
        this.retailStock = retailStock;
    }

    public String getRetailLockStock() {
        return retailLockStock;
    }

    public void setRetailLockStock(String retailLockStock) {
        this.retailLockStock = retailLockStock;
    }

    public String getUnqualifiedStock() {
        return unqualifiedStock;
    }

    public void setUnqualifiedStock(String unqualifiedStock) {
        this.unqualifiedStock = unqualifiedStock;
    }

    public String getCheckStock() {
        return checkStock;
    }

    public void setCheckStock(String checkStock) {
        this.checkStock = checkStock;
    }

    public String getQualifiedLockStock() {
        return qualifiedLockStock;
    }

    public void setQualifiedLockStock(String qualifiedLockStock) {
        this.qualifiedLockStock = qualifiedLockStock;
    }

    public String getUnQualifiedLockStock() {
        return unQualifiedLockStock;
    }

    public void setUnQualifiedLockStock(String unQualifiedLockStock) {
        this.unQualifiedLockStock = unQualifiedLockStock;
    }

    public String getBdatetime() {
        return bdatetime;
    }

    public void setBdatetime(String bdatetime) {
        this.bdatetime = bdatetime;
    }

    public boolean isSync2ThirdPlatform() {
        return sync2ThirdPlatform;
    }

    public void setSync2ThirdPlatform(boolean sync2ThirdPlatform) {
        this.sync2ThirdPlatform = sync2ThirdPlatform;
    }

    @Override
    public String toString() {
        return "CmallStockRequestDTO{" +
            "compId='" + compId + '\'' +
            ", busNo='" + busNo + '\'' +
            ", wareCode='" + wareCode + '\'' +
            ", makeNo='" + makeNo + '\'' +
            ", batchNo='" + batchNo + '\'' +
            ", tNum='" + tNum + '\'' +
            ", num='" + num + '\'' +
            ", pieceNum='" + pieceNum + '\'' +
            ", syncDate='" + syncDate + '\'' +
            ", produceDate='" + produceDate + '\'' +
            ", expireDate='" + expireDate + '\'' +
            ", unit='" + unit + '\'' +
            ", qualifiedStock='" + qualifiedStock + '\'' +
            ", medicalStock='" + medicalStock + '\'' +
            ", medicalLockStock='" + medicalLockStock + '\'' +
            ", retailStock='" + retailStock + '\'' +
            ", retailLockStock='" + retailLockStock + '\'' +
            ", unqualifiedStock='" + unqualifiedStock + '\'' +
            ", checkStock='" + checkStock + '\'' +
            ", qualifiedLockStock='" + qualifiedLockStock + '\'' +
            ", unQualifiedLockStock='" + unQualifiedLockStock + '\'' +
            ", bdatetime='" + bdatetime + '\'' +
            ", sync2ThirdPlatform=" + sync2ThirdPlatform +
            ", sysType='" + sysType + '\'' +
            '}';
    }
}
