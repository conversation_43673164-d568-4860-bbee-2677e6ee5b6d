package com.cowell.bam.service.dto;

import lombok.Data;

import java.io.Serializable;

@Data
public class InnerRequestParamDTO implements Serializable {

    /**
     * 海典,英克，sap 对应业务类型
     */
    String btype;

    /**
     * 海典,英克，sap 对应业务来源
     */
    String bsource;

    /**
     * 海典,英克，sap 对应业务目标
     */
    String bdestination;

    /**
     * 请求类型(hd,yk,sap)
     */
    private String erpType;

    /**
     * 接口描述
     */
    private String requestDesc;

    /**
     * 请求类型
     */
    private String methodType;


    /**
     * 是否需要的登录
     */
    private String isAuth;


    /**
     * 海典，英克，sap，请求url
     */
    private String requestUrl;

    /**
     * 回调地址
     */
    private String bcallback;

    /**
     * 数据重复校验字符串
     * 使用data字段里的JSON计算MD5值填充此字段。发送系统生成MD5，
     * 接收系统单纯校验MD5是否重复，不用再生成MD5与发送系统MD5比对。
     */
    private String bdatahash;


    /**
     * 海典，英克密码
     */
    private String password;

    /**
     * 海典英克用户名
     */
    private String userName;

    /**
     * 接口请求模式同步或者异步
     */
    private String requestMode;

    /**
     * 接口版本号
     */
    private String bversion;


    /**
     * 业务操作具体使用哪个handler
     */
    private String businessHandlerType;

    /**
     * bdata参数类型通过apollo配置(海典)
     */
    private String bDataType;

    /**
     * erpsaas阿波罗配置的接口信息
     */
    private String serviceConfig;
}
