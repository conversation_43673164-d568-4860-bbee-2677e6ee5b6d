package com.cowell.bam.service.dto;

import io.swagger.annotations.ApiModelProperty;
import org.apache.commons.lang3.builder.ToStringBuilder;

import java.util.Date;

/**
 * <AUTHOR>
 * @ProjectName bam
 * @Description:
 * @date 2019/09/26 14:14
 */
public class ItemPriceBdataDTO {

    @ApiModelProperty(value = "门店编码")
    private String busNo;

    @ApiModelProperty(value = "企业编号")
    private String compId;

    @ApiModelProperty(value = "商品编码")
    private String wareCode;

    @ApiModelProperty(name = "价格")
    private String price;

    @ApiModelProperty(name = "会员价格")
    private String memberPrice;

    @ApiModelProperty(name = "消息发送时间格式：yyyy-MM-dd HH:mm:ss")
    private Date bdatetime;

    private String dateTime;

    @ApiModelProperty(name = "数据类型（0 全量推送、1 增量变动）")
    private Byte bType;

    public String getBusNo() {
        return busNo;
    }

    public void setBusNo(String busNo) {
        this.busNo = busNo;
    }

    public String getCompId() {
        return compId;
    }

    public void setCompId(String compId) {
        this.compId = compId;
    }

    public String getWareCode() {
        return wareCode;
    }

    public void setWareCode(String wareCode) {
        this.wareCode = wareCode;
    }


    public String getPrice() {
        return price;
    }

    public void setPrice(String price) {
        this.price = price;
    }

    public Date getBdatetime() {
        return bdatetime;
    }

    public void setBdatetime(Date bdatetime) {
        this.bdatetime = bdatetime;
    }

    public String getDateTime() {
        return dateTime;
    }

    public void setDateTime(String dateTime) {
        this.dateTime = dateTime;
    }

    public Byte getbType() {
        return bType;
    }

    public void setbType(Byte bType) {
        this.bType = bType;
    }

    public String getMemberPrice() {
        return memberPrice;
    }

    public void setMemberPrice(String memberPrice) {
        this.memberPrice = memberPrice;
    }

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this);
    }
}
