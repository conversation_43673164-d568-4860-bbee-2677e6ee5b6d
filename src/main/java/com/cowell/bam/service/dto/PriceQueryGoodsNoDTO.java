package com.cowell.bam.service.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * 价格列表
 * @author: liubw
 * @date 2018/10/30 3:46 PM
 */

@Data
public class PriceQueryGoodsNoDTO implements Serializable {


    @ApiModelProperty("商品编码")
    private String goodsNo;

    @ApiModelProperty("商品价格")
    private BigDecimal price;

    @ApiModelProperty("商品会员价格")
    private BigDecimal memberPrice;

    /**
     * 是否有价格 0 否 1 是
     */
    private Integer isHasPrice;

    /**
     * 是否有会员价格 0 否 1 是
     */
    private Integer isHasMemberPrice;

    private List<PriceStoreDetail> priceStoreDetailList;

    @ApiModelProperty("是否特价  0 否 1 是")
    private Integer isSpecial;

    @ApiModelProperty("商品会员价格是否特价  0 否 1 是")
    private Integer memberPriceIsSpecial;

    @ApiModelProperty("是否有拆零价格 0 否 1 是")
    private Integer isHasPiecePrice;

    @ApiModelProperty("是否有会员拆零价格 0 否 1 是")
    private Integer isHasMemberPiecePrice;

    @ApiModelProperty("商品拆零价格")
    private BigDecimal piecePrice;

    @ApiModelProperty("商品会员拆零价格")
    private BigDecimal memberPiecePrice;

    @ApiModelProperty("拆零价是否特价  0 否 1 是")
    private Integer isPieceSpecial;

    @ApiModelProperty("商品会员拆零价格是否特价  0 否 1 是")
    private Integer memberPiecePriceIsSpecial;

}
