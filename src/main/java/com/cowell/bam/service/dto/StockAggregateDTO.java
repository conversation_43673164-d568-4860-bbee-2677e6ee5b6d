package com.cowell.bam.service.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2022/8/25 13:59
 */
@Data
public class StockAggregateDTO extends StockGoodsBaseDTO implements Serializable {
    @ApiModelProperty(name = "skuMerchantCode", value = "商品编号")
    private String skuMerchantCode;
    @ApiModelProperty(name = "batchNo", value = "生成批号")
    private String batchNo;
    @ApiModelProperty(name = "batchCode", value = "批次")
    private String batchCode;

    @ApiModelProperty(name = "stock", value = "总库存")
    private BigDecimal stock;
    @ApiModelProperty(name = "buyStock", value = "可卖库存")
    private BigDecimal buyStock;
    @ApiModelProperty(name = "pieceBuyStock", value = "拆零可卖库存(合格品区小库存)")
    private BigDecimal pieceBuyStock;
    @ApiModelProperty(name = "waittingAreaStock", value = "待验收区库存")
    private BigDecimal waittingAreaStock;
    @ApiModelProperty(name = "pieceWaittingAreaStock", value = "拆零待验收区库存(待验收区小库存)")
    private BigDecimal pieceWaittingAreaStock;
    @ApiModelProperty(name = "unqualifiedAreaStock", value = "非合格品区库存")
    private BigDecimal unqualifiedAreaStock;
    @ApiModelProperty(name = "pieceUnqualifiedAreaStock", value = "拆零非合格品库存(非合格品区小库存)")
    private BigDecimal pieceUnqualifiedAreaStock;
    @ApiModelProperty(name = "waitStock", value = "待出库库存")
    private BigDecimal waitStock;
    @ApiModelProperty(name = "transitStock", value = "在途库存")
    private BigDecimal transitStock;

    @ApiModelProperty(name = "unit", value = "单位")
    private String unit;

    @ApiModelProperty("库存地类型")
    private String location;

    @ApiModelProperty(value = "MDM连锁编码")
    private String comId;

    @ApiModelProperty(value = "MDM门店编码")
    private String storeNo;
}
