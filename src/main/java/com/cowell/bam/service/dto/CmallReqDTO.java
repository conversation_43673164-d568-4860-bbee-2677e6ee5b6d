package com.cowell.bam.service.dto;

import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;
import java.util.List;

public class CmallReqDTO implements Serializable {
    @ApiModelProperty(name = "企业编号")
    String compId;
    @ApiModelProperty(name = "门店编码")
    String busNo;
    @ApiModelProperty(name = "在途库存标识 1-在途")
    Integer transitStock;
    List<CompareDataInfo> details;

    public Integer getTransitStock() {
        return transitStock;
    }

    public void setTransitStock(Integer transitStock) {
        this.transitStock = transitStock;
    }

    public String getCompId() {
        return compId;
    }

    public void setCompId(String compId) {
        this.compId = compId;
    }

    public String getBusNo() {
        return busNo;
    }

    public void setBusNo(String busNo) {
        this.busNo = busNo;
    }

    public List<CompareDataInfo> getDetails() {
        return details;
    }

    public void setDetails(List<CompareDataInfo> details) {
        this.details = details;
    }

    @Override
    public String toString() {
        return "CmallReqDTO{" +
            "compId='" + compId + '\'' +
            ", busNo='" + busNo + '\'' +
            ", details=" + details +
            '}';
    }
}
