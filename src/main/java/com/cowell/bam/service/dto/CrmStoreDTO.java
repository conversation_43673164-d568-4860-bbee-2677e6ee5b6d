package com.cowell.bam.service.dto;

import javax.validation.constraints.Max;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.io.Serializable;
import java.time.ZonedDateTime;
import java.util.Objects;

/**
 * A DTO for the CrmStore entity.
 */
public class CrmStoreDTO implements Serializable {

    private Long id;

    @NotNull
    private Long businessId;

    private String poiId;

    @NotNull
    @Size(max = 50)
    private String storeName;

    @Max(value = 10)
    private Integer storeType;

    @NotNull
    @Size(max = 20)
    private String phone;

    @NotNull
    @Size(max = 20)
    private String province;

    @NotNull
    @Max(value = 999999)
    private Integer provinceCode;

    @NotNull
    @Size(max = 30)
    private String city;

    @NotNull
    @Max(value = 999999)
    private Integer cityCode;

    @NotNull
    @Size(max = 30)
    private String county;

    @NotNull
    @Max(value = 999999)
    private Integer countyCode;

    @NotNull
    @Size(max = 50)
    private String address;

    private Float longitude;

    private Float latitude;

    @Size(max = 100)
    private String businessTime;

    @NotNull
    @Size(max = 32)
    private String extendCode;

    @Size(max = 256)
    private String qrCodeurl;

    @Size(max = 3000)
    private String extend;

    @NotNull
    @Max(value = 999999999)
    private Integer version;

    @NotNull
    private Integer status;

    @NotNull
    private Integer wxStatus;

    //    @ReadOnlyProperty
    private Long createdBy;

    //    @ReadOnlyProperty
    private ZonedDateTime createdDate;

    private Long lastModifiedBy;

    private ZonedDateTime lastModifiedDate = ZonedDateTime.now();

    private String operationModel;


    public String getOperationModel() {
        return operationModel;
    }

    public void setOperationModel(String operationModel) {
        this.operationModel = operationModel;
    }

    public String getPoiId() {
        return poiId;
    }

    public void setPoiId(String poiId) {
        this.poiId = poiId;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getBusinessId() {
        return businessId;
    }

    public void setBusinessId(Long businessId) {
        this.businessId = businessId;
    }

    public String getStoreName() {
        return storeName;
    }

    public void setStoreName(String storeName) {
        this.storeName = storeName;
    }

    public Integer getStoreType() {
        return storeType;
    }

    public void setStoreType(Integer storeType) {
        this.storeType = storeType;
    }

    public String getPhone() {
        return phone;
    }

    public void setPhone(String phone) {
        this.phone = phone;
    }

    public String getProvince() {
        return province;
    }

    public void setProvince(String province) {
        this.province = province;
    }

    public Integer getProvinceCode() {
        return provinceCode;
    }

    public void setProvinceCode(Integer provinceCode) {
        this.provinceCode = provinceCode;
    }

    public String getCity() {
        return city;
    }

    public void setCity(String city) {
        this.city = city;
    }

    public Integer getCityCode() {
        return cityCode;
    }

    public void setCityCode(Integer cityCode) {
        this.cityCode = cityCode;
    }

    public String getCounty() {
        return county;
    }

    public void setCounty(String county) {
        this.county = county;
    }

    public Integer getCountyCode() {
        return countyCode;
    }

    public void setCountyCode(Integer countyCode) {
        this.countyCode = countyCode;
    }

    public String getAddress() {
        return address;
    }

    public void setAddress(String address) {
        this.address = address;
    }

    public Float getLongitude() {
        return longitude;
    }

    public void setLongitude(Float longitude) {
        this.longitude = longitude;
    }

    public Float getLatitude() {
        return latitude;
    }

    public void setLatitude(Float latitude) {
        this.latitude = latitude;
    }

    public String getBusinessTime() {
        return businessTime;
    }

    public void setBusinessTime(String businessTime) {
        this.businessTime = businessTime;
    }

    public String getExtendCode() {
        return extendCode;
    }

    public void setExtendCode(String extendCode) {
        this.extendCode = extendCode;
    }

    public String getQrCodeurl() {
        return qrCodeurl;
    }

    public void setQrCodeurl(String qrCodeurl) {
        this.qrCodeurl = qrCodeurl;
    }

    public String getExtend() {
        return extend;
    }

    public void setExtend(String extend) {
        this.extend = extend;
    }

    public Integer getVersion() {
        return version;
    }

    public void setVersion(Integer version) {
        this.version = version;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public Integer getWxStatus() {
        return wxStatus;
    }

    public void setWxStatus(Integer wxStatus) {
        this.wxStatus = wxStatus;
    }

    public Long getCreatedBy() {
        return createdBy;
    }

    public void setCreatedBy(Long createdBy) {
        this.createdBy = createdBy;
    }

    public ZonedDateTime getCreatedDate() {
        return createdDate;
    }

    public void setCreatedDate(ZonedDateTime createdDate) {
        this.createdDate = createdDate;
    }

    public Long getLastModifiedBy() {
        return lastModifiedBy;
    }

    public void setLastModifiedBy(Long lastModifiedBy) {
        this.lastModifiedBy = lastModifiedBy;
    }

    public ZonedDateTime getLastModifiedDate() {
        return lastModifiedDate;
    }

    public void setLastModifiedDate(ZonedDateTime lastModifiedDate) {
        this.lastModifiedDate = lastModifiedDate;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }

        CrmStoreDTO crmStoreDTO = (CrmStoreDTO) o;
        if (crmStoreDTO.getId() == null || getId() == null) {
            return false;
        }
        return Objects.equals(getId(), crmStoreDTO.getId());
    }

    @Override
    public int hashCode() {
        return Objects.hashCode(getId());
    }

    @Override
    public String toString() {
        return "CrmStoreDTO{" +
            "id=" + id +
            ", businessId=" + businessId +
            ", poiId='" + poiId + '\'' +
            ", storeName='" + storeName + '\'' +
            ", storeType=" + storeType +
            ", phone='" + phone + '\'' +
            ", province='" + province + '\'' +
            ", provinceCode=" + provinceCode +
            ", city='" + city + '\'' +
            ", cityCode=" + cityCode +
            ", county='" + county + '\'' +
            ", countyCode=" + countyCode +
            ", address='" + address + '\'' +
            ", longitude=" + longitude +
            ", latitude=" + latitude +
            ", businessTime='" + businessTime + '\'' +
            ", extendCode='" + extendCode + '\'' +
            ", qrCodeurl='" + qrCodeurl + '\'' +
            ", extend='" + extend + '\'' +
            ", version=" + version +
            ", status=" + status +
            ", wxStatus=" + wxStatus +
            ", createdBy=" + createdBy +
            ", createdDate=" + createdDate +
            ", lastModifiedBy=" + lastModifiedBy +
            ", lastModifiedDate=" + lastModifiedDate +
            ", operationModel='" + operationModel + '\'' +
            '}';
    }
}
