package com.cowell.bam.service.dto;

import io.swagger.annotations.ApiModelProperty;


public class PriceDataInfoDTO {

    @ApiModelProperty(value = "商品编码")
    private String skuMerchantCode;

    @ApiModelProperty(name = "价格")
    private String price;

    /**
     * 商品会员价格
     */
    private String memberPrice;

    private String priceTypeCode;

    /**
     * 说明
     */
    private String message;

    public String getSkuMerchantCode() {
        return skuMerchantCode;
    }

    public void setSkuMerchantCode(String skuMerchantCode) {
        this.skuMerchantCode = skuMerchantCode;
    }

    public String getPrice() {
        return price;
    }

    public void setPrice(String price) {
        this.price = price;
    }

    public String getMemberPrice() {
        return memberPrice;
    }

    public void setMemberPrice(String memberPrice) {
        this.memberPrice = memberPrice;
    }

    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }

    public String getPriceTypeCode() {
        return priceTypeCode;
    }

    public void setPriceTypeCode(String priceTypeCode) {
        this.priceTypeCode = priceTypeCode;
    }
}
