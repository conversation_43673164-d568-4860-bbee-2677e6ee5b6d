package com.cowell.bam.service.dto;

import org.apache.commons.lang3.builder.ToStringBuilder;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @ProjectName bam
 * @Description:
 * @date 2019/10/17 10:49
 */
public class ItemPriceQueryParam implements Serializable {
    private Long businessId;
    private Long storeId;
    private List<String> priceGoodsNo;
    private List<String> mpriceGoodsNo;

    public Long getBusinessId() {
        return businessId;
    }

    public void setBusinessId(Long businessId) {
        this.businessId = businessId;
    }

    public Long getStoreId() {
        return storeId;
    }

    public void setStoreId(Long storeId) {
        this.storeId = storeId;
    }

    public List<String> getPriceGoodsNo() {
        return priceGoodsNo;
    }

    public void setPriceGoodsNo(List<String> priceGoodsNo) {
        this.priceGoodsNo = priceGoodsNo;
    }

    public List<String> getMpriceGoodsNo() {
        return mpriceGoodsNo;
    }

    public void setMpriceGoodsNo(List<String> mpriceGoodsNo) {
        this.mpriceGoodsNo = mpriceGoodsNo;
    }

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this);
    }
}
