package com.cowell.bam.service;

import com.codahale.metrics.annotation.Timed;
import com.cowell.bam.service.dto.base.AmisCommonResponse;
import com.cowell.bam.service.dto.base.AmisListResponse;
import org.springframework.cloud.netflix.feign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestParam;

/**
 * <AUTHOR>
 * @date 2023/1/9 16:19
 */
@FeignClient(name = "nyuwa")
public interface INyuwaFeign {

    @GetMapping("/api/intranet/mdd/{appId}/{modelName}/list")
    @Timed
    AmisCommonResponse<AmisListResponse<Object>> queryCompareResult(@PathVariable("appId") String appId,
                                                                    @PathVariable("modelName") String modelName,
                                                                    @RequestParam("page") Integer page,
                                                                    @RequestParam("perPage") Integer perPage,
                                                                    @RequestParam("batch_no") String batchNo,
                                                                    @RequestParam("comparison_status") Integer comparison_status);
}
