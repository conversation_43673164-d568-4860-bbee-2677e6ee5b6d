package com.cowell.bam.service;

import com.codahale.metrics.annotation.Timed;
import com.cowell.bam.service.dto.*;
import com.github.pagehelper.PageInfo;
import org.springframework.cloud.netflix.feign.FeignClient;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import javax.validation.Valid;
import java.util.List;

@FeignClient(name = "stockcenter-api")
public interface IStockCenterFeignService {

    @PostMapping("/api/internal/stock/goods/getCodeList")
    @Timed
    ResponseEntity<List<StockGoodsCountInfo>> getCodeList(@Valid @RequestBody StockGoodsQueryParam stockGoodsQueryParam);

    @PostMapping("/api/internal/stock/cost/getStockCostList")
    @Timed
    ResponseEntity<List<StockBatchCodeCostDTO>> getStockCostList(@Valid @RequestBody StockCostQueryParam stockCostQueryParam);

    ///internal/stock/goods/getGoodsBatchCodeList

    @GetMapping("/api/internal/stock/goods/getGoodsBatchCodeList")
    @Timed
    ResponseEntity<PageInfo<StockGoodsCountInfo>> getGoodsBatchCodeList(@RequestParam("storeId") Long storeId, @RequestParam("page") int page, @RequestParam("size") int size);

    @GetMapping("/api/internal/stock/goods/getGoodsNoByStoreId")
    @Timed
    ResponseEntity<PageInfo<StoreGoodsNoDTO>> getGoodsNoByStoreId(@RequestParam("storeId") Long storeId, @RequestParam("page") int page, @RequestParam("size") int size);

    @PostMapping("/api/internal/stock/goodsBatchCode/stockAggregate")
    @Timed
    ResponseEntity<PageInfo<StockGoodsCountInfo>> stockAggregate(@RequestBody StockAggregateQuery param);

}
