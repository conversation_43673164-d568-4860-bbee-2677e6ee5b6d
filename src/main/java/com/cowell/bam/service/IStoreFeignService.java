package com.cowell.bam.service;

import com.codahale.metrics.annotation.Timed;
import com.cowell.bam.service.dto.*;
import org.springframework.cloud.netflix.feign.FeignClient;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

@FeignClient(name = "store")
public interface IStoreFeignService {

    @PostMapping("/api/internal/mdmBusinessBase/transform")
    @Timed
    MdmDataTransformDTO transformMdmData(@Valid @RequestBody MdmDataTransformDTO mdmDataTransformDTO);

    /**
     * 根据连锁id查询门店ids
     *
     * @param businessId
     * @return
     */
    @RequestMapping(value = "/api/internal/getStoreIdsByBusinessId", method = RequestMethod.GET)
    @Timed
    ResponseEntity<List<Long>> getStoreIdsByBusinessId(@RequestParam(value = "businessId") Long businessId);

    @RequestMapping(value = "/api/internal/mdmStoreBase/findAllStoreByBusinessId", method = RequestMethod.GET)
    @Timed
    ResponseEntity<List<MdmStoreBaseDTO>> findAllStoreByBusinessId(@RequestParam(value = "businessId") Long businessId);


    /**
     * 根据连锁id查询门店ids
     *
     * @param comId
     * @return
     */
    @RequestMapping(value = "/api/internal/mdmStoreBase/findMdmStoreByComId", method = RequestMethod.GET)
    @Timed
    ResponseEntity<List<MdmStoreBaseDTO>> findMdmStoreByComId(@RequestParam(value = "comId") String comId);


    /**
     * 根据连锁id查询门店ids
     *
     * @param ids
     * @return
     */
    @RequestMapping(value = "/api/businessInfos/getAllBusinessInfoByIds", method = RequestMethod.POST)
    @Timed
    ResponseEntity<List<BusinessInfoDTO>> getAllBusinessInfoByIds(@RequestBody List<Long> ids);


    /**
     * 根据mdm store编码查询MDM门店基本信息
     *
     * @param storeNo
     */
    @GetMapping("/api/internal/mdmStoreBase/findByStoreNo")
    @Timed
    ResponseEntity<MdmStoreBaseDTO> findByStoreNo(@RequestParam(value = "storeNo") String storeNo);

    /**
     * 根据mdm store编码查询MDM门店基本信息
     *
     * @param storeId
     */
    @GetMapping("/api/internal/mdmStoreBase/findByStoreId")
    @Timed
    ResponseEntity<MdmStoreBaseDTO> findByStoreId(@RequestParam(value = "storeId") Long storeId);

    /**
     * 根据门店id 获取门店信息
     *
     * @param id
     * @return
     */
    @RequestMapping(value = "/api/internal/crm-stores-findOne", method = RequestMethod.GET)
    @Timed
    ResponseEntity<CrmStoreDTO> getCrmStore(@RequestParam(value = "id") Long id);



    /**
     * 根据businessId 获取连锁下所有门店信息
     * @param businessId
     */
    @GetMapping("api/internal/getCrmStoreByBusinessId")
    @Timed
    ResponseEntity<List<CrmStoreDTO>> getCrmStoreByBusinessId(@RequestParam(value = "businessId") Long businessId);

    @GetMapping(value ={ "/api/internal/findMultipleMdmBusinessByBusIds"})
    List<MdmBusinessBaseDTO> findMultipleMdmBusinessByBusIds(@RequestParam(value = "businessIds") List<Long> businessIds);

    @GetMapping(value ={ "/api/internal/findMdmBusinessBaseByBusinessIds"})
    @Timed
    List<MdmBusinessBaseDTO> findMdmBusinessBaseByBusinessIds(@RequestParam(value = "businessIds") List<Long> businessIds,
                                                              @RequestParam(value = "bizCode",required = false) String bizCode,
                                                              @RequestParam(value = "bizType",required = false) Integer bizType);
}
