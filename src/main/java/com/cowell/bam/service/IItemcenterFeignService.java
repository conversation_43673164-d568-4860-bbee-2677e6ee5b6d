package com.cowell.bam.service;

import com.codahale.metrics.annotation.Timed;
import com.cowell.bam.service.dto.ItemPriceResponse;
import com.cowell.bam.service.dto.ItemPriceQueryParam;
import org.springframework.cloud.netflix.feign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import javax.validation.Valid;
import java.util.List;

@FeignClient(name = "itemcenter")
public interface IItemcenterFeignService {

    @PostMapping("/api/internal/item/queryStoreItemPrice")
    @Timed
    List<ItemPriceResponse> queryStoreItemPrice(@Valid @RequestBody ItemPriceQueryParam ItemPriceQueryParam);


}
