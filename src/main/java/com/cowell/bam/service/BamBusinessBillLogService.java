package com.cowell.bam.service;

import com.cowell.bam.domain.BamLogDO;
import com.cowell.bam.domain.BamQueryDO;
import com.cowell.bam.domain.BamReturnDO;

import java.util.List;
import java.util.concurrent.Future;

/**
 * <AUTHOR>
 * @date: 2018/12/29 11:29
 * @description:
 */
public interface BamBusinessBillLogService {
    int save(BamLogDO bamLogDO);

    Future<Boolean> batchInsert(List<BamLogDO> bamLogDOList);

    List<BamReturnDO> selectByCompanyAndStoreId(BamQueryDO bamQueryDO) throws Exception;

}
