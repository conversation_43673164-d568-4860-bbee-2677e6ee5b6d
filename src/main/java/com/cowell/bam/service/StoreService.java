package com.cowell.bam.service;

import com.codahale.metrics.annotation.Timed;
import com.cowell.bam.service.dto.MdmDataTransformDTO;
import org.springframework.cloud.netflix.feign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import javax.validation.Valid;

/**
 * 获取门店信息rpc服务
 * Created by liw on 2018/5/22.
 */
@FeignClient(name = "store-sync")
public interface StoreService {


    @PostMapping("/api/internal/mdmBusinessBase/transform")
    @Timed
    MdmDataTransformDTO transformMdmData(@Valid @RequestBody MdmDataTransformDTO mdmDataTransformDTO);

}
