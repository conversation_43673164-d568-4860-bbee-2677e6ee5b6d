package com.cowell.bam.service.utils;

import java.util.List;

/**
 *  分页对象
 *
 * <AUTHOR>
 * @version Pagination.java, 2018/5/21 16:52
 */
public class Pagination<T> {

    private List<T> source;

    private int page;

    private int size;

    private int pageNum;

    private int pageSize;

    private long hitCount;

    private int totalPages;

    private int pages;

    private List<T> result ;

    private List<T> list ;

    public Pagination() {}

    public Pagination(int page, int size) {
        this.page = page;
        this.size = size;
        this.pageNum = page;
        this.pageSize = size;
    }

    public Pagination(List<T> source, int page, int size, long hitCount) {
        this.source = source;
        this.page = page;
        this.size = size;
        this.hitCount = hitCount;
        this.totalPages = (int) ((hitCount % size == 0) ? hitCount / size : (hitCount / size) + 1);

        this.pageNum = page;
        this.pageSize = size;
        this.pages = this.totalPages;
        this.list = source;

    }

    /**
     * Getter method for <tt>source</tt>.
     *
     * @return value of source
     */
    public List<T> getSource() {
        return source;
    }

    /**
     * Setter method for <tt>source</tt>.
     *
     * @param source value to be assigned to source
     */
    public void setSource(List<T> source) {
        this.source = source;
    }

    /**
     * Getter method for <tt>page</tt>.
     *
     * @return value of page
     */
    public int getPage() {
        return page;
    }

    /**
     * Setter method for <tt>page</tt>.
     *
     * @param page value to be assigned to page
     */
    public void setPage(int page) {
        this.page = page;
    }

    /**
     * Getter method for <tt>size</tt>.
     *
     * @return value of size
     */
    public int getSize() {
        return size;
    }

    /**
     * Setter method for <tt>size</tt>.
     *
     * @param size value to be assigned to size
     */
    public void setSize(int size) {
        this.size = size;
    }

    /**
     * Getter method for <tt>hitCount</tt>.
     *
     * @return value of hitCount
     */
    public long getHitCount() {
        return hitCount;
    }

    /**
     * Setter method for <tt>hitCount</tt>.
     *
     * @param hitCount value to be assigned to hitCount
     */
    public void setHitCount(long hitCount) {
        this.hitCount = hitCount;
        this.totalPages = (int) ((hitCount % size == 0) ? hitCount / size : (hitCount / size) + 1);

    }

    /**
     * Getter method for <tt>totalPages</tt>.
     *
     * @return value of totalPages
     */
    public int getTotalPages() {
        return totalPages;
    }

    /**
     * Setter method for <tt>totalPages</tt>.
     *
     * @param totalPages value to be assigned to totalPages
     */
    public void setTotalPages(int totalPages) {
        this.totalPages = totalPages;
    }

    public List<T> getResult() {
        return result;
    }

    public void setResult(List<T> result) {
        this.result = result;
    }

    public int getPageNum() {
        return pageNum;
    }

    public void setPageNum(int pageNum) {
        this.pageNum = pageNum;
    }

    public int getPageSize() {
        return pageSize;
    }

    public void setPageSize(int pageSize) {
        this.pageSize = pageSize;
    }

    public int getPages() {
        return pages;
    }

    public void setPages(int pages) {
        this.pages = pages;
    }

    public List<T> getList() {
        return list;
    }

    public void setList(List<T> list) {
        this.list = list;
    }
}
