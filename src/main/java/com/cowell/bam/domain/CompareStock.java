package com.cowell.bam.domain;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

public class CompareStock implements Serializable {
    private Long id;

    private String comId;

    private String busNo;

    private String wareCode;

    private String batchNo;

    private String makeNo;

    private BigDecimal tNum;

    private BigDecimal num;

    private BigDecimal qualifiedStock;

    private BigDecimal checkStock;

    private BigDecimal unqualifiedStock;

    private String unit;

    private Date expireDate;

    private Date produceDate;

    private Date syncDate;

    private String extend;

    private Date gmtCreate;

    private Date gmtUpdate;

    private static final long serialVersionUID = 1L;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getComId() {
        return comId;
    }

    public void setComId(String comId) {
        this.comId = comId == null ? null : comId.trim();
    }

    public String getBusNo() {
        return busNo;
    }

    public void setBusNo(String busNo) {
        this.busNo = busNo == null ? null : busNo.trim();
    }

    public String getWareCode() {
        return wareCode;
    }

    public void setWareCode(String wareCode) {
        this.wareCode = wareCode == null ? null : wareCode.trim();
    }

    public String getBatchNo() {
        return batchNo;
    }

    public void setBatchNo(String batchNo) {
        this.batchNo = batchNo == null ? null : batchNo.trim();
    }

    public String getMakeNo() {
        return makeNo;
    }

    public void setMakeNo(String makeNo) {
        this.makeNo = makeNo == null ? null : makeNo.trim();
    }

    public BigDecimal gettNum() {
        return tNum;
    }

    public void settNum(BigDecimal tNum) {
        this.tNum = tNum;
    }

    public BigDecimal getNum() {
        return num;
    }

    public void setNum(BigDecimal num) {
        this.num = num;
    }

    public BigDecimal getQualifiedStock() {
        return qualifiedStock;
    }

    public void setQualifiedStock(BigDecimal qualifiedStock) {
        this.qualifiedStock = qualifiedStock;
    }

    public BigDecimal getCheckStock() {
        return checkStock;
    }

    public void setCheckStock(BigDecimal checkStock) {
        this.checkStock = checkStock;
    }

    public BigDecimal getUnqualifiedStock() {
        return unqualifiedStock;
    }

    public void setUnqualifiedStock(BigDecimal unqualifiedStock) {
        this.unqualifiedStock = unqualifiedStock;
    }

    public String getUnit() {
        return unit;
    }

    public void setUnit(String unit) {
        this.unit = unit == null ? null : unit.trim();
    }

    public Date getExpireDate() {
        return expireDate;
    }

    public void setExpireDate(Date expireDate) {
        this.expireDate = expireDate;
    }

    public Date getProduceDate() {
        return produceDate;
    }

    public void setProduceDate(Date produceDate) {
        this.produceDate = produceDate;
    }

    public Date getSyncDate() {
        return syncDate;
    }

    public void setSyncDate(Date syncDate) {
        this.syncDate = syncDate;
    }

    public String getExtend() {
        return extend;
    }

    public void setExtend(String extend) {
        this.extend = extend == null ? null : extend.trim();
    }

    public Date getGmtCreate() {
        return gmtCreate;
    }

    public void setGmtCreate(Date gmtCreate) {
        this.gmtCreate = gmtCreate;
    }

    public Date getGmtUpdate() {
        return gmtUpdate;
    }

    public void setGmtUpdate(Date gmtUpdate) {
        this.gmtUpdate = gmtUpdate;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", comId=").append(comId);
        sb.append(", busNo=").append(busNo);
        sb.append(", wareCode=").append(wareCode);
        sb.append(", batchNo=").append(batchNo);
        sb.append(", makeNo=").append(makeNo);
        sb.append(", tNum=").append(tNum);
        sb.append(", num=").append(num);
        sb.append(", qualifiedStock=").append(qualifiedStock);
        sb.append(", checkStock=").append(checkStock);
        sb.append(", unqualifiedStock=").append(unqualifiedStock);
        sb.append(", unit=").append(unit);
        sb.append(", expireDate=").append(expireDate);
        sb.append(", produceDate=").append(produceDate);
        sb.append(", syncDate=").append(syncDate);
        sb.append(", extend=").append(extend);
        sb.append(", gmtCreate=").append(gmtCreate);
        sb.append(", gmtUpdate=").append(gmtUpdate);
        sb.append(", serialVersionUID=").append(serialVersionUID);
        sb.append("]");
        return sb.toString();
    }
}
