package com.cowell.bam.domain;

import com.cowell.bam.service.dto.CmallRequestDTO;
import com.fasterxml.jackson.annotation.JsonSetter;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;
import java.util.List;

public class CmallTableRequestDTO implements Serializable {
    @ApiModelProperty(name = "唯一ID")
    List<CmallRequestDTO> Table;

    public List<CmallRequestDTO> getTable() {
        return Table;
    }
    @JsonSetter(value = "Table")
    public void setTable(List<CmallRequestDTO> table) {
        Table = table;
    }

    @Override
    public String toString() {
        return "CmallTableRequestDTO{" +
            "Table=" + Table +
            '}';
    }
}
