package com.cowell.bam.domain;

import java.io.Serializable;

public class DiffDataCompareInfoWithBLOBs extends DiffDataCompareInfo implements Serializable {
    private String thirdData;

    private String ourData;

    private static final long serialVersionUID = 1L;

    public String getThirdData() {
        return thirdData;
    }

    public void setThirdData(String thirdData) {
        this.thirdData = thirdData == null ? null : thirdData.trim();
    }

    public String getOurData() {
        return ourData;
    }

    public void setOurData(String ourData) {
        this.ourData = ourData == null ? null : ourData.trim();
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", thirdData=").append(thirdData);
        sb.append(", ourData=").append(ourData);
        sb.append(", serialVersionUID=").append(serialVersionUID);
        sb.append("]");
        return sb.toString();
    }
}
