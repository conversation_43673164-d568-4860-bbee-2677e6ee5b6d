package com.cowell.bam.domain;

public class BamLogDO {
    private Long id;
    private String ZGUID;    //主键	string	32	表的主键
    private String ZTRACEID;//	业务单据唯一ID	string	50个英文	业务单据ID 例如：1812170005010
    private Short ZFLOWID;//	流程编码	int		例：10
    private String ZSTEPID;//	步骤编码	int		例：11
    private String ZSENDSYSID;//	发送系统编码	string	10个英文	取值SAP、POS、WMS
    private String ZRECSYSID;    //接收系统编码	string	10个英文	取值SAP、POS、WMS
    private String ZCOMID;    //公司编码	string	10个英文
    private String ZSTOREID;    //门店编码	string	10个英文
    private String ZTIMESTAMP;    //发送时间戳	Dec	14位整数，6位小数	例：20181213000000.000000
    private String ZBUSINESSDATE;//	单据日期	Dec	14位整数，6位小数	20181213000000.000000取东八区系统时间
    private Short ZITEMCOUNT;//	明细条目数	int		例：100
    private Short ZSTATUS;//	状态	int		0失败,1成功
    private String ZMESSAG;//消息	string	500个英文	成功记录Success,失败记录原因。超长截断。
    private String ZEXTEND1;//备用字段	string	100个英文
    private String ZEXTEND2;//	备用字段	string	100个英文
    private String ZEXTEND3;//备用字段	string	500个英文
    private String ZEXTEND4;//备用字段	string	500个英文
    private String env;
    private String logtime;


    public String getLogtime() {
        return logtime;
    }

    public void setLogtime(String logtime) {
        this.logtime = logtime;
    }

    public String getEnv() {
        return env;
    }

    public void setEnv(String env) {
        this.env = env;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getZGUID() {
        return ZGUID;
    }

    public void setZGUID(String ZGUID) {
        this.ZGUID = ZGUID;
    }

    public String getZTRACEID() {
        return ZTRACEID;
    }

    public void setZTRACEID(String ZTRACEID) {
        this.ZTRACEID = ZTRACEID;
    }

    public Short getZFLOWID() {
        return ZFLOWID;
    }

    public void setZFLOWID(Short ZFLOWID) {
        this.ZFLOWID = ZFLOWID;
    }

    public String getZSTEPID() {
        return ZSTEPID;
    }

    public void setZSTEPID(String ZSTEPID) {
        this.ZSTEPID = ZSTEPID;
    }

    public String getZSENDSYSID() {
        return ZSENDSYSID;
    }

    public void setZSENDSYSID(String ZSENDSYSID) {
        this.ZSENDSYSID = ZSENDSYSID;
    }

    public String getZRECSYSID() {
        return ZRECSYSID;
    }

    public void setZRECSYSID(String ZRECSYSID) {
        this.ZRECSYSID = ZRECSYSID;
    }

    public String getZCOMID() {
        return ZCOMID;
    }

    public void setZCOMID(String ZCOMID) {
        this.ZCOMID = ZCOMID;
    }

    public String getZSTOREID() {
        return ZSTOREID;
    }

    public void setZSTOREID(String ZSTOREID) {
        this.ZSTOREID = ZSTOREID;
    }

    public String getZTIMESTAMP() {
        return ZTIMESTAMP;
    }

    public void setZTIMESTAMP(String ZTIMESTAMP) {
        this.ZTIMESTAMP = ZTIMESTAMP;
    }

    public String getZBUSINESSDATE() {
        return ZBUSINESSDATE;
    }

    public void setZBUSINESSDATE(String ZBUSINESSDATE) {
        this.ZBUSINESSDATE = ZBUSINESSDATE;
    }

    public Short getZITEMCOUNT() {
        return ZITEMCOUNT;
    }

    public void setZITEMCOUNT(Short ZITEMCOUNT) {
        this.ZITEMCOUNT = ZITEMCOUNT;
    }

    public Short getZSTATUS() {
        return ZSTATUS;
    }

    public void setZSTATUS(Short ZSTATUS) {
        this.ZSTATUS = ZSTATUS;
    }

    public String getZMESSAG() {
        return ZMESSAG;
    }

    public void setZMESSAG(String ZMESSAG) {
        this.ZMESSAG = ZMESSAG;
    }

    public String getZEXTEND1() {
        return ZEXTEND1;
    }

    public void setZEXTEND1(String ZEXTEND1) {
        this.ZEXTEND1 = ZEXTEND1;
    }

    public String getZEXTEND2() {
        return ZEXTEND2;
    }

    public void setZEXTEND2(String ZEXTEND2) {
        this.ZEXTEND2 = ZEXTEND2;
    }

    public String getZEXTEND3() {
        return ZEXTEND3;
    }

    public void setZEXTEND3(String ZEXTEND3) {
        this.ZEXTEND3 = ZEXTEND3;
    }

    public String getZEXTEND4() {
        return ZEXTEND4;
    }

    public void setZEXTEND4(String ZEXTEND4) {
        this.ZEXTEND4 = ZEXTEND4;
    }
}
