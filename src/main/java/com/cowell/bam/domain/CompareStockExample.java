package com.cowell.bam.domain;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class CompareStockExample {
    protected String orderByClause;

    protected boolean distinct;

    protected List<Criteria> oredCriteria;

    protected int limitStart = -1;

    protected int pageSize = -1;

    public CompareStockExample() {
        oredCriteria = new ArrayList<Criteria>();
    }

    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    public String getOrderByClause() {
        return orderByClause;
    }

    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    public boolean isDistinct() {
        return distinct;
    }

    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    public void setLimitStart(int limitStart) {
        this.limitStart=limitStart;
    }

    public int getLimitStart() {
        return limitStart;
    }

    public void setPageSize(int pageSize) {
        this.pageSize=pageSize;
    }

    public int getPageSize() {
        return pageSize;
    }

    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(Long value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(Long value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(Long value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(Long value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThan(Long value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(Long value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<Long> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<Long> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(Long value1, Long value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(Long value1, Long value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andComIdIsNull() {
            addCriterion("com_id is null");
            return (Criteria) this;
        }

        public Criteria andComIdIsNotNull() {
            addCriterion("com_id is not null");
            return (Criteria) this;
        }

        public Criteria andComIdEqualTo(String value) {
            addCriterion("com_id =", value, "comId");
            return (Criteria) this;
        }

        public Criteria andComIdNotEqualTo(String value) {
            addCriterion("com_id <>", value, "comId");
            return (Criteria) this;
        }

        public Criteria andComIdGreaterThan(String value) {
            addCriterion("com_id >", value, "comId");
            return (Criteria) this;
        }

        public Criteria andComIdGreaterThanOrEqualTo(String value) {
            addCriterion("com_id >=", value, "comId");
            return (Criteria) this;
        }

        public Criteria andComIdLessThan(String value) {
            addCriterion("com_id <", value, "comId");
            return (Criteria) this;
        }

        public Criteria andComIdLessThanOrEqualTo(String value) {
            addCriterion("com_id <=", value, "comId");
            return (Criteria) this;
        }

        public Criteria andComIdLike(String value) {
            addCriterion("com_id like", value, "comId");
            return (Criteria) this;
        }

        public Criteria andComIdNotLike(String value) {
            addCriterion("com_id not like", value, "comId");
            return (Criteria) this;
        }

        public Criteria andComIdIn(List<String> values) {
            addCriterion("com_id in", values, "comId");
            return (Criteria) this;
        }

        public Criteria andComIdNotIn(List<String> values) {
            addCriterion("com_id not in", values, "comId");
            return (Criteria) this;
        }

        public Criteria andComIdBetween(String value1, String value2) {
            addCriterion("com_id between", value1, value2, "comId");
            return (Criteria) this;
        }

        public Criteria andComIdNotBetween(String value1, String value2) {
            addCriterion("com_id not between", value1, value2, "comId");
            return (Criteria) this;
        }

        public Criteria andBusNoIsNull() {
            addCriterion("bus_no is null");
            return (Criteria) this;
        }

        public Criteria andBusNoIsNotNull() {
            addCriterion("bus_no is not null");
            return (Criteria) this;
        }

        public Criteria andBusNoEqualTo(String value) {
            addCriterion("bus_no =", value, "busNo");
            return (Criteria) this;
        }

        public Criteria andBusNoNotEqualTo(String value) {
            addCriterion("bus_no <>", value, "busNo");
            return (Criteria) this;
        }

        public Criteria andBusNoGreaterThan(String value) {
            addCriterion("bus_no >", value, "busNo");
            return (Criteria) this;
        }

        public Criteria andBusNoGreaterThanOrEqualTo(String value) {
            addCriterion("bus_no >=", value, "busNo");
            return (Criteria) this;
        }

        public Criteria andBusNoLessThan(String value) {
            addCriterion("bus_no <", value, "busNo");
            return (Criteria) this;
        }

        public Criteria andBusNoLessThanOrEqualTo(String value) {
            addCriterion("bus_no <=", value, "busNo");
            return (Criteria) this;
        }

        public Criteria andBusNoLike(String value) {
            addCriterion("bus_no like", value, "busNo");
            return (Criteria) this;
        }

        public Criteria andBusNoNotLike(String value) {
            addCriterion("bus_no not like", value, "busNo");
            return (Criteria) this;
        }

        public Criteria andBusNoIn(List<String> values) {
            addCriterion("bus_no in", values, "busNo");
            return (Criteria) this;
        }

        public Criteria andBusNoNotIn(List<String> values) {
            addCriterion("bus_no not in", values, "busNo");
            return (Criteria) this;
        }

        public Criteria andBusNoBetween(String value1, String value2) {
            addCriterion("bus_no between", value1, value2, "busNo");
            return (Criteria) this;
        }

        public Criteria andBusNoNotBetween(String value1, String value2) {
            addCriterion("bus_no not between", value1, value2, "busNo");
            return (Criteria) this;
        }

        public Criteria andWareCodeIsNull() {
            addCriterion("ware_code is null");
            return (Criteria) this;
        }

        public Criteria andWareCodeIsNotNull() {
            addCriterion("ware_code is not null");
            return (Criteria) this;
        }

        public Criteria andWareCodeEqualTo(String value) {
            addCriterion("ware_code =", value, "wareCode");
            return (Criteria) this;
        }

        public Criteria andWareCodeNotEqualTo(String value) {
            addCriterion("ware_code <>", value, "wareCode");
            return (Criteria) this;
        }

        public Criteria andWareCodeGreaterThan(String value) {
            addCriterion("ware_code >", value, "wareCode");
            return (Criteria) this;
        }

        public Criteria andWareCodeGreaterThanOrEqualTo(String value) {
            addCriterion("ware_code >=", value, "wareCode");
            return (Criteria) this;
        }

        public Criteria andWareCodeLessThan(String value) {
            addCriterion("ware_code <", value, "wareCode");
            return (Criteria) this;
        }

        public Criteria andWareCodeLessThanOrEqualTo(String value) {
            addCriterion("ware_code <=", value, "wareCode");
            return (Criteria) this;
        }

        public Criteria andWareCodeLike(String value) {
            addCriterion("ware_code like", value, "wareCode");
            return (Criteria) this;
        }

        public Criteria andWareCodeNotLike(String value) {
            addCriterion("ware_code not like", value, "wareCode");
            return (Criteria) this;
        }

        public Criteria andWareCodeIn(List<String> values) {
            addCriterion("ware_code in", values, "wareCode");
            return (Criteria) this;
        }

        public Criteria andWareCodeNotIn(List<String> values) {
            addCriterion("ware_code not in", values, "wareCode");
            return (Criteria) this;
        }

        public Criteria andWareCodeBetween(String value1, String value2) {
            addCriterion("ware_code between", value1, value2, "wareCode");
            return (Criteria) this;
        }

        public Criteria andWareCodeNotBetween(String value1, String value2) {
            addCriterion("ware_code not between", value1, value2, "wareCode");
            return (Criteria) this;
        }

        public Criteria andBatchNoIsNull() {
            addCriterion("batch_no is null");
            return (Criteria) this;
        }

        public Criteria andBatchNoIsNotNull() {
            addCriterion("batch_no is not null");
            return (Criteria) this;
        }

        public Criteria andBatchNoEqualTo(String value) {
            addCriterion("batch_no =", value, "batchNo");
            return (Criteria) this;
        }

        public Criteria andBatchNoNotEqualTo(String value) {
            addCriterion("batch_no <>", value, "batchNo");
            return (Criteria) this;
        }

        public Criteria andBatchNoGreaterThan(String value) {
            addCriterion("batch_no >", value, "batchNo");
            return (Criteria) this;
        }

        public Criteria andBatchNoGreaterThanOrEqualTo(String value) {
            addCriterion("batch_no >=", value, "batchNo");
            return (Criteria) this;
        }

        public Criteria andBatchNoLessThan(String value) {
            addCriterion("batch_no <", value, "batchNo");
            return (Criteria) this;
        }

        public Criteria andBatchNoLessThanOrEqualTo(String value) {
            addCriterion("batch_no <=", value, "batchNo");
            return (Criteria) this;
        }

        public Criteria andBatchNoLike(String value) {
            addCriterion("batch_no like", value, "batchNo");
            return (Criteria) this;
        }

        public Criteria andBatchNoNotLike(String value) {
            addCriterion("batch_no not like", value, "batchNo");
            return (Criteria) this;
        }

        public Criteria andBatchNoIn(List<String> values) {
            addCriterion("batch_no in", values, "batchNo");
            return (Criteria) this;
        }

        public Criteria andBatchNoNotIn(List<String> values) {
            addCriterion("batch_no not in", values, "batchNo");
            return (Criteria) this;
        }

        public Criteria andBatchNoBetween(String value1, String value2) {
            addCriterion("batch_no between", value1, value2, "batchNo");
            return (Criteria) this;
        }

        public Criteria andBatchNoNotBetween(String value1, String value2) {
            addCriterion("batch_no not between", value1, value2, "batchNo");
            return (Criteria) this;
        }

        public Criteria andMakeNoIsNull() {
            addCriterion("make_no is null");
            return (Criteria) this;
        }

        public Criteria andMakeNoIsNotNull() {
            addCriterion("make_no is not null");
            return (Criteria) this;
        }

        public Criteria andMakeNoEqualTo(String value) {
            addCriterion("make_no =", value, "makeNo");
            return (Criteria) this;
        }

        public Criteria andMakeNoNotEqualTo(String value) {
            addCriterion("make_no <>", value, "makeNo");
            return (Criteria) this;
        }

        public Criteria andMakeNoGreaterThan(String value) {
            addCriterion("make_no >", value, "makeNo");
            return (Criteria) this;
        }

        public Criteria andMakeNoGreaterThanOrEqualTo(String value) {
            addCriterion("make_no >=", value, "makeNo");
            return (Criteria) this;
        }

        public Criteria andMakeNoLessThan(String value) {
            addCriterion("make_no <", value, "makeNo");
            return (Criteria) this;
        }

        public Criteria andMakeNoLessThanOrEqualTo(String value) {
            addCriterion("make_no <=", value, "makeNo");
            return (Criteria) this;
        }

        public Criteria andMakeNoLike(String value) {
            addCriterion("make_no like", value, "makeNo");
            return (Criteria) this;
        }

        public Criteria andMakeNoNotLike(String value) {
            addCriterion("make_no not like", value, "makeNo");
            return (Criteria) this;
        }

        public Criteria andMakeNoIn(List<String> values) {
            addCriterion("make_no in", values, "makeNo");
            return (Criteria) this;
        }

        public Criteria andMakeNoNotIn(List<String> values) {
            addCriterion("make_no not in", values, "makeNo");
            return (Criteria) this;
        }

        public Criteria andMakeNoBetween(String value1, String value2) {
            addCriterion("make_no between", value1, value2, "makeNo");
            return (Criteria) this;
        }

        public Criteria andMakeNoNotBetween(String value1, String value2) {
            addCriterion("make_no not between", value1, value2, "makeNo");
            return (Criteria) this;
        }

        public Criteria andTNumIsNull() {
            addCriterion("t_num is null");
            return (Criteria) this;
        }

        public Criteria andTNumIsNotNull() {
            addCriterion("t_num is not null");
            return (Criteria) this;
        }

        public Criteria andTNumEqualTo(BigDecimal value) {
            addCriterion("t_num =", value, "tNum");
            return (Criteria) this;
        }

        public Criteria andTNumNotEqualTo(BigDecimal value) {
            addCriterion("t_num <>", value, "tNum");
            return (Criteria) this;
        }

        public Criteria andTNumGreaterThan(BigDecimal value) {
            addCriterion("t_num >", value, "tNum");
            return (Criteria) this;
        }

        public Criteria andTNumGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("t_num >=", value, "tNum");
            return (Criteria) this;
        }

        public Criteria andTNumLessThan(BigDecimal value) {
            addCriterion("t_num <", value, "tNum");
            return (Criteria) this;
        }

        public Criteria andTNumLessThanOrEqualTo(BigDecimal value) {
            addCriterion("t_num <=", value, "tNum");
            return (Criteria) this;
        }

        public Criteria andTNumIn(List<BigDecimal> values) {
            addCriterion("t_num in", values, "tNum");
            return (Criteria) this;
        }

        public Criteria andTNumNotIn(List<BigDecimal> values) {
            addCriterion("t_num not in", values, "tNum");
            return (Criteria) this;
        }

        public Criteria andTNumBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("t_num between", value1, value2, "tNum");
            return (Criteria) this;
        }

        public Criteria andTNumNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("t_num not between", value1, value2, "tNum");
            return (Criteria) this;
        }

        public Criteria andNumIsNull() {
            addCriterion("num is null");
            return (Criteria) this;
        }

        public Criteria andNumIsNotNull() {
            addCriterion("num is not null");
            return (Criteria) this;
        }

        public Criteria andNumEqualTo(BigDecimal value) {
            addCriterion("num =", value, "num");
            return (Criteria) this;
        }

        public Criteria andNumNotEqualTo(BigDecimal value) {
            addCriterion("num <>", value, "num");
            return (Criteria) this;
        }

        public Criteria andNumGreaterThan(BigDecimal value) {
            addCriterion("num >", value, "num");
            return (Criteria) this;
        }

        public Criteria andNumGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("num >=", value, "num");
            return (Criteria) this;
        }

        public Criteria andNumLessThan(BigDecimal value) {
            addCriterion("num <", value, "num");
            return (Criteria) this;
        }

        public Criteria andNumLessThanOrEqualTo(BigDecimal value) {
            addCriterion("num <=", value, "num");
            return (Criteria) this;
        }

        public Criteria andNumIn(List<BigDecimal> values) {
            addCriterion("num in", values, "num");
            return (Criteria) this;
        }

        public Criteria andNumNotIn(List<BigDecimal> values) {
            addCriterion("num not in", values, "num");
            return (Criteria) this;
        }

        public Criteria andNumBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("num between", value1, value2, "num");
            return (Criteria) this;
        }

        public Criteria andNumNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("num not between", value1, value2, "num");
            return (Criteria) this;
        }

        public Criteria andQualifiedStockIsNull() {
            addCriterion("qualified_stock is null");
            return (Criteria) this;
        }

        public Criteria andQualifiedStockIsNotNull() {
            addCriterion("qualified_stock is not null");
            return (Criteria) this;
        }

        public Criteria andQualifiedStockEqualTo(BigDecimal value) {
            addCriterion("qualified_stock =", value, "qualifiedStock");
            return (Criteria) this;
        }

        public Criteria andQualifiedStockNotEqualTo(BigDecimal value) {
            addCriterion("qualified_stock <>", value, "qualifiedStock");
            return (Criteria) this;
        }

        public Criteria andQualifiedStockGreaterThan(BigDecimal value) {
            addCriterion("qualified_stock >", value, "qualifiedStock");
            return (Criteria) this;
        }

        public Criteria andQualifiedStockGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("qualified_stock >=", value, "qualifiedStock");
            return (Criteria) this;
        }

        public Criteria andQualifiedStockLessThan(BigDecimal value) {
            addCriterion("qualified_stock <", value, "qualifiedStock");
            return (Criteria) this;
        }

        public Criteria andQualifiedStockLessThanOrEqualTo(BigDecimal value) {
            addCriterion("qualified_stock <=", value, "qualifiedStock");
            return (Criteria) this;
        }

        public Criteria andQualifiedStockIn(List<BigDecimal> values) {
            addCriterion("qualified_stock in", values, "qualifiedStock");
            return (Criteria) this;
        }

        public Criteria andQualifiedStockNotIn(List<BigDecimal> values) {
            addCriterion("qualified_stock not in", values, "qualifiedStock");
            return (Criteria) this;
        }

        public Criteria andQualifiedStockBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("qualified_stock between", value1, value2, "qualifiedStock");
            return (Criteria) this;
        }

        public Criteria andQualifiedStockNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("qualified_stock not between", value1, value2, "qualifiedStock");
            return (Criteria) this;
        }

        public Criteria andCheckStockIsNull() {
            addCriterion("check_stock is null");
            return (Criteria) this;
        }

        public Criteria andCheckStockIsNotNull() {
            addCriterion("check_stock is not null");
            return (Criteria) this;
        }

        public Criteria andCheckStockEqualTo(BigDecimal value) {
            addCriterion("check_stock =", value, "checkStock");
            return (Criteria) this;
        }

        public Criteria andCheckStockNotEqualTo(BigDecimal value) {
            addCriterion("check_stock <>", value, "checkStock");
            return (Criteria) this;
        }

        public Criteria andCheckStockGreaterThan(BigDecimal value) {
            addCriterion("check_stock >", value, "checkStock");
            return (Criteria) this;
        }

        public Criteria andCheckStockGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("check_stock >=", value, "checkStock");
            return (Criteria) this;
        }

        public Criteria andCheckStockLessThan(BigDecimal value) {
            addCriterion("check_stock <", value, "checkStock");
            return (Criteria) this;
        }

        public Criteria andCheckStockLessThanOrEqualTo(BigDecimal value) {
            addCriterion("check_stock <=", value, "checkStock");
            return (Criteria) this;
        }

        public Criteria andCheckStockIn(List<BigDecimal> values) {
            addCriterion("check_stock in", values, "checkStock");
            return (Criteria) this;
        }

        public Criteria andCheckStockNotIn(List<BigDecimal> values) {
            addCriterion("check_stock not in", values, "checkStock");
            return (Criteria) this;
        }

        public Criteria andCheckStockBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("check_stock between", value1, value2, "checkStock");
            return (Criteria) this;
        }

        public Criteria andCheckStockNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("check_stock not between", value1, value2, "checkStock");
            return (Criteria) this;
        }

        public Criteria andUnqualifiedStockIsNull() {
            addCriterion("unqualified_stock is null");
            return (Criteria) this;
        }

        public Criteria andUnqualifiedStockIsNotNull() {
            addCriterion("unqualified_stock is not null");
            return (Criteria) this;
        }

        public Criteria andUnqualifiedStockEqualTo(BigDecimal value) {
            addCriterion("unqualified_stock =", value, "unqualifiedStock");
            return (Criteria) this;
        }

        public Criteria andUnqualifiedStockNotEqualTo(BigDecimal value) {
            addCriterion("unqualified_stock <>", value, "unqualifiedStock");
            return (Criteria) this;
        }

        public Criteria andUnqualifiedStockGreaterThan(BigDecimal value) {
            addCriterion("unqualified_stock >", value, "unqualifiedStock");
            return (Criteria) this;
        }

        public Criteria andUnqualifiedStockGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("unqualified_stock >=", value, "unqualifiedStock");
            return (Criteria) this;
        }

        public Criteria andUnqualifiedStockLessThan(BigDecimal value) {
            addCriterion("unqualified_stock <", value, "unqualifiedStock");
            return (Criteria) this;
        }

        public Criteria andUnqualifiedStockLessThanOrEqualTo(BigDecimal value) {
            addCriterion("unqualified_stock <=", value, "unqualifiedStock");
            return (Criteria) this;
        }

        public Criteria andUnqualifiedStockIn(List<BigDecimal> values) {
            addCriterion("unqualified_stock in", values, "unqualifiedStock");
            return (Criteria) this;
        }

        public Criteria andUnqualifiedStockNotIn(List<BigDecimal> values) {
            addCriterion("unqualified_stock not in", values, "unqualifiedStock");
            return (Criteria) this;
        }

        public Criteria andUnqualifiedStockBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("unqualified_stock between", value1, value2, "unqualifiedStock");
            return (Criteria) this;
        }

        public Criteria andUnqualifiedStockNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("unqualified_stock not between", value1, value2, "unqualifiedStock");
            return (Criteria) this;
        }

        public Criteria andUnitIsNull() {
            addCriterion("unit is null");
            return (Criteria) this;
        }

        public Criteria andUnitIsNotNull() {
            addCriterion("unit is not null");
            return (Criteria) this;
        }

        public Criteria andUnitEqualTo(String value) {
            addCriterion("unit =", value, "unit");
            return (Criteria) this;
        }

        public Criteria andUnitNotEqualTo(String value) {
            addCriterion("unit <>", value, "unit");
            return (Criteria) this;
        }

        public Criteria andUnitGreaterThan(String value) {
            addCriterion("unit >", value, "unit");
            return (Criteria) this;
        }

        public Criteria andUnitGreaterThanOrEqualTo(String value) {
            addCriterion("unit >=", value, "unit");
            return (Criteria) this;
        }

        public Criteria andUnitLessThan(String value) {
            addCriterion("unit <", value, "unit");
            return (Criteria) this;
        }

        public Criteria andUnitLessThanOrEqualTo(String value) {
            addCriterion("unit <=", value, "unit");
            return (Criteria) this;
        }

        public Criteria andUnitLike(String value) {
            addCriterion("unit like", value, "unit");
            return (Criteria) this;
        }

        public Criteria andUnitNotLike(String value) {
            addCriterion("unit not like", value, "unit");
            return (Criteria) this;
        }

        public Criteria andUnitIn(List<String> values) {
            addCriterion("unit in", values, "unit");
            return (Criteria) this;
        }

        public Criteria andUnitNotIn(List<String> values) {
            addCriterion("unit not in", values, "unit");
            return (Criteria) this;
        }

        public Criteria andUnitBetween(String value1, String value2) {
            addCriterion("unit between", value1, value2, "unit");
            return (Criteria) this;
        }

        public Criteria andUnitNotBetween(String value1, String value2) {
            addCriterion("unit not between", value1, value2, "unit");
            return (Criteria) this;
        }

        public Criteria andExpireDateIsNull() {
            addCriterion("expire_date is null");
            return (Criteria) this;
        }

        public Criteria andExpireDateIsNotNull() {
            addCriterion("expire_date is not null");
            return (Criteria) this;
        }

        public Criteria andExpireDateEqualTo(Date value) {
            addCriterion("expire_date =", value, "expireDate");
            return (Criteria) this;
        }

        public Criteria andExpireDateNotEqualTo(Date value) {
            addCriterion("expire_date <>", value, "expireDate");
            return (Criteria) this;
        }

        public Criteria andExpireDateGreaterThan(Date value) {
            addCriterion("expire_date >", value, "expireDate");
            return (Criteria) this;
        }

        public Criteria andExpireDateGreaterThanOrEqualTo(Date value) {
            addCriterion("expire_date >=", value, "expireDate");
            return (Criteria) this;
        }

        public Criteria andExpireDateLessThan(Date value) {
            addCriterion("expire_date <", value, "expireDate");
            return (Criteria) this;
        }

        public Criteria andExpireDateLessThanOrEqualTo(Date value) {
            addCriterion("expire_date <=", value, "expireDate");
            return (Criteria) this;
        }

        public Criteria andExpireDateIn(List<Date> values) {
            addCriterion("expire_date in", values, "expireDate");
            return (Criteria) this;
        }

        public Criteria andExpireDateNotIn(List<Date> values) {
            addCriterion("expire_date not in", values, "expireDate");
            return (Criteria) this;
        }

        public Criteria andExpireDateBetween(Date value1, Date value2) {
            addCriterion("expire_date between", value1, value2, "expireDate");
            return (Criteria) this;
        }

        public Criteria andExpireDateNotBetween(Date value1, Date value2) {
            addCriterion("expire_date not between", value1, value2, "expireDate");
            return (Criteria) this;
        }

        public Criteria andProduceDateIsNull() {
            addCriterion("produce_date is null");
            return (Criteria) this;
        }

        public Criteria andProduceDateIsNotNull() {
            addCriterion("produce_date is not null");
            return (Criteria) this;
        }

        public Criteria andProduceDateEqualTo(Date value) {
            addCriterion("produce_date =", value, "produceDate");
            return (Criteria) this;
        }

        public Criteria andProduceDateNotEqualTo(Date value) {
            addCriterion("produce_date <>", value, "produceDate");
            return (Criteria) this;
        }

        public Criteria andProduceDateGreaterThan(Date value) {
            addCriterion("produce_date >", value, "produceDate");
            return (Criteria) this;
        }

        public Criteria andProduceDateGreaterThanOrEqualTo(Date value) {
            addCriterion("produce_date >=", value, "produceDate");
            return (Criteria) this;
        }

        public Criteria andProduceDateLessThan(Date value) {
            addCriterion("produce_date <", value, "produceDate");
            return (Criteria) this;
        }

        public Criteria andProduceDateLessThanOrEqualTo(Date value) {
            addCriterion("produce_date <=", value, "produceDate");
            return (Criteria) this;
        }

        public Criteria andProduceDateIn(List<Date> values) {
            addCriterion("produce_date in", values, "produceDate");
            return (Criteria) this;
        }

        public Criteria andProduceDateNotIn(List<Date> values) {
            addCriterion("produce_date not in", values, "produceDate");
            return (Criteria) this;
        }

        public Criteria andProduceDateBetween(Date value1, Date value2) {
            addCriterion("produce_date between", value1, value2, "produceDate");
            return (Criteria) this;
        }

        public Criteria andProduceDateNotBetween(Date value1, Date value2) {
            addCriterion("produce_date not between", value1, value2, "produceDate");
            return (Criteria) this;
        }

        public Criteria andSyncDateIsNull() {
            addCriterion("sync_date is null");
            return (Criteria) this;
        }

        public Criteria andSyncDateIsNotNull() {
            addCriterion("sync_date is not null");
            return (Criteria) this;
        }

        public Criteria andSyncDateEqualTo(Date value) {
            addCriterion("sync_date =", value, "syncDate");
            return (Criteria) this;
        }

        public Criteria andSyncDateNotEqualTo(Date value) {
            addCriterion("sync_date <>", value, "syncDate");
            return (Criteria) this;
        }

        public Criteria andSyncDateGreaterThan(Date value) {
            addCriterion("sync_date >", value, "syncDate");
            return (Criteria) this;
        }

        public Criteria andSyncDateGreaterThanOrEqualTo(Date value) {
            addCriterion("sync_date >=", value, "syncDate");
            return (Criteria) this;
        }

        public Criteria andSyncDateLessThan(Date value) {
            addCriterion("sync_date <", value, "syncDate");
            return (Criteria) this;
        }

        public Criteria andSyncDateLessThanOrEqualTo(Date value) {
            addCriterion("sync_date <=", value, "syncDate");
            return (Criteria) this;
        }

        public Criteria andSyncDateIn(List<Date> values) {
            addCriterion("sync_date in", values, "syncDate");
            return (Criteria) this;
        }

        public Criteria andSyncDateNotIn(List<Date> values) {
            addCriterion("sync_date not in", values, "syncDate");
            return (Criteria) this;
        }

        public Criteria andSyncDateBetween(Date value1, Date value2) {
            addCriterion("sync_date between", value1, value2, "syncDate");
            return (Criteria) this;
        }

        public Criteria andSyncDateNotBetween(Date value1, Date value2) {
            addCriterion("sync_date not between", value1, value2, "syncDate");
            return (Criteria) this;
        }

        public Criteria andExtendIsNull() {
            addCriterion("extend is null");
            return (Criteria) this;
        }

        public Criteria andExtendIsNotNull() {
            addCriterion("extend is not null");
            return (Criteria) this;
        }

        public Criteria andExtendEqualTo(String value) {
            addCriterion("extend =", value, "extend");
            return (Criteria) this;
        }

        public Criteria andExtendNotEqualTo(String value) {
            addCriterion("extend <>", value, "extend");
            return (Criteria) this;
        }

        public Criteria andExtendGreaterThan(String value) {
            addCriterion("extend >", value, "extend");
            return (Criteria) this;
        }

        public Criteria andExtendGreaterThanOrEqualTo(String value) {
            addCriterion("extend >=", value, "extend");
            return (Criteria) this;
        }

        public Criteria andExtendLessThan(String value) {
            addCriterion("extend <", value, "extend");
            return (Criteria) this;
        }

        public Criteria andExtendLessThanOrEqualTo(String value) {
            addCriterion("extend <=", value, "extend");
            return (Criteria) this;
        }

        public Criteria andExtendLike(String value) {
            addCriterion("extend like", value, "extend");
            return (Criteria) this;
        }

        public Criteria andExtendNotLike(String value) {
            addCriterion("extend not like", value, "extend");
            return (Criteria) this;
        }

        public Criteria andExtendIn(List<String> values) {
            addCriterion("extend in", values, "extend");
            return (Criteria) this;
        }

        public Criteria andExtendNotIn(List<String> values) {
            addCriterion("extend not in", values, "extend");
            return (Criteria) this;
        }

        public Criteria andExtendBetween(String value1, String value2) {
            addCriterion("extend between", value1, value2, "extend");
            return (Criteria) this;
        }

        public Criteria andExtendNotBetween(String value1, String value2) {
            addCriterion("extend not between", value1, value2, "extend");
            return (Criteria) this;
        }

        public Criteria andGmtCreateIsNull() {
            addCriterion("gmt_create is null");
            return (Criteria) this;
        }

        public Criteria andGmtCreateIsNotNull() {
            addCriterion("gmt_create is not null");
            return (Criteria) this;
        }

        public Criteria andGmtCreateEqualTo(Date value) {
            addCriterion("gmt_create =", value, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateNotEqualTo(Date value) {
            addCriterion("gmt_create <>", value, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateGreaterThan(Date value) {
            addCriterion("gmt_create >", value, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateGreaterThanOrEqualTo(Date value) {
            addCriterion("gmt_create >=", value, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateLessThan(Date value) {
            addCriterion("gmt_create <", value, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateLessThanOrEqualTo(Date value) {
            addCriterion("gmt_create <=", value, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateIn(List<Date> values) {
            addCriterion("gmt_create in", values, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateNotIn(List<Date> values) {
            addCriterion("gmt_create not in", values, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateBetween(Date value1, Date value2) {
            addCriterion("gmt_create between", value1, value2, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateNotBetween(Date value1, Date value2) {
            addCriterion("gmt_create not between", value1, value2, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtUpdateIsNull() {
            addCriterion("gmt_update is null");
            return (Criteria) this;
        }

        public Criteria andGmtUpdateIsNotNull() {
            addCriterion("gmt_update is not null");
            return (Criteria) this;
        }

        public Criteria andGmtUpdateEqualTo(Date value) {
            addCriterion("gmt_update =", value, "gmtUpdate");
            return (Criteria) this;
        }

        public Criteria andGmtUpdateNotEqualTo(Date value) {
            addCriterion("gmt_update <>", value, "gmtUpdate");
            return (Criteria) this;
        }

        public Criteria andGmtUpdateGreaterThan(Date value) {
            addCriterion("gmt_update >", value, "gmtUpdate");
            return (Criteria) this;
        }

        public Criteria andGmtUpdateGreaterThanOrEqualTo(Date value) {
            addCriterion("gmt_update >=", value, "gmtUpdate");
            return (Criteria) this;
        }

        public Criteria andGmtUpdateLessThan(Date value) {
            addCriterion("gmt_update <", value, "gmtUpdate");
            return (Criteria) this;
        }

        public Criteria andGmtUpdateLessThanOrEqualTo(Date value) {
            addCriterion("gmt_update <=", value, "gmtUpdate");
            return (Criteria) this;
        }

        public Criteria andGmtUpdateIn(List<Date> values) {
            addCriterion("gmt_update in", values, "gmtUpdate");
            return (Criteria) this;
        }

        public Criteria andGmtUpdateNotIn(List<Date> values) {
            addCriterion("gmt_update not in", values, "gmtUpdate");
            return (Criteria) this;
        }

        public Criteria andGmtUpdateBetween(Date value1, Date value2) {
            addCriterion("gmt_update between", value1, value2, "gmtUpdate");
            return (Criteria) this;
        }

        public Criteria andGmtUpdateNotBetween(Date value1, Date value2) {
            addCriterion("gmt_update not between", value1, value2, "gmtUpdate");
            return (Criteria) this;
        }
    }

    public static class Criteria extends GeneratedCriteria {

        protected Criteria() {
            super();
        }
    }

    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}
