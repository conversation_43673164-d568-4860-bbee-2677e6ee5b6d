package com.cowell.bam.domain;

import com.cowell.bam.service.dto.CmallStockRequestDTO;
import com.cowell.bam.service.dto.StockGoodsCountInfo;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * bam
 * 2020/9/15 14:23
 * 库存对比上下文
 *
 * <AUTHOR>
 * @since
 **/
@Data
public class CompareContext implements Serializable {


    /**
     * 连锁ID
     */
    private Long businessId;
    /**
     * 门店ID
     */
    private Long storeId;

    /**
     * 同步批次库存列表
     */
    private List<CmallStockRequestDTO> stockRequestList;

    /**
     * 中台库存列表
     */
    private List<StockGoodsCountInfo> goodsCountList;


    /**
     * 是否存在差异
     */
    private boolean diff;


}
