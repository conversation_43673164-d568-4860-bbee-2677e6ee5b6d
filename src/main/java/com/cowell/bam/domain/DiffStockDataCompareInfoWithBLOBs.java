package com.cowell.bam.domain;

import java.io.Serializable;

public class DiffStockDataCompareInfoWithBLOBs extends DiffStockDataCompareInfo implements Serializable {
    private String batchCodeData;

    private String batchCostData;

    private static final long serialVersionUID = 1L;

    public String getBatchCodeData() {
        return batchCodeData;
    }

    public void setBatchCodeData(String batchCodeData) {
        this.batchCodeData = batchCodeData == null ? null : batchCodeData.trim();
    }

    public String getBatchCostData() {
        return batchCostData;
    }

    public void setBatchCostData(String batchCostData) {
        this.batchCostData = batchCostData == null ? null : batchCostData.trim();
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", batchCodeData=").append(batchCodeData);
        sb.append(", batchCostData=").append(batchCostData);
        sb.append(", serialVersionUID=").append(serialVersionUID);
        sb.append("]");
        return sb.toString();
    }
}
