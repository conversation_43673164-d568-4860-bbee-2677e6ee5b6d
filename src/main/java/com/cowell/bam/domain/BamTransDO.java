package com.cowell.bam.domain;

public class BamTransDO {

    /**
     * 单据日期
     */
    private String businessDate;
    /**
     * 发送方系统名称
     */
    private String sendSysName;
    /**
     * 接收方系统名称
     */
    private String receiveSysName;
    /**
     * 动作描述
     */
    private String actionName;

    /**
     * 状态
     */
    private String status;

    public String getBusinessDate() {
        return businessDate;
    }

    public void setBusinessDate(String businessDate) {
        this.businessDate = businessDate;
    }

    public String getSendSysName() {
        return sendSysName;
    }

    public void setSendSysName(String sendSysName) {
        this.sendSysName = sendSysName;
    }

    public String getReceiveSysName() {
        return receiveSysName;
    }

    public void setReceiveSysName(String receiveSysName) {
        this.receiveSysName = receiveSysName;
    }

    public String getActionName() {
        return actionName;
    }

    public void setActionName(String actionName) {
        this.actionName = actionName;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }


    @Override
    public String toString() {
        return "BamTransDO{" +
            "businessDate='" + businessDate + '\'' +
            ", sendSysName='" + sendSysName + '\'' +
            ", receiveSysName='" + receiveSysName + '\'' +
            ", actionName='" + actionName + '\'' +
            ", status='" + status + '\'' +
            '}';
    }
}
