package com.cowell.bam.domain;

import com.alibaba.fastjson.annotation.JSONField;

public class HanaBdpDO {
    private String id;
    /**
     * 主键	string	32	表的主键
     */
    @JSONField(name = "ZGUID")
    private String zGuId;
    /**
     * 业务单据唯一ID	string	50个英文	业务单据ID 例如：1812170005010
     */
    @JSONField(name = "ZTRACEID")
    private String zTraceId;
    /**
     * 流程编码	int		例：10
     */
    @JSONField(name = "ZFLOWID")
    private Short zFlowId;
    /**
     * 	步骤编码	int		例：11
     */
    @JSONField(name = "ZSTEPID")
    private Short zStepId;
    /**
     * 发送系统编码	string	10个英文	取值SAP、POS、WMS
     */
    @JSONField(name = "ZSENDSYSID")
    private String zSendsysId;
    /**
     * 接收系统编码	string	10个英文	取值SAP、POS、WMS
     */
    @JSONField(name = "ZRECSYSID")
    private String zRecsysId;
    /**
     * 公司编码	string	10个英文
     */
    @JSONField(name = "ZCOMID")
    private String zComId;
    /**
     * 门店编码	string	10个英文
     */
    @JSONField(name = "ZSTOREID")
    private String zStoreId;
    /**
     * 发送时间戳	Dec	14位整数，6位小数	例：20181213000000.000000
     */
    @JSONField(name = "ZTIMESTAMP")
    private String zTimeStamp;
    /**
     * 单据日期	Dec	14位整数，6位小数	20181213000000.000000取东八区系统时间
     */
    @JSONField(name = "ZBUSINESSDATE")
    private String zBusinessDate;
    /**
     * 明细条目数	int		例：100
     */
    @JSONField(name = "ZITEMCOUNT")
    private Short zItemCount;
    /**
     * 	状态	int		0失败,1成功
     */
    @JSONField(name = "ZSTATUS")
    private Short zStatus;
    /**
     * 消息	string	500个英文	成功记录Success,失败记录原因。超长截断。
     */
    @JSONField(name = "ZMESSAG")
    private String zMessag;
    /**
     * 备用字段	string	100个英文
     */
    @JSONField(name = "ZEXTEND1")
    private String zExtend1;
    /**
     * 备用字段	string	100个英文
     */
    @JSONField(name = "ZEXTEND2")
    private String zExtend2;
    /**
     * 备用字段	string	500个英文
     */
    @JSONField(name = "ZEXTEND3")
    private String zExtend3;
    /**
     * 备用字段	string	500个英文
     */
    @JSONField(name = "ZEXTEND4")
    private String zExtend4;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getzGuId() {
        return zGuId;
    }

    public void setzGuId(String zGuId) {
        this.zGuId = zGuId;
    }

    public String getzTraceId() {
        return zTraceId;
    }

    public void setzTraceId(String zTraceId) {
        this.zTraceId = zTraceId;
    }

    public Short getzFlowId() {
        return zFlowId;
    }

    public void setzFlowId(Short zFlowId) {
        this.zFlowId = zFlowId;
    }

    public Short getzStepId() {
        return zStepId;
    }

    public void setzStepId(Short zStepId) {
        this.zStepId = zStepId;
    }

    public String getzSendsysId() {
        return zSendsysId;
    }

    public void setzSendsysId(String zSendsysId) {
        this.zSendsysId = zSendsysId;
    }

    public String getzRecsysId() {
        return zRecsysId;
    }

    public void setzRecsysId(String zRecsysId) {
        this.zRecsysId = zRecsysId;
    }

    public String getzComId() {
        return zComId;
    }

    public void setzComId(String zComId) {
        this.zComId = zComId;
    }

    public String getzStoreId() {
        return zStoreId;
    }

    public void setzStoreId(String zStoreId) {
        this.zStoreId = zStoreId;
    }

    public String getzTimeStamp() {
        return zTimeStamp;
    }

    public void setzTimeStamp(String zTimeStamp) {
        this.zTimeStamp = zTimeStamp;
    }

    public String getzBusinessDate() {
        return zBusinessDate;
    }

    public void setzBusinessDate(String zBusinessDate) {
        this.zBusinessDate = zBusinessDate;
    }

    public Short getzItemCount() {
        return zItemCount;
    }

    public void setzItemCount(Short zItemCount) {
        this.zItemCount = zItemCount;
    }

    public Short getzStatus() {
        return zStatus;
    }

    public void setzStatus(Short zStatus) {
        this.zStatus = zStatus;
    }

    public String getzMessag() {
        return zMessag;
    }

    public void setzMessag(String zMessag) {
        this.zMessag = zMessag;
    }

    public String getzExtend1() {
        return zExtend1;
    }

    public void setzExtend1(String zExtend1) {
        this.zExtend1 = zExtend1;
    }

    public String getzExtend2() {
        return zExtend2;
    }

    public void setzExtend2(String zExtend2) {
        this.zExtend2 = zExtend2;
    }

    public String getzExtend3() {
        return zExtend3;
    }

    public void setzExtend3(String zExtend3) {
        this.zExtend3 = zExtend3;
    }

    public String getzExtend4() {
        return zExtend4;
    }

    public void setzExtend4(String zExtend4) {
        this.zExtend4 = zExtend4;
    }
}
