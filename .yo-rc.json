{"generator-jhipster": {"promptValues": {"packageName": "com.cowell.bam", "nativeLanguage": "zh-cn"}, "jhipsterVersion": "5.1.0", "applicationType": "microservice", "baseName": "bam", "packageName": "com.cowell.bam", "packageFolder": "com/cowell/bam", "serverPort": "8081", "authenticationType": "uaa", "uaaBaseName": "uaa", "cacheProvider": "hazelcast", "enableHibernateCache": false, "websocket": false, "databaseType": "sql", "devDatabaseType": "h2Disk", "prodDatabaseType": "mysql", "searchEngine": false, "messageBroker": false, "serviceDiscoveryType": "eureka", "buildTool": "maven", "enableSwaggerCodegen": false, "jwtSecretKey": "7b929447ca31fe29cb06f73d0ffe4469a79dd972", "enableTranslation": true, "testFrameworks": [], "jhiPrefix": "jhi", "clientPackageManager": "npm", "nativeLanguage": "zh-cn", "languages": ["zh-cn"], "skipClient": true, "skipUserManagement": true}}